import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/widgets/custom_text.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../domain/entities/product_entity.dart';

/// A horizontal scrollable variant selector widget similar to Zepto's design
/// Displays product variants in equal-sized rectangular containers
class ProductVariantSelector extends StatelessWidget {
  final List<ProductEntity> variants;
  final ProductEntity? selectedVariant;
  final Function(ProductEntity) onVariantSelected;
  final bool isLoading;

  const ProductVariantSelector({
    super.key,
    required this.variants,
    required this.selectedVariant,
    required this.onVariantSelected,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading || variants.isEmpty) {
      return const SizedBox.shrink();
    }

    return SingleChildScrollView(
  scrollDirection: Axis.horizontal,
  child: Row(
    children: List.generate(variants.length, (index) {
      final variant = variants[index];
      final isSelected = selectedVariant?.id == variant.id;

      return Padding(
        padding: EdgeInsets.only(right: index < variants.length - 1 ? 12 : 0),
        child: _buildVariantOption(
          context,
          variant,
          isSelected,
        ),
      );
    }),
  ),
);

  }

 Widget _buildVariantOption(
  BuildContext context,
  ProductEntity variant,
  bool isSelected,
) {
  final hasOffer = (variant.discountPercentage ?? 0) > 0;

  return GestureDetector(
    behavior: HitTestBehavior.opaque, // ensures taps are always registered
    onTap: () {
      HapticFeedback.lightImpact();
      onVariantSelected(variant);
    },
    child: AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      width: 120, // fixed width for consistent cards
      decoration: BoxDecoration(
        color: isSelected ? AppColors.green100 : AppColors.neutral100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppColors.green700 : AppColors.neutral200,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (hasOffer)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.blue100, // light blue tint
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: CustomText(
                '${variant.discountPercentage.toStringAsFixed(0)}% OFF',
                textAlign: TextAlign.center,
                  fontSize: 10,
                  fontWeight: FontWeight.w700,
                  color: AppColors.blue600,
             
              ),
            ),

          const SizedBox(height: 6),

          /// 🔹 Variant Name
          CustomText(
            variant.variantName ?? variant.name,
            fontSize: isSelected ? 14 : 10,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: AppColors.neutral600,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 4),

          /// 🔹 Price Row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                '₹${variant.price.round()}',
                color: AppColors.neutral700,
                fontSize: isSelected ? 16 : 10,
                fontWeight: FontWeight.w700,
              ),
              const SizedBox(width: 6),
              if ((variant.originalPrice ?? 0) > (variant.price))
                CustomText(
                  "₹${variant.originalPrice?.round() ?? 0}",
                  color: AppColors.neutral400,
                  fontSize: isSelected ? 12 : 10,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.lineThrough,
                ),
            ],
          ),

          const SizedBox(height: 6),
        ],
      ),
    ),
  );
}
}