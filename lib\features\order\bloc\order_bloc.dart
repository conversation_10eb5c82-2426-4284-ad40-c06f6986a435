import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/utils/notifier.dart';
import 'package:rozana/core/services/analytics_events/order_analytics_events.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/domain/usecases/get_order_history_usecase.dart';
import 'package:rozana/domain/usecases/get_order_details_usecase.dart';
import 'package:rozana/domain/usecases/cancel_order_usecase.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/domain/usecases/return_order_usecase.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'order_event.dart';
import 'order_state.dart';

export 'order_event.dart';
export 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final GetOrderHistoryUseCase _getOrderHistoryUseCase;
  final GetOrderDetailsUseCase _getOrderDetailsUseCase;
  final CancelOrderUseCase _cancelOrderUseCase;
  final ReturnOrderUseCase _returnOrderUseCase;

  // Pagination and filtering state
  String _currentCustomerId = 'customer_123';
  String _currentSearchQuery = '';
  int _currentPage = 0;
  List<OrderEntity> _allOrders = [];
  bool _hasMoreData = true;

  OrderBloc({
    required GetOrderHistoryUseCase getOrderHistoryUseCase,
    required GetOrderDetailsUseCase getOrderDetailsUseCase,
    required CancelOrderUseCase cancelOrderUseCase,
    required ReturnOrderUseCase returnOrderUseCase,
  })  : _getOrderHistoryUseCase = getOrderHistoryUseCase,
        _getOrderDetailsUseCase = getOrderDetailsUseCase,
        _cancelOrderUseCase = cancelOrderUseCase,
        _returnOrderUseCase = returnOrderUseCase,
        super(const OrderState.initial()) {
    on<OrderInit>(_onInit);
    on<LoadOrderHistory>(_onLoadOrderHistory);
    on<LoadMoreOrders>(_onLoadMoreOrders);
    on<LoadOrderDetails>(_onLoadOrderDetails);
    on<RefreshOrderHistory>(_onRefreshOrderHistory);
    on<CancelOrder>(_onCancelOrder);
    on<ClearOrderDetails>(_onClearOrderDetails);
    on<SearchOrders>(_onSearchOrders);
    on<ResetOrder>(_onReset);
    on<ReturnOrder>(_onReturnOrder);
    on<OrderAgain>(_onOrderAgain);
  }

  Future<void> _onInit(OrderInit event, Emitter<OrderState> emit) async {
    if (getIt<AppBloc>().isAuthenticated) {
      emit(const OrderState.loading());
      add(const OrderEvent.loadOrderHistory());
    }
  }

  Future<void> _onLoadOrderHistory(
    LoadOrderHistory event,
    Emitter<OrderState> emit,
  ) async {
    try {
      if (!getIt<AppBloc>().isAuthenticated) {
        return;
      }
      if (event.page == 0) {
        emit(const OrderState.loading());
        _allOrders.clear();
        _currentPage = 0;
        _hasMoreData = true;
      }

      _currentCustomerId = event.customerId;

      final orders = await _getOrderHistoryUseCase.call(
        status: '', // Always load all orders
        page: event.page,
        pageSize: event.pageSize,
        refresh: event.refresh,
      );

      if (event.page == 0) {
        _allOrders = orders;
      } else {
        _allOrders.addAll(orders);
      }

      _currentPage = event.page;
      _hasMoreData = orders.length >= event.pageSize;

      if (orders.isEmpty) {
        emit(OrderState.empty(
          searchQuery: _currentSearchQuery,
        ));
        return;
      }
      emit(OrderState.orderHistoryLoaded(
        orders: List.from(_allOrders),
        isLoadingMore: false,
        hasMoreData: _hasMoreData,
        currentPage: _currentPage,
        searchQuery: _currentSearchQuery,
      ));
    } catch (e) {
      emit(OrderState.error(message: e.toString()));
    }
  }

  Future<void> _onLoadMoreOrders(
    LoadMoreOrders event,
    Emitter<OrderState> emit,
  ) async {
    if (!_hasMoreData || state.isLoadingMore) return;

    try {
      // Update state to show loading more
      if (state is OrderHistoryLoaded) {
        final currentState = state as OrderHistoryLoaded;
        emit(currentState.copyWith(isLoadingMore: true));
      }

      final nextPage = _currentPage + 1;

      // Load more orders directly instead of adding a new event
      final orders = await _getOrderHistoryUseCase.call(
        status: '', // Always load all orders
        page: nextPage,
        pageSize: 10,
        refresh: false,
      );

      // Update pagination state
      _currentPage = nextPage;
      _hasMoreData = orders.length >= 10; // pageSize

      // Add new orders to existing list
      if (orders.isNotEmpty) {
        _allOrders.addAll(orders);

        emit(OrderState.orderHistoryLoaded(
          orders: List.from(_allOrders),
          isLoadingMore: false,
          hasMoreData: _hasMoreData,
          currentPage: _currentPage,
          searchQuery: _currentSearchQuery,
        ));
      } else {
        // No more orders to load
        _hasMoreData = false;

        if (_allOrders.isEmpty) {
          emit(OrderState.empty(
            searchQuery: _currentSearchQuery,
          ));
        } else {
          // Just update hasMoreData flag
          emit(OrderState.orderHistoryLoaded(
            orders: List.from(_allOrders),
            isLoadingMore: false,
            hasMoreData: false,
            currentPage: _currentPage,
            searchQuery: _currentSearchQuery,
          ));
        }
      }
    } catch (e) {
      emit(OrderState.error(message: e.toString()));
    }
  }

  Future<void> _onLoadOrderDetails(
    LoadOrderDetails event,
    Emitter<OrderState> emit,
  ) async {
    try {
      if (event.showLoader) {
        emit(const OrderState.loading());
      }

      final order = await _getOrderDetailsUseCase.call(event.orderId);

      if (order == null) {
        ///
        emit(OrderState.error(
          message: 'Order not found',
          orderId: event.orderId,
        ));
        return;
      }
      emit(OrderState.orderDetailsLoaded(order: order));
    } catch (e) {
      emit(OrderState.error(
        message: e.toString(),
        orderId: event.orderId,
      ));
    }
  }

  Future<void> _onRefreshOrderHistory(
    RefreshOrderHistory event,
    Emitter<OrderState> emit,
  ) async {
    add(OrderEvent.loadOrderHistory(
      customerId: _currentCustomerId,
      status: '', // Always load all orders
      page: 0,
      pageSize: 10,
      refresh: true,
    ));
  }

  Future<void> _onCancelOrder(
    CancelOrder event,
    Emitter<OrderState> emit,
  ) async {
    try {
      // Get order details for analytics
      final order = await _getOrderDetailsUseCase.call(event.orderId);
      
      // Track order cancel selected analytics
      if (order != null) {
        OrderAnalyticsEvents().trackOrderCancelSelected(
          numberOfProducts: order.items.length,
          itemsTotal: order.totalAmount.toString(),
          deliveryCharge: order.deliveryFee.toString(),
          tax: order.tax.toString(),
          discount: order.discount.toString(),
          grandTotal: order.totalAmount.toString(),
          freeProductsAdded: 0, // This would need to be calculated from order data
          paymentMethodSelected: order.paymentMethod,
          paymentStatus: 'Unknown', // OrderEntity doesn't have paymentStatus
          orderStatus: order.status,
          orderId: event.orderId,
        );
      }
      
      final success = await _cancelOrderUseCase.call(event.orderId);
      if (success) {
        emit(OrderState.orderCancelled(
          orderId: event.orderId,
          message: 'Order cancelled successfully',
        ));
      } else {
        emit(OrderState.error(
          message: 'Failed to cancel order',
          orderId: event.orderId,
        ));
      }
    } catch (e) {
      emit(OrderState.error(
        message: e.toString(),
        orderId: event.orderId,
      ));
    }
  }

  Future<void> _onReturnOrder(
    ReturnOrder event,
    Emitter<OrderState> emit,
  ) async {
    try {
      await _returnOrderUseCase.call(event.orderId, event.item, event.reason);
      add(OrderEvent.loadOrderDetails(event.orderId, showLoader: false));
    } catch (e) {
      emit(OrderState.error(
        message: e.toString(),
        orderId: event.orderId,
      ));
    }
  }

  Future<void> _onClearOrderDetails(
    ClearOrderDetails event,
    Emitter<OrderState> emit,
  ) async {
    if (_allOrders.isNotEmpty) {
      emit(OrderState.orderHistoryLoaded(
        orders: _allOrders,
        isLoadingMore: false,
        hasMoreData: _hasMoreData,
        currentPage: _currentPage,
        searchQuery: _currentSearchQuery,
      ));
    } else {
      emit(const OrderState.initial());
    }
  }

  Future<void> _onSearchOrders(
    SearchOrders event,
    Emitter<OrderState> emit,
  ) async {
    _currentSearchQuery = event.query;

    if (event.query.isEmpty) {
      // If search query is empty, show all orders
      add(const OrderEvent.refreshOrderHistory());
      return;
    }

    try {
      // Filter existing orders by search query
      final filteredOrders = _allOrders.where((order) {
        final query = event.query.toLowerCase();
        return order.id.toLowerCase().contains(query) ||
            order.items.any((item) => item.name.toLowerCase().contains(query));
      }).toList();

      if (filteredOrders.isEmpty) {
        emit(OrderState.empty(
          searchQuery: _currentSearchQuery,
        ));
      } else {
        emit(OrderState.orderHistoryLoaded(
          orders: filteredOrders,
          isLoadingMore: false,
          hasMoreData: false, // No pagination for search results
          currentPage: 0,
          searchQuery: _currentSearchQuery,
        ));
      }
    } catch (e) {
      emit(OrderState.error(message: e.toString()));
    }
  }

  Future<void> _onReset(
    ResetOrder event,
    Emitter<OrderState> emit,
  ) async {
    _allOrders.clear();
    _currentPage = 0;
    _currentSearchQuery = '';
    _hasMoreData = true;
    emit(const OrderState.initial());
  }

  Future<void> _onOrderAgain(
    OrderAgain event,
    Emitter<OrderState> emit,
  ) async {
    try {
      // Track buy again page landed
      OrderAnalyticsEvents().trackBuyAgainPageLanded();
      
      // First check if we have historyItems, if not, convert items to the format needed
      List<Map<String, dynamic>> itemsToProcess = [];

      if (event.order.historyItems.isNotEmpty) {
        itemsToProcess = event.order.historyItems;
      } else if (event.order.items.isNotEmpty) {
        // Convert OrderItemEntity to the format expected by getSKUDataFromTS
        itemsToProcess = event.order.items
            .map((item) => ({
                  'child_sku': item.skuID,
                  'sku': item.skuID,
                  'quantity': item.quantity,
                  'name': item.name,
                }))
            .toList();
      } else {
        getIt<AppNotifier>().showSnackBar('No items found in this order');
        emit(const OrderState.error(message: 'No items found in this order'));
        return;
      }

      // Get repository instance
      final orderRepository = getIt<OrderRepositoryInterface>();

      // Get SKU data from TypeSense
      final products = await orderRepository.getSKUDataFromTS(itemsToProcess);
      if (products.isEmpty) {
        getIt<AppNotifier>()
            .showSnackBar('Could not load products information');
        emit(const OrderState.error(
            message: 'Could not load products information'));
        return;
      }

      // Get cart bloc
      final cartBloc = getIt<CartBloc>();
      cartBloc.add(const CartEvent.clear());

      // Create a list to track out-of-stock items
      final List<String> outOfStockItems = [];
      int addedCount = 0;

      // First pass: Check stock for all items
      final List<CartItemModel> itemsToAdd = [];

      for (var i = 0; i < products.length; i++) {
        final product = products[i];
        if (product is ProductModel &&
            product.skuID != null &&
            product.name != null &&
            product.price != null) {
          // Find quantity from original order
          final originalItem = itemsToProcess.firstWhere(
            (item) => item['child_sku'] == product.skuID,
            orElse: () => {'quantity': 1, 'name': product.name ?? 'Item'},
          );

          // Check stock
          if ((product.availableQty ?? 0) < 1) {
            outOfStockItems.add(originalItem['name'] ?? 'Item');
            continue;
          }

          // Create cart item
          final cartItem = CartItemModel(
            productId: product.id,
            name: product.name,
            variantName: product.variantName,
            price: product.price,
            imageUrl: product.imageUrl,
            quantity: originalItem['quantity'],
            unit: 'unit',
            discountedPrice: product.price,
            facilityId: product.facilityId,
            facilityName: product.facilityName,
            skuID: product.skuID,
            availableQuantity: product.availableQty,
            maxQuantity: product.maxLimit,
            tax: product.tax,
            cgst: product.cgst,
            sgst: product.sgst,
            whSku: product.whSku,
            packUomQuantity: product.packUomQuantity,
            source: product.source,
          );
          itemsToAdd.add(cartItem);
        }
      }

      // Show out of stock message if any
      if (outOfStockItems.isNotEmpty) {
        final message = outOfStockItems.length == 1
            ? '${outOfStockItems.first} is currently unavailable and couldn\'t be added to your cart.'
            : '${outOfStockItems.length} Items are currently unavailable and couldn\'t be added to your cart.';

        getIt<AppNotifier>().showSnackBar(message);
      }

      // Add all in-stock items to cart
      for (final cartItem in itemsToAdd) {
        cartBloc.add(CartEvent.addItem(item: cartItem));
        addedCount++;
        if (cartItem != itemsToAdd.last) {
          await Future.delayed(const Duration(milliseconds: 300));
        }
      }

      if (addedCount > 0) {
        emit(OrderState.itemsAddedToCart(
          itemCount: addedCount,
          navigateToCart: true,
        ));
      }
    } catch (e) {
      emit(OrderState.error(message: 'Error: ${e.toString()}'));
    }
  }
}
