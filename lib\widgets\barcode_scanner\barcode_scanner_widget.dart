import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'scanner_overlay.dart';

/// A modern and attractive barcode scanner widget that can be called on demand
/// and returns the scanned barcode data.
class BarcodeScannerWidget extends StatefulWidget {
  /// Callback function that is called when a barcode is detected
  final Function(String) onBarcodeDetected;

  /// Optional title to display at the top of the scanner
  final String? title;

  /// Optional description text to display below the title
  final String? description;

  /// Optional custom overlay color
  final Color? overlayColor;

  /// Optional custom border color for the scanner frame
  final Color? borderColor;

  /// Optional custom success color when barcode is detected
  final Color? successColor;

  const BarcodeScannerWidget({
    super.key,
    required this.onBarcodeDetected,
    this.title,
    this.description,
    this.overlayColor,
    this.borderColor,
    this.successColor,
  });

  @override
  State<BarcodeScannerWidget> createState() => _BarcodeScannerWidgetState();
}

class _BarcodeScannerWidgetState extends State<BarcodeScannerWidget>
    with SingleTickerProviderStateMixin {
  late MobileScannerController _scannerController;
  bool _hasScanned = false;
  String? _lastScanned;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _scannerController = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      facing: CameraFacing.back,
      torchEnabled: false,
    );

    // Animation for success feedback
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _animation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  @override
  void dispose() {
    _scannerController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onDetect(BarcodeCapture capture) {
    if (_hasScanned || capture.barcodes.isEmpty) return;

    final barcode = capture.barcodes.first;
    final rawValue = barcode.rawValue;

    if (rawValue != null && rawValue != _lastScanned) {
      setState(() {
        _hasScanned = true;
        _lastScanned = rawValue;
      });

      // Play success animation
      _animationController.forward().then((_) {
        _animationController.reverse();
      });

      // Vibrate for feedback
      HapticFeedback.mediumImpact();

      // Call the callback with the scanned value
      widget.onBarcodeDetected(rawValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    final defaultOverlayColor = Colors.black.withValues(alpha: 0.5);
    final defaultBorderColor = Colors.white;
    final defaultSuccessColor = Colors.green;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          widget.title ?? 'Scan Barcode',
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: Stack(
        children: [
          MobileScanner(
            controller: _scannerController,
            onDetect: _onDetect,
          ),
          ScannerOverlay(
            overlayColor: widget.overlayColor ?? defaultOverlayColor,
            borderColor: _hasScanned
                ? (widget.successColor ?? defaultSuccessColor)
                : (widget.borderColor ?? defaultBorderColor),
            scanAnimationController: _animationController,
            animation: _animation,
          ),
          if (widget.description != null)
            Positioned(
              bottom: 100,
              left: 24,
              right: 24,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  widget.description!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          if (_hasScanned)
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.7),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.7,
                      height: MediaQuery.of(context).size.height * 0.7,
                      child: Lottie.asset(
                        'assets/lotties/scan-success.json',
                        repeat: false,
                        fit: BoxFit.contain,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Scan Successful',
                      style: TextStyle(
                        color: widget.successColor ?? defaultSuccessColor,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
