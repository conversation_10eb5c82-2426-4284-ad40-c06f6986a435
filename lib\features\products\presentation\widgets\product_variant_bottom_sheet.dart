import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/domain/entities/product_entity.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/bottom_action_button.dart';

import '../../../../widgets/shimmer_widgets.dart';

class ProductVariantBottomSheet extends StatelessWidget {
  final String productTitle;
  final List<ProductEntity> variants;
  final bool isLoading;

  const ProductVariantBottomSheet({
    super.key,
    required this.productTitle,
    required this.variants,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: GestureDetector(
          onTap: () {
            context.pop();
          },
        ),
        bottomSheet: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.75,
          ),
          child: NotificationListener<UserScrollNotification>(
            onNotification: (notification) {
              return false;
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                Flexible(
                  child: ColoredBox(
                    color: AppColors.neutral150,
                    child: ListView.separated(
                      shrinkWrap: true,
                      primary: true,
                      physics: ClampingScrollPhysics(),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 20),
                      itemCount: variants.length,
                      itemBuilder: (context, index) => _buildVariantItem(index),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 12),
                    ),
                  ),
                ),
                _buildDoneButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: AppColors.neutral100,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomText(
              productTitle,
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppColors.neutral800,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
              child: Icon(Icons.close, color: AppColors.neutral600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVariantItem(
    int index,
  ) {
    final variant = variants[index];
    return AbsorbPointer(
      absorbing: (variant.availableQty ?? 0) < 1,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Visibility(
              visible: !isLoading,
              replacement: CustomShimmer(
                radius: 16,
                width: 56,
                height: 56,
                baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                highlightColor: Color(0xFFD8D5EA),
              ),
              child: CustomImage(
                imageUrl: variant.imageUrl ?? '',
                width: 56,
                height: 56,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 3,
              child: Visibility(
                visible: !isLoading,
                replacement: CustomShimmer(
                  height: 16,
                  baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                  highlightColor: Color(0xFFD8D5EA),
                ),
                child: CustomText(
                  variant.variantName ?? variant.name,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.neutral600,
                ),
              ),
            ),
            SizedBox(width: 12),
            Visibility(
              visible: !isLoading,
              replacement: SizedBox(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "₹${variant.price.toStringAsFixed(0)}",
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 6),
                  if (variant.originalPrice != null &&
                      variant.originalPrice! > variant.price)
                    Text(
                      "₹${variant.originalPrice!.toStringAsFixed(0)}",
                      style: TextStyle(
                        fontSize: 12,
                        decoration: TextDecoration.lineThrough,
                        color: AppColors.neutral400,
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(width: 12),
            ConstrainedBox(
              constraints: BoxConstraints(
                  maxWidth: (variant.availableQty ?? 0) > 0 ? 80 : 96),
              child: Visibility(
                visible: !isLoading,
                replacement: CustomShimmer(
                  radius: 12,
                  height: 34,
                  baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                  highlightColor: Color(0xFFD8D5EA),
                ),
                child: Visibility(
                  visible: (variant.availableQty ?? 0) > 0,
                  replacement: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: CustomText(
                      'Out of stock',
                      color: AppColors.red600,
                      fontWeight: FontWeight.w700,
                      maxLines: 2,
                      fontSize: 14,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  child: QuantityTogglerWidget(
                    height: 34,
                    product: variant,
                    showVariantsBottomSheet: false,
                    selectedProduct: variant,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDoneButton(BuildContext context) {
    return BottomActionButton(
      text: 'Done',
      fontSize: 16.sp,
      onPressed: () => Navigator.pop(context),
    );
  }
}
