import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../widgets/custom_text.dart';
import '../../../products/presentation/widgets/product_card.dart';
import '../../bloc/home bloc/home_bloc.dart';

class ONDCProductSection extends StatelessWidget {
  const ONDCProductSection({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (previous, current) {
          if (previous is! HomeLoaded) {
            return true;
          }
          if (current is HomeLoaded) {
            return previous.ondcItems != current.ondcItems;
          }
          return false;
        },
        builder: (context, state) {
          List<ProductEntity>? ondcItems =
              state.mapOrNull(loaded: (value) => value.ondcItems);
          // Use AnimatedSwitcher for smooth transitions between loading and loaded states
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            switchInCurve: Curves.easeInOut,
            switchOutCurve: Curves.easeInOut,
            child: ondcItems == null
                ? RepaintBoundary(
                    child: Semantics(
                      label: 'Loading top products',
                      hint: 'Please wait while top products are loading',
                      child: GridView.builder(
                        shrinkWrap: true,
                        primary: false,
                        padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.screenHzPadding,
                            vertical: 20),
                        itemCount: 6,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            mainAxisSpacing: constraints.maxWidth * 0.025,
                            crossAxisSpacing: constraints.maxWidth * 0.025,
                            mainAxisExtent: (constraints.maxWidth /
                                    (constraints.maxWidth * (0.005)))
                                .sp),
                        itemBuilder: (context, index) {
                          return ProductCardShimmer();
                        },
                      ),
                    ),
                  )
                : ondcItems.isEmpty
                    ? const SizedBox.shrink()
                    : RepaintBoundary(
                        child: Column(
                          key: const ValueKey('ondc_items_loaded'),
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppDimensions.screenHzPadding,
                                  vertical: 8),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: CustomText(
                                      'Top Products',
                                      fontSize: 20,
                                      fontWeight: FontWeight.w900,
                                      color: AppColors.primary700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            GridView.builder(
                              shrinkWrap: true,
                              primary: false,
                              padding: EdgeInsets.symmetric(
                                horizontal: AppDimensions.screenHzPadding,
                              ),
                              itemCount: ondcItems.length,
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 3,
                                      mainAxisSpacing:
                                          constraints.maxWidth * 0.025,
                                      crossAxisSpacing:
                                          constraints.maxWidth * 0.025,
                                      mainAxisExtent: (constraints.maxWidth /
                                              (constraints.maxWidth * (0.005)))
                                          .sp),
                              itemBuilder: (context, index) {
                                return DiscountedProductCard(
                                  product: ondcItems[index],
                                  isLoading: false,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
          );
        },
      );
    });
  }
}
