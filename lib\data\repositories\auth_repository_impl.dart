import 'package:firebase_auth/firebase_auth.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/core/services/app_verification_service.dart';
import 'package:rozana/domain/repositories/auth_repository_interface.dart';

class AuthRepositoryImpl implements IAuthRepository {
  final FirebaseAuth _firebaseAuth;
  final AppVerificationService _verificationService;

  // Constructor with dependency injection
  AuthRepositoryImpl({FirebaseAuth? firebaseAuth})
      : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _verificationService = AppVerificationService(
            auth: firebaseAuth ?? FirebaseAuth.instance) {
    // Configure Firebase Auth settings using the verification service
    _verificationService.configureFirebaseAuth();
  }

  // Mock method to simulate mobile number verification
  @override
  Future<void> verifyMobileNumber(
    String mobileNumber, {
    required Function(String, int?) codeSent,
    required Function(Exception) verificationFailed,
    required Function(UserCredential) verificationCompleted,
  }) async {
    try {
      final formattedNumber = mobileNumber.trim();
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: formattedNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            UserCredential userCred =
                await _firebaseAuth.signInWithCredential(credential);
            verificationCompleted(userCred);
          } catch (e) {
            verificationFailed(Exception(e.toString()));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          // Use the verification service to get a readable error message
          String errorMessage = _verificationService.getReadableErrorMessage(e);
          LogMessage.p('Formatted error message: $errorMessage');
          verificationFailed(Exception(errorMessage));
        },
        codeSent: (String verificationId, int? resendToken) {
          LogMessage.p(
              'Code sent successfully, verificationId: $verificationId');
          codeSent(verificationId, resendToken);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          LogMessage.p('Code auto retrieval timeout: $verificationId');
        },
        timeout: const Duration(seconds: 120),
      );
    } on FirebaseAuthException catch (e) {
      LogMessage.p('FirebaseAuthException: ${e.code} - ${e.message}');
      throw _verificationService.getReadableErrorMessage(e);
    } catch (e) {
      LogMessage.p('Unknown error during mobile number verification: $e');
      throw 'Something went wrong. Please try again.';
    }
  }

  @override
  Future<UserCredential> validateOTP(String verificationId, String otp) async {
    try {
      // Create credential
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      // Sign in
      UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        return userCredential;
      }

      throw FirebaseAuthException(
        code: 'null-user',
        message: 'Failed to verify OTP, no user returned.',
      );
    } on FirebaseAuthException catch (e) {
      LogMessage.p('FirebaseAuthException: ${e.code} - ${e.message}');
      throw e.message ?? 'Something went wrong while verifying the OTP.';
    } catch (e) {
      LogMessage.p('Unknown error during OTP verification: $e');
      throw 'Something went wrong. Please try again.';
    }
  }
}
