/// Interface for platform-specific Google Places service implementations
abstract class PlacesServiceInterface {
  /// Initialize the Places service
  Future<bool> initPlacesService();

  /// Generate a new session token for autocomplete requests
  Future<String> generateSessionToken();

  /// Get place autocomplete suggestions
  Future<List<Map<String, dynamic>>> getPlaceAutocomplete({
    required String input,
    String? sessionToken,
    Map<String, dynamic>? options,
  });

  /// Get place details by place ID
  Future<Map<String, dynamic>?> getPlaceDetailsById(String placeId);
}
