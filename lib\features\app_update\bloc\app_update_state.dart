import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_update_state.freezed.dart';

@freezed
class AppUpdateState with _$AppUpdateState {
  const factory AppUpdateState.initial() = AppUpdateInitial;

  const factory AppUpdateState.updateAvailable({
    required int currentVersion,
    required int versionCode,
    String? versionNumber,
    required bool forceUpdate,
  }) = UpdateAvailable;

  const factory AppUpdateState.noUpdate({
    required int currentVersion,
  }) = NoUpdate;
}
