import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/services/analytics_events/address_analytics_events.dart';
import '../../bloc/address_bloc.dart';
import '../widgets/map_bottom_sheet.dart';

class LocationMapView extends StatefulWidget {
  final AddressModel? initialAddress;

  const LocationMapView({
    super.key,
    this.initialAddress,
  });

  @override
  State<LocationMapView> createState() => _LocationMapViewState();
}

class _LocationMapViewState extends State<LocationMapView> {
  GoogleMapController? _mapController;
  LatLng? _currentLatLng;

  @override
  void initState() {
    super.initState();
    // Track address map landing page analytics
    AddressAnalyticsEvents().trackAddressMapLandingPage();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _mapController!.setMapStyle(AddressBloc.mapStyle);
    if (widget.initialAddress != null) {
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(
              widget.initialAddress?.latitude?.toDouble() ?? 0,
              widget.initialAddress?.longitude?.toDouble() ?? 0,
            ),
            zoom: 19,
          ),
        ),
      );
    }
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates as map moves
    _currentLatLng =
        LatLng(position.target.latitude, position.target.longitude);
  }

  void _onCameraIdle() async {
    if (_mapController != null) {
      getIt<AddressBloc>().add(
        AddressEvent.mapCameraIdle(
          latitude: _currentLatLng?.latitude ?? 0,
          longitude: _currentLatLng?.longitude ?? 0,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final LatLng initialPosition = widget.initialAddress != null
        ? LatLng(
            widget.initialAddress?.latitude?.toDouble() ?? 0,
            widget.initialAddress?.longitude?.toDouble() ?? 0,
          )
        : const LatLng(28.6139, 77.2090);

    return BlocListener<AddressBloc, AddressState>(
      listenWhen: (previous, current) => current.relocateMap,
      listener: (ctx, state) {
        state.mapOrNull(
          mapSelection: (value) {
            AddressModel? tempAddress = value.temporaryAddress;
            if (tempAddress?.latitude != null &&
                tempAddress?.longitude != null) {
              final newPosition = LatLng(
                tempAddress?.latitude?.toDouble() ?? 0,
                tempAddress?.longitude?.toDouble() ?? 0,
              );
              // This also handles moving the map after a place search is selected.
              _mapController?.animateCamera(
                CameraUpdate.newCameraPosition(
                  CameraPosition(target: newPosition, zoom: 19),
                ),
              );
            }
          },
          error: (value) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(value.message)),
            );
          },
        );
      },
      child: LayoutBuilder(builder: (ctx, constraints) {
        return Stack(
          children: [
            SingleChildScrollView(
              child: SizedBox(
                height: (constraints.maxHeight) -
                    ((MediaQuery.of(ctx).padding.vertical) + 10),
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          GoogleMap(
                            mapType: MapType.normal,
                            indoorViewEnabled: true,
                            initialCameraPosition: CameraPosition(
                              target: initialPosition,
                              zoom: 19,
                            ),
                            buildingsEnabled: true,
                            onMapCreated: _onMapCreated,
                            onCameraMove: _onCameraMove,
                            onCameraIdle: _onCameraIdle,
                            myLocationEnabled: true,
                            myLocationButtonEnabled: false,
                            zoomControlsEnabled: false,
                            mapToolbarEnabled: false,
                            compassEnabled: false,
                            // markers: _markers,
                            zoomGesturesEnabled: true,
                            rotateGesturesEnabled: true,
                            scrollGesturesEnabled: true,
                            tiltGesturesEnabled: true,
                            style: '''[
                            {
                              "featureType": "poi",
                              "elementType": "labels",
                              "stylers": [{"visibility": "off"}]
                            }
                          ]''',
                          ),
                          Center(
                            child: SafeArea(
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 24),
                                child: Image.asset(
                                  'assets/new/icons/pin_drop.png',
                                  width: 48,
                                  height: 48,
                                ),
                              ),
                            ),
                          ),
                          Align(
                              alignment: Alignment.bottomCenter,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  InkWell(
                                    splashColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      getIt<AddressBloc>().add(
                                          const AddressEvent
                                              .getCurrentLocation());
                                    },
                                    child: Container(
                                      padding: EdgeInsets.all(8),
                                      margin: EdgeInsets.only(bottom: 20),
                                      decoration: BoxDecoration(
                                        color: AppColors.neutral100,
                                        border: Border.all(
                                            color: AppColors.primary500,
                                            width: 1),
                                        borderRadius: BorderRadius.circular(8),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black
                                                .withValues(alpha: 0.12),
                                            blurRadius: 6,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        children: [
                                          Image.asset(
                                            'assets/new/icons/my_location.png',
                                            width: 16,
                                            height: 16,
                                          ),
                                          SizedBox(width: 4),
                                          CustomText(
                                            'Go to current location',
                                            style: TextStyle(
                                              color: AppColors.primary500,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),
                    BlocBuilder<AddressBloc, AddressState>(
                      builder: (ctx, state) {
                        return state.maybeMap(
                          mapSelection: (value) {
                            AddressModel? address = value.temporaryAddress;
                            bool isAuthenticated =
                                getIt<AppBloc>().isAuthenticated;
                            return MapBottomSheet(
                              temporaryAddress: address,
                              isAuthenticated: isAuthenticated,
                              onConfirmLocation: () {
                                HapticFeedback.lightImpact();

                                if (isAuthenticated) {
                                  getIt<AddressBloc>()
                                      .add(AddressEvent.selectFromMap());
                                } else {
                                  AddressBloc addressBloc =
                                      getIt<AddressBloc>();
                                  addressBloc.add(AddressEvent.selectAddress(
                                      address?.id ?? ''));
                                  addressBloc.stream.listen((data) {
                                    // ignore: use_build_context_synchronously
                                    if (context.canPop()) {
                                      // ignore: use_build_context_synchronously
                                      context.pop();
                                    }
                                  });
                                }
                              },
                            );
                          },
                          orElse: () => const SizedBox.shrink(),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
                top: MediaQuery.of(ctx).padding.top + 16,
                left: 16,
                right: 16,
                child: GestureDetector(
                  onTap: () {
                    getIt<AddressBloc>().add(AddressEvent.initSearch());
                  },
                  child: Container(
                    height: 48.sp,
                    decoration: BoxDecoration(
                      color: AppColors.neutral100,
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: AppColors.primary100, width: 0.5),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.12),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        SizedBox.square(
                          dimension: 48,
                          child: Center(
                            child: Image.asset(
                              'assets/new/icons/search.png',
                              height: 24,
                              width: 24,
                            ),
                          ),
                        ),
                        Expanded(
                            child: CustomText(
                          'Search for area, street name...',
                          fontSize: 16,
                          color: AppColors.primary300,
                          fontWeight: FontWeight.w500,
                          textHeight: 1.4,
                          overflow: TextOverflow.ellipsis,
                        ))
                      ],
                    ),
                  ),
                )),
          ],
        );
      }),
    );
  }
}
