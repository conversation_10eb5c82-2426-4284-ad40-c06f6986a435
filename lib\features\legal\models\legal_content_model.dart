class LegalContentModel {
  final String title;
  final List<LegalSection> sections;

  LegalContentModel({
    required this.title,
    required this.sections,
  });

  factory LegalContentModel.fromJson(Map<String, dynamic> json) {
    return LegalContentModel(
      title: json['title'] ?? '',
      sections: (json['sections'] as List<dynamic>?)
              ?.map((section) => LegalSection.fromJson(section))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'sections': sections.map((section) => section.toJson()).toList(),
    };
  }
}

class LegalSection {
  final String heading;
  final String content;

  LegalSection({
    required this.heading,
    required this.content,
  });

  factory LegalSection.fromJson(Map<String, dynamic> json) {
    return LegalSection(
      heading: json['heading'] ?? '',
      content: json['content'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'heading': heading,
      'content': content,
    };
  }
}
