import 'dart:math';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/data/mappers/product_mapper.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/data/services/data_loading_manager.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import 'package:rozana/features/products/presentation/widgets/product_section.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';

class CollectionProductsSection extends StatefulWidget {
  final String collectionId;
  final String title;
  final VoidCallback? onSeeAllTap;
  final EdgeInsetsGeometry? padding;
  final String categoryId;

  const CollectionProductsSection({
    super.key,
    required this.collectionId,
    required this.title,
    this.onSeeAllTap,
    this.padding,
    required this.categoryId,
  });

  @override
  State<CollectionProductsSection> createState() =>
      _CollectionProductsSectionState();
}

class _CollectionProductsSectionState extends State<CollectionProductsSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<ProductModel> _products = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // Create a CategoryEntity with the collectionId
      final category = CategoryEntity(
        id: '',
        categoryId: '',
        name: '',
        count: 0,
        collectionId: widget.collectionId,
      );
      
      // Load products by collection ID
      final productEntities = await _dataManager.loadProducts(
        category: category,
        useCollectionId: true,
        pageSize: 20, // Fetch more than needed to show in "See More"
      );

      if (mounted) {
        setState(() {
          _products.clear();
          _products.addAll(productEntities
              .map((entity) => ProductMapper.toModel(entity))
              .toList());
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    return _buildContent();
  }

  Widget _buildContent() {
    if (_products.isEmpty) {
      return const SizedBox.shrink(); // Don't show anything if no products
    }
    
    // Determine how many products to show (max 6)
    final displayProducts = _products.length > 6 
        ? _products.sublist(0, 6) 
        : _products;
    final bool showSeeMore = _products.length > 6;

    return
     Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product grid using ProductsSection
        
        AppCard(
          
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if(widget.title.isNotEmpty)
              CustomText(
               widget.title,
               color: AppColors.primary700,
               fontSize: 20,
               fontWeight: FontWeight.w800,
              ),
              SizedBox(height: 8,),
              ProductsSection(
                title: '',
                onSeeAllTap: widget.onSeeAllTap,
                preloadData: false, // We provide our own data
                showSeeAll: false, // We'll handle this separately
                useGridView: true,
                gridCrossAxisCount: 3,
                gridChildAspectRatio: 0.005,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: widget.padding,
                externalProducts: displayProducts,                
              ),

              if (showSeeMore)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                onTap: () {},
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                  decoration: BoxDecoration(
                    color: AppColors.neutral100,
                    borderRadius: BorderRadius.circular(8), // pill shape like design
                    border: Border.all(
                      color: AppColors.primary600, // purple border
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center, 
                    children: [
                      // product thumbnails
                      SizedBox(
                        height: 40,
                        width: 100,
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            for (int i = 0; i < min(3, _products.length - 6); i++)
                              Positioned(
                                left: i * 28.0,
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(color: AppColors.neutral200, width: 2),

                                    image: DecorationImage(
                                      image: NetworkImage(_products[i].imageUrl ?? ''),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Text
                      CustomText(
                        "See all products",
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: AppColors.primary600,
                      ),
                      const SizedBox(width: 6),
                      // Arrow
                      CustomImage(
                        imageUrl: 'assets/new/icons/arrow_right.png',
                        width: 16,
                        height: 16,
                        imageColor: AppColors.primary600,
                      )
                    ],
                  ),
                ),
              ),
            ),

            ],
          ),
        ),
        
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: CustomText(
              widget.title,
              color: AppColors.primary700,
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
            childAspectRatio: 0.7,
          ),
          itemBuilder: (context, index) {
            return const ProductCardShimmer();
          },
          itemCount: 6,
        ),
      ],
    );
  }
}
