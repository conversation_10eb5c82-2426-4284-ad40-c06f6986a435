// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'free_product_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FreeProductState {
  bool get isLoading;
  bool get isCheckingOffer;
  List<ProductEntity> get freeProducts;
  List<ProductEntity> get selectedFreeProducts;
  String? get error;
  String? get offerProductId;
  String? get offerVariantId;
  String? get sku;
  bool get hasActiveOffer;

  /// Create a copy of FreeProductState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FreeProductStateCopyWith<FreeProductState> get copyWith =>
      _$FreeProductStateCopyWithImpl<FreeProductState>(
          this as FreeProductState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FreeProductState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isCheckingOffer, isCheckingOffer) ||
                other.isCheckingOffer == isCheckingOffer) &&
            const DeepCollectionEquality()
                .equals(other.freeProducts, freeProducts) &&
            const DeepCollectionEquality()
                .equals(other.selectedFreeProducts, selectedFreeProducts) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.offerProductId, offerProductId) ||
                other.offerProductId == offerProductId) &&
            (identical(other.offerVariantId, offerVariantId) ||
                other.offerVariantId == offerVariantId) &&
            (identical(other.sku, sku) || other.sku == sku) &&
            (identical(other.hasActiveOffer, hasActiveOffer) ||
                other.hasActiveOffer == hasActiveOffer));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isCheckingOffer,
      const DeepCollectionEquality().hash(freeProducts),
      const DeepCollectionEquality().hash(selectedFreeProducts),
      error,
      offerProductId,
      offerVariantId,
      sku,
      hasActiveOffer);

  @override
  String toString() {
    return 'FreeProductState(isLoading: $isLoading, isCheckingOffer: $isCheckingOffer, freeProducts: $freeProducts, selectedFreeProducts: $selectedFreeProducts, error: $error, offerProductId: $offerProductId, offerVariantId: $offerVariantId, sku: $sku, hasActiveOffer: $hasActiveOffer)';
  }
}

/// @nodoc
abstract mixin class $FreeProductStateCopyWith<$Res> {
  factory $FreeProductStateCopyWith(
          FreeProductState value, $Res Function(FreeProductState) _then) =
      _$FreeProductStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      bool isCheckingOffer,
      List<ProductEntity> freeProducts,
      List<ProductEntity> selectedFreeProducts,
      String? error,
      String? offerProductId,
      String? offerVariantId,
      String? sku,
      bool hasActiveOffer});
}

/// @nodoc
class _$FreeProductStateCopyWithImpl<$Res>
    implements $FreeProductStateCopyWith<$Res> {
  _$FreeProductStateCopyWithImpl(this._self, this._then);

  final FreeProductState _self;
  final $Res Function(FreeProductState) _then;

  /// Create a copy of FreeProductState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isCheckingOffer = null,
    Object? freeProducts = null,
    Object? selectedFreeProducts = null,
    Object? error = freezed,
    Object? offerProductId = freezed,
    Object? offerVariantId = freezed,
    Object? sku = freezed,
    Object? hasActiveOffer = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckingOffer: null == isCheckingOffer
          ? _self.isCheckingOffer
          : isCheckingOffer // ignore: cast_nullable_to_non_nullable
              as bool,
      freeProducts: null == freeProducts
          ? _self.freeProducts
          : freeProducts // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>,
      selectedFreeProducts: null == selectedFreeProducts
          ? _self.selectedFreeProducts
          : selectedFreeProducts // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      offerProductId: freezed == offerProductId
          ? _self.offerProductId
          : offerProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      offerVariantId: freezed == offerVariantId
          ? _self.offerVariantId
          : offerVariantId // ignore: cast_nullable_to_non_nullable
              as String?,
      sku: freezed == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String?,
      hasActiveOffer: null == hasActiveOffer
          ? _self.hasActiveOffer
          : hasActiveOffer // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [FreeProductState].
extension FreeProductStatePatterns on FreeProductState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_FreeProductState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _FreeProductState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_FreeProductState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FreeProductState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_FreeProductState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FreeProductState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool isLoading,
            bool isCheckingOffer,
            List<ProductEntity> freeProducts,
            List<ProductEntity> selectedFreeProducts,
            String? error,
            String? offerProductId,
            String? offerVariantId,
            String? sku,
            bool hasActiveOffer)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _FreeProductState() when $default != null:
        return $default(
            _that.isLoading,
            _that.isCheckingOffer,
            _that.freeProducts,
            _that.selectedFreeProducts,
            _that.error,
            _that.offerProductId,
            _that.offerVariantId,
            _that.sku,
            _that.hasActiveOffer);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool isLoading,
            bool isCheckingOffer,
            List<ProductEntity> freeProducts,
            List<ProductEntity> selectedFreeProducts,
            String? error,
            String? offerProductId,
            String? offerVariantId,
            String? sku,
            bool hasActiveOffer)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FreeProductState():
        return $default(
            _that.isLoading,
            _that.isCheckingOffer,
            _that.freeProducts,
            _that.selectedFreeProducts,
            _that.error,
            _that.offerProductId,
            _that.offerVariantId,
            _that.sku,
            _that.hasActiveOffer);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool isLoading,
            bool isCheckingOffer,
            List<ProductEntity> freeProducts,
            List<ProductEntity> selectedFreeProducts,
            String? error,
            String? offerProductId,
            String? offerVariantId,
            String? sku,
            bool hasActiveOffer)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FreeProductState() when $default != null:
        return $default(
            _that.isLoading,
            _that.isCheckingOffer,
            _that.freeProducts,
            _that.selectedFreeProducts,
            _that.error,
            _that.offerProductId,
            _that.offerVariantId,
            _that.sku,
            _that.hasActiveOffer);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _FreeProductState implements FreeProductState {
  const _FreeProductState(
      {this.isLoading = false,
      this.isCheckingOffer = false,
      final List<ProductEntity> freeProducts = const [],
      final List<ProductEntity> selectedFreeProducts = const [],
      this.error,
      this.offerProductId,
      this.offerVariantId,
      this.sku,
      this.hasActiveOffer = false})
      : _freeProducts = freeProducts,
        _selectedFreeProducts = selectedFreeProducts;

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isCheckingOffer;
  final List<ProductEntity> _freeProducts;
  @override
  @JsonKey()
  List<ProductEntity> get freeProducts {
    if (_freeProducts is EqualUnmodifiableListView) return _freeProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_freeProducts);
  }

  final List<ProductEntity> _selectedFreeProducts;
  @override
  @JsonKey()
  List<ProductEntity> get selectedFreeProducts {
    if (_selectedFreeProducts is EqualUnmodifiableListView)
      return _selectedFreeProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedFreeProducts);
  }

  @override
  final String? error;
  @override
  final String? offerProductId;
  @override
  final String? offerVariantId;
  @override
  final String? sku;
  @override
  @JsonKey()
  final bool hasActiveOffer;

  /// Create a copy of FreeProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FreeProductStateCopyWith<_FreeProductState> get copyWith =>
      __$FreeProductStateCopyWithImpl<_FreeProductState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FreeProductState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isCheckingOffer, isCheckingOffer) ||
                other.isCheckingOffer == isCheckingOffer) &&
            const DeepCollectionEquality()
                .equals(other._freeProducts, _freeProducts) &&
            const DeepCollectionEquality()
                .equals(other._selectedFreeProducts, _selectedFreeProducts) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.offerProductId, offerProductId) ||
                other.offerProductId == offerProductId) &&
            (identical(other.offerVariantId, offerVariantId) ||
                other.offerVariantId == offerVariantId) &&
            (identical(other.sku, sku) || other.sku == sku) &&
            (identical(other.hasActiveOffer, hasActiveOffer) ||
                other.hasActiveOffer == hasActiveOffer));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isCheckingOffer,
      const DeepCollectionEquality().hash(_freeProducts),
      const DeepCollectionEquality().hash(_selectedFreeProducts),
      error,
      offerProductId,
      offerVariantId,
      sku,
      hasActiveOffer);

  @override
  String toString() {
    return 'FreeProductState(isLoading: $isLoading, isCheckingOffer: $isCheckingOffer, freeProducts: $freeProducts, selectedFreeProducts: $selectedFreeProducts, error: $error, offerProductId: $offerProductId, offerVariantId: $offerVariantId, sku: $sku, hasActiveOffer: $hasActiveOffer)';
  }
}

/// @nodoc
abstract mixin class _$FreeProductStateCopyWith<$Res>
    implements $FreeProductStateCopyWith<$Res> {
  factory _$FreeProductStateCopyWith(
          _FreeProductState value, $Res Function(_FreeProductState) _then) =
      __$FreeProductStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isCheckingOffer,
      List<ProductEntity> freeProducts,
      List<ProductEntity> selectedFreeProducts,
      String? error,
      String? offerProductId,
      String? offerVariantId,
      String? sku,
      bool hasActiveOffer});
}

/// @nodoc
class __$FreeProductStateCopyWithImpl<$Res>
    implements _$FreeProductStateCopyWith<$Res> {
  __$FreeProductStateCopyWithImpl(this._self, this._then);

  final _FreeProductState _self;
  final $Res Function(_FreeProductState) _then;

  /// Create a copy of FreeProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? isCheckingOffer = null,
    Object? freeProducts = null,
    Object? selectedFreeProducts = null,
    Object? error = freezed,
    Object? offerProductId = freezed,
    Object? offerVariantId = freezed,
    Object? sku = freezed,
    Object? hasActiveOffer = null,
  }) {
    return _then(_FreeProductState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckingOffer: null == isCheckingOffer
          ? _self.isCheckingOffer
          : isCheckingOffer // ignore: cast_nullable_to_non_nullable
              as bool,
      freeProducts: null == freeProducts
          ? _self._freeProducts
          : freeProducts // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>,
      selectedFreeProducts: null == selectedFreeProducts
          ? _self._selectedFreeProducts
          : selectedFreeProducts // ignore: cast_nullable_to_non_nullable
              as List<ProductEntity>,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      offerProductId: freezed == offerProductId
          ? _self.offerProductId
          : offerProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      offerVariantId: freezed == offerVariantId
          ? _self.offerVariantId
          : offerVariantId // ignore: cast_nullable_to_non_nullable
              as String?,
      sku: freezed == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String?,
      hasActiveOffer: null == hasActiveOffer
          ? _self.hasActiveOffer
          : hasActiveOffer // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
