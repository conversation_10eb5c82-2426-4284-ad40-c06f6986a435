// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'categories_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CategoriesEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CategoriesEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategoriesEvent()';
  }
}

/// @nodoc
class $CategoriesEventCopyWith<$Res> {
  $CategoriesEventCopyWith(
      CategoriesEvent _, $Res Function(CategoriesEvent) __);
}

/// Adds pattern-matching-related methods to [CategoriesEvent].
extension CategoriesEventPatterns on CategoriesEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Fetch value)? fetchCategories,
    TResult Function(_Update value)? updateLoadedList,
    TResult Function(_ScrollToItem value)? scrollToItem,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Fetch() when fetchCategories != null:
        return fetchCategories(_that);
      case _Update() when updateLoadedList != null:
        return updateLoadedList(_that);
      case _ScrollToItem() when scrollToItem != null:
        return scrollToItem(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Fetch value) fetchCategories,
    required TResult Function(_Update value) updateLoadedList,
    required TResult Function(_ScrollToItem value) scrollToItem,
  }) {
    final _that = this;
    switch (_that) {
      case _Fetch():
        return fetchCategories(_that);
      case _Update():
        return updateLoadedList(_that);
      case _ScrollToItem():
        return scrollToItem(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Fetch value)? fetchCategories,
    TResult? Function(_Update value)? updateLoadedList,
    TResult? Function(_ScrollToItem value)? scrollToItem,
  }) {
    final _that = this;
    switch (_that) {
      case _Fetch() when fetchCategories != null:
        return fetchCategories(_that);
      case _Update() when updateLoadedList != null:
        return updateLoadedList(_that);
      case _ScrollToItem() when scrollToItem != null:
        return scrollToItem(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchCategories,
    TResult Function(List<CategoryEntity>? categories)? updateLoadedList,
    TResult Function(CategoryEntity? categories)? scrollToItem,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Fetch() when fetchCategories != null:
        return fetchCategories();
      case _Update() when updateLoadedList != null:
        return updateLoadedList(_that.categories);
      case _ScrollToItem() when scrollToItem != null:
        return scrollToItem(_that.categories);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchCategories,
    required TResult Function(List<CategoryEntity>? categories)
        updateLoadedList,
    required TResult Function(CategoryEntity? categories) scrollToItem,
  }) {
    final _that = this;
    switch (_that) {
      case _Fetch():
        return fetchCategories();
      case _Update():
        return updateLoadedList(_that.categories);
      case _ScrollToItem():
        return scrollToItem(_that.categories);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchCategories,
    TResult? Function(List<CategoryEntity>? categories)? updateLoadedList,
    TResult? Function(CategoryEntity? categories)? scrollToItem,
  }) {
    final _that = this;
    switch (_that) {
      case _Fetch() when fetchCategories != null:
        return fetchCategories();
      case _Update() when updateLoadedList != null:
        return updateLoadedList(_that.categories);
      case _ScrollToItem() when scrollToItem != null:
        return scrollToItem(_that.categories);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Fetch implements CategoriesEvent {
  const _Fetch();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Fetch);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategoriesEvent.fetchCategories()';
  }
}

/// @nodoc

class _Update implements CategoriesEvent {
  const _Update({final List<CategoryEntity>? categories})
      : _categories = categories;

  final List<CategoryEntity>? _categories;
  List<CategoryEntity>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of CategoriesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateCopyWith<_Update> get copyWith =>
      __$UpdateCopyWithImpl<_Update>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Update &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_categories));

  @override
  String toString() {
    return 'CategoriesEvent.updateLoadedList(categories: $categories)';
  }
}

/// @nodoc
abstract mixin class _$UpdateCopyWith<$Res>
    implements $CategoriesEventCopyWith<$Res> {
  factory _$UpdateCopyWith(_Update value, $Res Function(_Update) _then) =
      __$UpdateCopyWithImpl;
  @useResult
  $Res call({List<CategoryEntity>? categories});
}

/// @nodoc
class __$UpdateCopyWithImpl<$Res> implements _$UpdateCopyWith<$Res> {
  __$UpdateCopyWithImpl(this._self, this._then);

  final _Update _self;
  final $Res Function(_Update) _then;

  /// Create a copy of CategoriesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categories = freezed,
  }) {
    return _then(_Update(
      categories: freezed == categories
          ? _self._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
    ));
  }
}

/// @nodoc

class _ScrollToItem implements CategoriesEvent {
  const _ScrollToItem({this.categories});

  final CategoryEntity? categories;

  /// Create a copy of CategoriesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ScrollToItemCopyWith<_ScrollToItem> get copyWith =>
      __$ScrollToItemCopyWithImpl<_ScrollToItem>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ScrollToItem &&
            (identical(other.categories, categories) ||
                other.categories == categories));
  }

  @override
  int get hashCode => Object.hash(runtimeType, categories);

  @override
  String toString() {
    return 'CategoriesEvent.scrollToItem(categories: $categories)';
  }
}

/// @nodoc
abstract mixin class _$ScrollToItemCopyWith<$Res>
    implements $CategoriesEventCopyWith<$Res> {
  factory _$ScrollToItemCopyWith(
          _ScrollToItem value, $Res Function(_ScrollToItem) _then) =
      __$ScrollToItemCopyWithImpl;
  @useResult
  $Res call({CategoryEntity? categories});
}

/// @nodoc
class __$ScrollToItemCopyWithImpl<$Res>
    implements _$ScrollToItemCopyWith<$Res> {
  __$ScrollToItemCopyWithImpl(this._self, this._then);

  final _ScrollToItem _self;
  final $Res Function(_ScrollToItem) _then;

  /// Create a copy of CategoriesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categories = freezed,
  }) {
    return _then(_ScrollToItem(
      categories: freezed == categories
          ? _self.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
    ));
  }
}

/// @nodoc
mixin _$CategoriesState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CategoriesState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategoriesState()';
  }
}

/// @nodoc
class $CategoriesStateCopyWith<$Res> {
  $CategoriesStateCopyWith(
      CategoriesState _, $Res Function(CategoriesState) __);
}

/// Adds pattern-matching-related methods to [CategoriesState].
extension CategoriesStatePatterns on CategoriesState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CategoryInitial value)? initial,
    TResult Function(_CategoryLoaded value)? loaded,
    TResult Function(_CategoryError value)? error,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CategoryInitial() when initial != null:
        return initial(_that);
      case _CategoryLoaded() when loaded != null:
        return loaded(_that);
      case _CategoryError() when error != null:
        return error(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CategoryInitial value) initial,
    required TResult Function(_CategoryLoaded value) loaded,
    required TResult Function(_CategoryError value) error,
  }) {
    final _that = this;
    switch (_that) {
      case _CategoryInitial():
        return initial(_that);
      case _CategoryLoaded():
        return loaded(_that);
      case _CategoryError():
        return error(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CategoryInitial value)? initial,
    TResult? Function(_CategoryLoaded value)? loaded,
    TResult? Function(_CategoryError value)? error,
  }) {
    final _that = this;
    switch (_that) {
      case _CategoryInitial() when initial != null:
        return initial(_that);
      case _CategoryLoaded() when loaded != null:
        return loaded(_that);
      case _CategoryError() when error != null:
        return error(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(
            List<CategoryEntity>? categories, CategoryEntity? switchToItem)?
        loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CategoryInitial() when initial != null:
        return initial();
      case _CategoryLoaded() when loaded != null:
        return loaded(_that.categories, _that.switchToItem);
      case _CategoryError() when error != null:
        return error(_that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(
            List<CategoryEntity>? categories, CategoryEntity? switchToItem)
        loaded,
    required TResult Function(String message) error,
  }) {
    final _that = this;
    switch (_that) {
      case _CategoryInitial():
        return initial();
      case _CategoryLoaded():
        return loaded(_that.categories, _that.switchToItem);
      case _CategoryError():
        return error(_that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(
            List<CategoryEntity>? categories, CategoryEntity? switchToItem)?
        loaded,
    TResult? Function(String message)? error,
  }) {
    final _that = this;
    switch (_that) {
      case _CategoryInitial() when initial != null:
        return initial();
      case _CategoryLoaded() when loaded != null:
        return loaded(_that.categories, _that.switchToItem);
      case _CategoryError() when error != null:
        return error(_that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _CategoryInitial implements CategoriesState {
  const _CategoryInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CategoryInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategoriesState.initial()';
  }
}

/// @nodoc

class _CategoryLoaded implements CategoriesState {
  const _CategoryLoaded(
      {required final List<CategoryEntity>? categories,
      this.switchToItem = null})
      : _categories = categories;

  final List<CategoryEntity>? _categories;
  List<CategoryEntity>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @JsonKey()
  final CategoryEntity? switchToItem;

  /// Create a copy of CategoriesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategoryLoadedCopyWith<_CategoryLoaded> get copyWith =>
      __$CategoryLoadedCopyWithImpl<_CategoryLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategoryLoaded &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            (identical(other.switchToItem, switchToItem) ||
                other.switchToItem == switchToItem));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_categories), switchToItem);

  @override
  String toString() {
    return 'CategoriesState.loaded(categories: $categories, switchToItem: $switchToItem)';
  }
}

/// @nodoc
abstract mixin class _$CategoryLoadedCopyWith<$Res>
    implements $CategoriesStateCopyWith<$Res> {
  factory _$CategoryLoadedCopyWith(
          _CategoryLoaded value, $Res Function(_CategoryLoaded) _then) =
      __$CategoryLoadedCopyWithImpl;
  @useResult
  $Res call({List<CategoryEntity>? categories, CategoryEntity? switchToItem});
}

/// @nodoc
class __$CategoryLoadedCopyWithImpl<$Res>
    implements _$CategoryLoadedCopyWith<$Res> {
  __$CategoryLoadedCopyWithImpl(this._self, this._then);

  final _CategoryLoaded _self;
  final $Res Function(_CategoryLoaded) _then;

  /// Create a copy of CategoriesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categories = freezed,
    Object? switchToItem = freezed,
  }) {
    return _then(_CategoryLoaded(
      categories: freezed == categories
          ? _self._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryEntity>?,
      switchToItem: freezed == switchToItem
          ? _self.switchToItem
          : switchToItem // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
    ));
  }
}

/// @nodoc

class _CategoryError implements CategoriesState {
  const _CategoryError({required this.message});

  final String message;

  /// Create a copy of CategoriesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategoryErrorCopyWith<_CategoryError> get copyWith =>
      __$CategoryErrorCopyWithImpl<_CategoryError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategoryError &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'CategoriesState.error(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$CategoryErrorCopyWith<$Res>
    implements $CategoriesStateCopyWith<$Res> {
  factory _$CategoryErrorCopyWith(
          _CategoryError value, $Res Function(_CategoryError) _then) =
      __$CategoryErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$CategoryErrorCopyWithImpl<$Res>
    implements _$CategoryErrorCopyWith<$Res> {
  __$CategoryErrorCopyWithImpl(this._self, this._then);

  final _CategoryError _self;
  final $Res Function(_CategoryError) _then;

  /// Create a copy of CategoriesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_CategoryError(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
