import 'package:dotlottie_loader/dotlottie_loader.dart';
import 'package:flutter/cupertino.dart';
import 'package:lottie/lottie.dart';
import 'package:rozana/widgets/custom_image.dart';

class ScrollableLottieImage extends StatefulWidget {
  final String imageUrl;
  final int imageHeight;
  final double imagePadding;
  final bool scroll;
  final double transformValue;
  final BoxConstraints constraints;
  final bool isScrolled;
  final double scrollOffset;

  const ScrollableLottieImage({
    super.key,
    required this.imageUrl,
    required this.imageHeight,
    required this.imagePadding,
    required this.scroll,
    required this.transformValue,
    required this.constraints,
    required this.isScrolled,
    required this.scrollOffset,
  });

  @override
  _ScrollableLottieImageState createState() => _ScrollableLottieImageState();
}

class _ScrollableLottieImageState extends State<ScrollableLottieImage>
    with SingleTickerProviderStateMixin {
  late final AnimationController _lottieController;

  @override
  void initState() {
    super.initState();
    _lottieController = AnimationController(vsync: this);
  }

  @override
  void dispose() {
    _lottieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final offset = -((widget.scroll ? widget.scrollOffset : 0) -
        (widget.constraints.maxWidth * widget.transformValue));

    return SizedBox(
      height: widget.imageHeight + widget.imagePadding,
      width: MediaQuery.of(context).size.width,
      child: Transform.translate(
        offset: Offset(0, offset),
        child: Center(
          child: !widget.isScrolled
              ? _buildImage(widget.imageUrl)
              : const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _buildImage(String imageUrl) {
    if (imageUrl.endsWith('.json')) {
      if (imageUrl.startsWith('http')) {
        return Lottie.network(
          imageUrl,
          controller: _lottieController,
          onLoaded: (composition) {
            _lottieController
              ..duration = composition.duration
              ..repeat();
          },
          width: double.infinity,
          fit: BoxFit.fitWidth,
          frameRate: FrameRate.max,
        );
      } else {
        return Lottie.asset(
          imageUrl,
          controller: _lottieController,
          onLoaded: (composition) {
            _lottieController
              ..duration = composition.duration
              ..repeat();
          },
          width: double.infinity,
          fit: BoxFit.fitWidth,
          frameRate: FrameRate.max,
        );
      }
    } else if (imageUrl.endsWith('.lottie')) {
      return imageUrl.startsWith('http')
          ? DotLottieLoader.fromNetwork(
              imageUrl,
              frameBuilder: (ctx, dotlottie) {
                if (dotlottie != null) {
                  return Lottie.memory(
                    dotlottie.animations.values.single,
                    controller: _lottieController,
                    onLoaded: (composition) {
                      _lottieController
                        ..duration = composition.duration
                        ..repeat();
                    },
                    width: double.infinity,
                    fit: BoxFit.fitWidth,
                    frameRate: FrameRate.max,
                  );
                }
                return const SizedBox.shrink();
              },
            )
          : DotLottieLoader.fromAsset(
              imageUrl,
              frameBuilder: (ctx, dotlottie) {
                if (dotlottie != null) {
                  return Lottie.memory(
                    dotlottie.animations.values.single,
                    controller: _lottieController,
                    onLoaded: (composition) {
                      _lottieController
                        ..duration = composition.duration
                        ..repeat();
                    },
                    width: double.infinity,
                    fit: BoxFit.fitWidth,
                  );
                }
                return const SizedBox.shrink();
              },
            );
    } else {
      return CustomImage(imageUrl: imageUrl);
    }
  }
}
