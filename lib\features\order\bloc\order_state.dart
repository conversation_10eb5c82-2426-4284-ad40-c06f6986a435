import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/domain/entities/order_entity.dart';

part 'order_state.freezed.dart';

@freezed
class OrderState with _$OrderState {
  /// Initial state
  const factory OrderState.initial() = OrderInitial;

  /// Loading state
  const factory OrderState.loading() = OrderLoading;

  /// Order history loaded state
  const factory OrderState.orderHistoryLoaded({
    required List<OrderEntity> orders,
    @Default(false) bool isLoadingMore,
    @Default(true) bool hasMoreData,
    @Default(0) int currentPage,
    @Default('') String searchQuery,
  }) = OrderHistoryLoaded;

  /// Order details loaded state
  const factory OrderState.orderDetailsLoaded({
    required OrderEntity order,
  }) = OrderDetailsLoaded;

  /// Order cancelled state
  const factory OrderState.orderCancelled({
    required String orderId,
    required String message,
  }) = OrderCancelled;

  /// Error state
  const factory OrderState.error({
    required String message,
    String? orderId,
  }) = OrderError;

  /// Empty state (no orders found)
  const factory OrderState.empty({
    @Default('') String searchQuery,
  }) = OrderEmpty;
  
  /// Items added to cart state
  const factory OrderState.itemsAddedToCart({
    required int itemCount,
    required bool navigateToCart,
  }) = OrderItemsAddedToCart;
}

/// Extension to add helper methods to OrderState
extension OrderStateExtension on OrderState {
  /// Check if currently loading
  bool get isLoading => this is OrderLoading;

  /// Check if has orders
  bool get hasOrders => maybeWhen(
        orderHistoryLoaded: (orders, isLoadingMore, hasMoreData, currentPage, searchQuery) => orders.isNotEmpty,
        orElse: () => false,
      );

  /// Get current orders list
  List<OrderEntity> get orders => maybeWhen(
        orderHistoryLoaded: (orders, isLoadingMore, hasMoreData, currentPage, searchQuery) => orders,
        orElse: () => [],
      );

  /// Get current order details
  OrderEntity? get orderDetails => maybeWhen(
        orderDetailsLoaded: (order) => order,
        orElse: () => null,
      );

  /// Check if loading more data
  bool get isLoadingMore => maybeWhen(
        orderHistoryLoaded: (orders, isLoadingMore, hasMoreData, currentPage, searchQuery) => isLoadingMore,
        orElse: () => false,
      );

  /// Check if has more data to load
  bool get hasMoreData => maybeWhen(
        orderHistoryLoaded: (orders, isLoadingMore, hasMoreData, currentPage, searchQuery) => hasMoreData,
        orElse: () => false,
      );

  /// Get current page
  int get currentPage => maybeWhen(
        orderHistoryLoaded: (orders, isLoadingMore, hasMoreData, currentPage, searchQuery) => currentPage,
        orElse: () => 0,
      );

  /// Get current search query
  String get searchQuery => maybeWhen(
        orderHistoryLoaded: (orders, isLoadingMore, hasMoreData, currentPage, searchQuery) => searchQuery,
        empty: (searchQuery) => searchQuery,
        orElse: () => '',
      );

  /// Check if in error state
  bool get hasError => this is OrderError;

  /// Get error message
  String? get errorMessage => maybeWhen(
        error: (message, _) => message,
        orElse: () => null,
      );
}
