import 'package:rozana/domain/entities/order_item_entity.dart';

import '../repositories/order_repository_interface.dart';

class ReturnOrderUseCase {
  final OrderRepositoryInterface _repository;

  ReturnOrderUseCase(this._repository);

  /// Execute the use case to return an order.
  ///
  /// [orderId] - ID of the order to return.
  /// [returnReason] - The reason for the return.
  /// Returns true if the return was successful, false otherwise.
  Future<void> call(String orderId, OrderItemEntity item, String reason) async {
    try {
      if (orderId.isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }
      return await _repository.returnOrder(orderId, item, reason);
    } catch (e) {
      // Log error and rethrow.
      throw Exception('Failed to return order: $e');
    }
  }
}
