import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/widgets/custom_textfield.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../widgets/custom_image.dart';
import '../../../../widgets/custom_text.dart';
import '../../bloc/address_bloc.dart';

class AddressFormView extends StatefulWidget {
  final AddressModel? address;
  final bool isEdit;

  const AddressFormView({super.key, this.address, required this.isEdit});

  @override
  // ignore: library_private_types_in_public_api
  _AddressFormViewState createState() => _AddressFormViewState();
}

class _AddressFormViewState extends State<AddressFormView> {
  List<String> orderingType = ['Myself', 'Someone else'];
  List<Map<String, String>> addressTypes = [
    {'Home': 'assets/new/icons/house.png'},
    {'Work': 'assets/new/icons/corporate_fare.png'},
    {'Other': 'assets/new/icons/pin_drop.png'},
  ];

  bool foundHome = false;
  bool foundWork = false;
  Map<String, dynamic> userData = {};

  @override
  void initState() {
    String? userString = AppPreferences.getUserdata();
    userData = jsonDecode(userString ?? '');
    _loadExistingAddressTypes();
    AddressBloc.flatNameController?.text = widget.address?.addressLine1;
    AddressBloc.localityController?.text = widget.address?.fullAddress;
    AddressBloc.landmarkController?.text = widget.address?.landmark;

    if (!widget.isEdit) {
      AddressBloc.orderingFor.value = 'myself';
      fillUserDetails();
    } else {
      AddressBloc.addressNameController?.text = widget.address?.addressName;
      AddressBloc.floorNoController?.text = widget.address?.addressLine2;
      AddressBloc.contactNameController?.text = widget.address?.name;
      AddressBloc.contactNumberController?.text = widget.address?.phone
          ?.substring((widget.address?.phone?.length ?? 10) - 10,
              widget.address?.phone?.length);
      String phone = userData['phoneNumber'];
      bool defaultUser = widget.address?.phone == phone;
      AddressBloc.orderingFor.value =
          widget.address?.savedFor ?? (defaultUser ? 'myself' : 'someone else');
      if (defaultUser) {
        AddressBloc.contactNumberController?.text =
            phone.substring((phone.length) - 10, phone.length);
      }
    }

    super.initState();
  }

  @override
  void dispose() {
    AddressBloc.addressNameController?.dispose();
    AddressBloc.flatNameController?.dispose();
    AddressBloc.floorNoController?.dispose();
    AddressBloc.localityController?.dispose();
    AddressBloc.landmarkController?.dispose();
    AddressBloc.contactNameController?.dispose();
    AddressBloc.contactNumberController?.dispose();

    AddressBloc.addressNameController = null;
    AddressBloc.flatNameController = null;
    AddressBloc.floorNoController = null;
    AddressBloc.localityController = null;
    AddressBloc.landmarkController = null;
    AddressBloc.contactNameController = null;
    AddressBloc.contactNumberController = null;
    super.dispose();
  }

  void _loadExistingAddressTypes() {
    try {
      List<AddressModel> userAddresses =
          getIt<AddressBloc>().state.addresses ?? [];

      for (var address in userAddresses) {
        if (widget.address != null && widget.address!.id == address.id) {
          continue;
        }

        if (address.addressType?.toLowerCase() == 'home') foundHome = true;
        if (address.addressType?.toLowerCase() == 'work') foundWork = true;
        if (foundHome && foundWork) break;
      }

      bool isExistingAddress = widget.address != null &&
          userAddresses.any((addr) => addr.id == widget.address?.id);

      if (!isExistingAddress) {
        if (foundHome && foundWork) {
          AddressBloc.selectedAddressType.value = 'other';
        } else if (foundHome) {
          AddressBloc.selectedAddressType.value = 'work';
        } else if (foundWork) {
          AddressBloc.selectedAddressType.value = 'home';
        } else {
          AddressBloc.selectedAddressType.value = 'home';
        }
      } else {
        AddressBloc.selectedAddressType.value =
            widget.address?.addressType ?? '';
      }
    } catch (e) {
      LogMessage.l('Failed to check used address types');
    }
  }

  void fillUserDetails() {
    if (AddressBloc.orderingFor.value == 'myself') {
      AddressBloc.contactNameController?.text = userData['displayName'] ?? '';
      String phone = (userData['phoneNumber'] ?? '');
      AddressBloc.contactNumberController?.text =
          phone.substring((phone.length) - 10, phone.length);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0).copyWith(bottom: 150),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AddressDetailCard(
            titleText: 'Ordering for?',
            children: [
              ValueListenableBuilder(
                  valueListenable: AddressBloc.orderingFor,
                  builder: (context, value, _) {
                    return Row(
                      children: List.generate(
                        orderingType.length,
                        (i) {
                          bool isSelected =
                              value == orderingType[i].toLowerCase();
                          return Flexible(
                            child: CustomRadioButton(
                              isSelected: isSelected,
                              title: orderingType[i],
                              onTap: () {
                                AddressBloc.orderingFor.value =
                                    orderingType[i].toLowerCase();
                                if (orderingType[i].toLowerCase() == 'myself') {
                                  fillUserDetails();
                                } else {
                                  AddressBloc.contactNameController?.clear();
                                  AddressBloc.contactNumberController?.clear();
                                }
                              },
                            ),
                          );
                        },
                      ),
                    );
                  })
            ],
          ),
          SizedBox(height: 12.sp),
          AddressDetailCard(
            titleText: 'Save address as',
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ValueListenableBuilder(
                      valueListenable: AddressBloc.orderingFor,
                      builder: (context, orderingType, _) {
                        return Visibility(
                          visible: orderingType.toLowerCase() == 'myself',
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(bottom: 12.sp),
                                child: ValueListenableBuilder(
                                    valueListenable:
                                        AddressBloc.selectedAddressType,
                                    builder: (context, value, _) {
                                      return Row(
                                        children: [
                                          Wrap(
                                            alignment: WrapAlignment.start,
                                            runAlignment: WrapAlignment.start,
                                            spacing: 12,
                                            children: List.generate(
                                                addressTypes.length, (i) {
                                              Map<String, String> item =
                                                  addressTypes[i];
                                              bool isSelected = value
                                                      .toLowerCase() ==
                                                  item.keys.first.toLowerCase();
                                              bool isEnabled = (item.keys.first
                                                          .toLowerCase() ==
                                                      'home')
                                                  ? !foundHome
                                                  : ((item.keys.first
                                                              .toLowerCase() ==
                                                          'work')
                                                      ? !foundWork
                                                      : true);

                                              return CustomChips(
                                                title: item.keys.first,
                                                icon: item.values.first,
                                                isSelected: isSelected,
                                                onTap: () {
                                                  AddressBloc
                                                      .selectedAddressType
                                                      .value = item.keys.first;
                                                },
                                                isEnabled: isEnabled,
                                              );
                                            }),
                                          ),
                                        ],
                                      );
                                    }),
                              ),
                              ValueListenableBuilder(
                                  valueListenable:
                                      AddressBloc.selectedAddressType,
                                  builder: (context, value, _) {
                                    return Visibility(
                                      visible: value.toLowerCase() == 'other',
                                      child: Padding(
                                        padding: EdgeInsets.only(bottom: 16.sp),
                                        child: CustomBorderedTextField(
                                          fieldManager:
                                              AddressBloc.addressNameController,
                                          label: 'Save address as',
                                          textInputAction: TextInputAction.next,
                                          keyboardType: TextInputType.name,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'[a-zA-Z0-9\s]')),
                                          ],
                                        ),
                                      ),
                                    );
                                  }),
                            ],
                          ),
                        );
                      }),
                  CustomBorderedTextField(
                    fieldManager: AddressBloc.flatNameController,
                    label: 'Flat / House no. / Building name*',
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.name,
                    validator: (value) {
                      ValidationState addressValidationState =
                          AppValidator.emptyStringValidator(value.trim(),
                              'Please enter Flat / House no. / Building name');

                      if (!addressValidationState.valid) {
                        AddressBloc.flatNameController
                            ?.throwError(addressValidationState.message ?? '');
                        // return;
                      } else {
                        AddressBloc.flatNameController?.throwError('');
                      }
                    },
                  ),
                  SizedBox(height: 16.sp),
                  CustomBorderedTextField(
                    fieldManager: AddressBloc.floorNoController,
                    label: 'Floor (optional)',
                    inputFormatters: [],
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.streetAddress,
                  ),
                  SizedBox(height: 16.sp),
                  CustomBorderedTextField(
                    fieldManager: AddressBloc.localityController,
                    label: 'Area / Sector / Locality*',
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                          RegExp(r'[a-zA-Z0-9\s,.-]')),
                    ],
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.streetAddress,
                    readOnly: true,
                    maxLines: 3,
                    minLines: 1,
                    suffixIcon: InkWell(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        getIt<AddressBloc>().add(AddressEvent.initMap());
                      },
                      child: Container(
                        height: 34.sp,
                        margin:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                        decoration: BoxDecoration(
                          color: AppColors.neutral100,
                          border: Border.all(
                              color: AppColors.primary500, width: 1.5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomText(
                              'Change',
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: AppColors.primary500,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.sp),
                  CustomBorderedTextField(
                    fieldManager: AddressBloc.landmarkController,
                    label: 'Nearby landmark (optional)',
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                          RegExp(r'[a-zA-Z0-9\s,.-]')),
                    ],
                    textInputAction: TextInputAction.done,
                    keyboardType: TextInputType.name,
                  ),
                ],
              )
            ],
          ),
          SizedBox(height: 12.sp),
          ValueListenableBuilder(
              valueListenable: AddressBloc.orderingFor,
              builder: (context, value, _) {
                bool forMySelf = value.toLowerCase() == 'myself';
                return AddressDetailCard(
                  titleText: forMySelf
                      ? 'Enter your details'
                      : 'Enter receiver’s details',
                  children: [
                    CustomBorderedTextField(
                      fieldManager: AddressBloc.contactNameController,
                      label: forMySelf ? 'Your name*' : 'Receiver’s name*',
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.name,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'[a-zA-Z\s]')),
                      ],
                      validator: (value) {
                        ValidationState contacNameValidation =
                            AppValidator.invalidStringValidator(
                                value.trim(), 'Please enter contact name',
                                invalidMessage:
                                    'Please enter valid contact name');

                        if (!contacNameValidation.valid) {
                          AddressBloc.contactNameController
                              ?.throwError(contacNameValidation.message ?? '');
                        } else {
                          AddressBloc.contactNameController?.throwError('');
                        }
                      },
                    ),
                    SizedBox(height: 16.sp),
                    CustomBorderedTextField(
                      fieldManager: AddressBloc.contactNumberController,
                      label: forMySelf ? '' : 'Receiver’s phone number*',
                      readOnly: forMySelf,
                      textInputAction: TextInputAction.done,
                      keyboardType: TextInputType.numberWithOptions(),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(10),
                      ],
                      validator: (value) {
                        ValidationState contacNumberValidation =
                            AppValidator.mobileNumberValidator(value.trim());
                        if (!contacNumberValidation.valid) {
                          AddressBloc.contactNumberController?.throwError(
                              contacNumberValidation.message ?? '');
                        } else {
                          AddressBloc.contactNumberController?.throwError('');
                        }
                      },
                      prefixIcon: Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              'assets/new/icons/flags.png',
                              width: 24.sp,
                              height: 18.sp,
                            ),
                            SizedBox(width: 4.sp),
                            CustomText(
                              '+91',
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: AppColors.neutral700,
                            ),
                            SizedBox(width: 4),
                          ],
                        ),
                      ),
                      suffixIcon: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Image.asset(
                          'assets/new/icons/contacts.png',
                          height: 24.sp,
                          width: 24.sp,
                        ),
                      ),
                    ),
                    SizedBox(height: 12.sp),
                    CustomText(
                      forMySelf
                          ? 'Editing these details will not change your profile data.'
                          : 'We’ll directly coordinate with them to deliver the order',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.neutral500,
                    ),
                  ],
                );
              }),
          // ElevatedButton(
          //   onPressed: _saveAddress,
          //   child: Text(
          //       widget.address?.id != null ? 'Update Address' : 'Save Address'),
          // ),
        ],
      ),
    );
  }
}

class AddressDetailCard extends StatelessWidget {
  const AddressDetailCard({
    super.key,
    this.cardColor,
    this.textPrimaryColor,
    this.dividerColor,
    this.titleText,
    required this.children,
  });

  final Color? cardColor;
  final Color? textPrimaryColor;
  final Color? dividerColor;
  final String? titleText;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(
        12.sp,
      ),
      decoration: BoxDecoration(
        color: cardColor ?? AppColors.neutral100,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            titleText ?? '',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: textPrimaryColor ?? AppColors.primary700,
          ),
          SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }
}

class CustomRadioButton extends StatelessWidget {
  const CustomRadioButton(
      {super.key, required this.isSelected, required this.title, this.onTap});
  final bool isSelected;
  final String title;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
      },
      child: Row(
        children: [
          Container(
            height: 24,
            width: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                width: isSelected ? 7 : 1.5,
                color: isSelected ? AppColors.primary600 : AppColors.primary200,
              ),
            ),
          ),
          SizedBox(width: 12),
          CustomText(
            title,
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
            color: isSelected ? AppColors.neutral700 : AppColors.neutral600,
          )
        ],
      ),
    );
  }
}

class CustomChips extends StatelessWidget {
  const CustomChips({
    super.key,
    required this.title,
    required this.icon,
    required this.isSelected,
    this.onTap,
    this.isEnabled = true,
  });

  final String title;
  final String icon;
  final bool isSelected;
  final void Function()? onTap;
  final bool isEnabled;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isEnabled) {
          HapticFeedback.lightImpact();
          onTap?.call();
        }
      },
      child: Opacity(
        opacity: isEnabled ? 1 : 0.6,
        child: Container(
          padding: EdgeInsets.fromLTRB(12, 8, 12, 6),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary500 : AppColors.neutral100,
            border: Border.all(color: AppColors.primary100, width: 1),
            borderRadius: BorderRadius.circular(99),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 4, 4, 4),
                child: CustomImage(
                  imageUrl: icon,
                  height: 16,
                  width: 16,
                  fallbackImage: 'assets/new/icons/pin_drop.png',
                  imageColor:
                      isSelected ? AppColors.neutral100 : AppColors.primary500,
                ),
              ),
              ConstrainedBox(
                constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width - 120),
                child: CustomText(
                  title,
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color:
                      isSelected ? AppColors.neutral100 : AppColors.primary500,
                  textHeight: 1,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
