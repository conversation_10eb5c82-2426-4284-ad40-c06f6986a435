import 'package:intl/intl.dart';

class TextFormatter {
  static String formatToUiText(String text) {
    if (text.isEmpty) return '';
    return text
        .split(RegExp(r'[-_]'))
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  static String getFormattedCategoryText(String text) {
    if (text.isEmpty) {
      return '';
    }

    // Handle '&' splitting and initial formatting
    List<String> parts = text.split('&');
    String formattedText;

    if (parts.length == 1) {
      formattedText = parts.first.trim();
    } else {
      // If the first part is 'home' (case-insensitive), use the second part.
      // Otherwise, use the first part.
      if (parts.first.trim().toLowerCase() == 'home') {
        formattedText = parts[1].trim();
      } else {
        formattedText = parts.first.trim();
      }
    }

    // Handle space splitting and word count logic
    List<String> words = formattedText.split(' ');

    if (words.length == 1) {
      return formattedText; // Single word, no further processing needed
    }

    // Limit to a maximum of two words for further processing
    if (words.length > 2) {
      words = words.sublist(0, 2);
    }

    String firstWord = words[0].trim();
    String secondWord = words[1].trim();

    // Special condition for "home" followed by a long second word
    if (firstWord.toLowerCase() == 'home' && secondWord.length > 5) {
      return secondWord;
    }

    // Combined length check for the first two words
    if ((firstWord.length + secondWord.length) > 12) {
      return firstWord.length > secondWord.length ? firstWord : secondWord;
    }

    // Default return if no specific conditions are met
    return formattedText;
  }
}

class DateTimeUtils {
  static bool isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}

String formatDeliveryDate(String isoDateString) {
  final date = DateTime.parse(isoDateString).toLocal();
  final now = DateTime.now();

  final today = DateTime(now.year, now.month, now.day);
  final tomorrow = today.add(Duration(days: 1));
  final dateOnly = DateTime(date.year, date.month, date.day);

  final timeString = DateFormat('h:mm a').format(date);

  if (dateOnly == today) {
    return 'Today, $timeString';
  } else if (dateOnly == tomorrow) {
    return 'Tomorrow, $timeString';
  } else if (dateOnly.isAfter(today) &&
      dateOnly.isBefore(today.add(Duration(days: 7)))) {
    final weekday = DateFormat('EEEE').format(date);
    return '$weekday, $timeString';
  } else {
    final dateString = DateFormat('d MMM').format(date);
    return '$dateString, $timeString';
  }
}
