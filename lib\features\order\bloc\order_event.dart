import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/domain/entities/order_item_entity.dart';

part 'order_event.freezed.dart';

@freezed
class OrderEvent with _$OrderEvent {
  /// Initialize order bloc
  const factory OrderEvent.init() = OrderInit;

  /// Load order history
  const factory OrderEvent.loadOrderHistory({
    @Default('customer_123') String customerId,
    @Default('') String status,
    @Default(0) int page,
    @Default(10) int pageSize,
    @Default(false) bool refresh,
  }) = LoadOrderHistory;

  /// Load more orders (pagination)
  const factory OrderEvent.loadMoreOrders() = LoadMoreOrders;

  /// Load order details
  const factory OrderEvent.loadOrderDetails(String orderId,
      {@Default(true) bool showLoader}) = LoadOrderDetails;

  /// Refresh order history
  const factory OrderEvent.refreshOrderHistory({
    @Default('') String status,
  }) = RefreshOrderHistory;


  /// Cancel order
  const factory OrderEvent.cancelOrder(String orderId) = CancelOrder;

  /// Clear order details
  const factory OrderEvent.clearOrderDetails() = ClearOrderDetails;

  /// Search orders
  const factory OrderEvent.searchOrders(String query) = SearchOrders;

  /// Reset to initial state
  const factory OrderEvent.reset() = ResetOrder;

  const factory OrderEvent.returnOrder(
      String orderId, OrderItemEntity item, String reason) = ReturnOrder;
      
  /// Order again - add all items from an order to cart
  const factory OrderEvent.orderAgain(OrderEntity order) = OrderAgain;
}
