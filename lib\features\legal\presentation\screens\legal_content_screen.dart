import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/legal_bloc.dart';
import '../../bloc/legal_event.dart';
import '../../bloc/legal_state.dart';
import '../../services/legal_content_service.dart';
import '../widgets/legal_content_widget.dart';
import 'package:rozana/core/extensions/localization_extension.dart';
import 'package:rozana/widgets/app_loader.dart';

class LegalContentScreen extends StatefulWidget {
  final LegalContentType contentType;

  const LegalContentScreen({
    super.key,
    required this.contentType,
  });

  @override
  State<LegalContentScreen> createState() => _LegalContentScreenState();
}

class _LegalContentScreenState extends State<LegalContentScreen> {
  late LegalBloc _legalBloc;

  @override
  void initState() {
    super.initState();
    _legalBloc = LegalBloc();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadContent();
  }

  void _loadContent() {
    // Get current language from context
    final currentLocale = Localizations.localeOf(context);
    final language = currentLocale.languageCode;

    _legalBloc.add(LoadLegalContent(
      contentType: widget.contentType,
      language: language,
    ));
  }

  @override
  void dispose() {
    _legalBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocProvider(
        create: (context) => _legalBloc,
        child: BlocBuilder<LegalBloc, LegalState>(
          builder: (context, state) {
            if (state is LegalLoading) {
              return const Center(
                child: AppLoader(),
              );
            } else if (state is LegalLoaded) {
              return LegalContentWidget(content: state.content);
            } else if (state is LegalError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      state.message,
                      style: Theme.of(context).textTheme.bodyLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadContent,
                      child: Text(context.l10n.retry),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  String _getAppBarTitle() {
    switch (widget.contentType) {
      case LegalContentType.aboutUs:
        return context.l10n.aboutUs;
      case LegalContentType.privacyPolicy:
        return context.l10n.privacyPolicy;
      case LegalContentType.termsConditions:
        return context.l10n.termsAndConditions;
    }
  }
}
