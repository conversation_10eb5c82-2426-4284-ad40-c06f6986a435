import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/category_entity.dart';

part 'product_listing_event.freezed.dart';

@freezed
class ProductListingEvent with _$ProductListingEvent {
  const factory ProductListingEvent.initial({
    CategoryEntity? category,
  }) = _Initial;

  const factory ProductListingEvent.selectSubcategory({
    CategoryEntity? subCategory,
  }) = _SelectSubcategory;
   const factory ProductListingEvent.reset() = _Reset;
}
