import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/core/extensions/localization_extension.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/utils/notifier.dart';
import 'refreshable_address_bar.dart';

class CustomSliverAppBarContent extends StatelessWidget {
  const CustomSliverAppBarContent(
      {super.key,
      required this.iconPrimaryColor,
      required this.iconSecondaryColor});

  final Color iconPrimaryColor;
  final Color iconSecondaryColor;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Column(
        mainAxisSize: MainAxisSize.min, // Make column as small as possible
        children: [
          // TOP LOCATION/DELIVERY BAR
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: RefreshableAddressBar(
                  // height: 42,
                  backgroundColor: Colors.transparent,
                  textColor: AppColors.white,
                  onAddressChanged: (address) {},
                  iconPrimaryColor: iconPrimaryColor,
                  iconSecondaryColor: iconSecondaryColor,
                ),
              ),
              const SizedBox(width: 10),
              BlocBuilder<LanguageBloc, LanguageState>(
                builder: (context, languageState) {
                  final currentLanguage = languageState.mapOrNull(
                        loaded: (state) => state.languageCode,
                      ) ??
                      'en';
                  return Tooltip(
                    message: context.l10n.language,
                    child: InkWell(
                      splashColor: Colors.transparent,
                      onTap: () {
                        // Toggle between English and Hindi
                        context
                            .read<LanguageBloc>()
                            .add(const LanguageEvent.toggleLanguage());
                        HapticFeedback.lightImpact();

                        // Show feedback to user

                        final newLanguage =
                            currentLanguage == 'en' ? 'hi' : 'en';
                        final languageName =
                            newLanguage == 'en' ? 'English' : 'हिंदी';

                        getIt<AppNotifier>().showSnackBar(
                            'Language switched to $languageName',
                            margin: EdgeInsets.zero);
                      },
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Stack(
                              alignment: Alignment.bottomRight,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      right: 10, bottom: 10),
                                  child: Image.asset(
                                    (currentLanguage == 'en')
                                        ? 'assets/new/icons/language_en.png'
                                        : 'assets/new/icons/language_hi.png',
                                    height: 12,
                                    width: 12,
                                    color: iconPrimaryColor,
                                  ),
                                ),
                                Image.asset(
                                  (currentLanguage == 'en')
                                      ? 'assets/new/icons/language_hi.png'
                                      : 'assets/new/icons/language_en.png',
                                  height: 12,
                                  width: 12,
                                  color: iconSecondaryColor,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              SizedBox(width: AppDimensions.screenHzPadding),
            ],
          ),
          SizedBox(height: 4),
        ],
      ),
    );
  }
}
