// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_listing_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductListingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ProductListingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingEvent()';
  }
}

/// @nodoc
class $ProductListingEventCopyWith<$Res> {
  $ProductListingEventCopyWith(
      ProductListingEvent _, $Res Function(ProductListingEvent) __);
}

/// Adds pattern-matching-related methods to [ProductListingEvent].
extension ProductListingEventPatterns on ProductListingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SelectSubcategory value)? selectSubcategory,
    TResult Function(_LoadProductsByCategory value)? loadProductsByCategory,
    TResult Function(_LoadProductsByBrand value)? loadProductsByBrand,
    TResult Function(_Reset value)? reset,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _SelectSubcategory() when selectSubcategory != null:
        return selectSubcategory(_that);
      case _LoadProductsByCategory() when loadProductsByCategory != null:
        return loadProductsByCategory(_that);
      case _LoadProductsByBrand() when loadProductsByBrand != null:
        return loadProductsByBrand(_that);
      case _Reset() when reset != null:
        return reset(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SelectSubcategory value) selectSubcategory,
    required TResult Function(_LoadProductsByCategory value)
        loadProductsByCategory,
    required TResult Function(_LoadProductsByBrand value) loadProductsByBrand,
    required TResult Function(_Reset value) reset,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _SelectSubcategory():
        return selectSubcategory(_that);
      case _LoadProductsByCategory():
        return loadProductsByCategory(_that);
      case _LoadProductsByBrand():
        return loadProductsByBrand(_that);
      case _Reset():
        return reset(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SelectSubcategory value)? selectSubcategory,
    TResult? Function(_LoadProductsByCategory value)? loadProductsByCategory,
    TResult? Function(_LoadProductsByBrand value)? loadProductsByBrand,
    TResult? Function(_Reset value)? reset,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _SelectSubcategory() when selectSubcategory != null:
        return selectSubcategory(_that);
      case _LoadProductsByCategory() when loadProductsByCategory != null:
        return loadProductsByCategory(_that);
      case _LoadProductsByBrand() when loadProductsByBrand != null:
        return loadProductsByBrand(_that);
      case _Reset() when reset != null:
        return reset(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CategoryEntity? category)? initial,
    TResult Function(CategoryEntity? subCategory)? selectSubcategory,
    TResult Function(String categoryId, String excludeProductId, int page,
            int pageSize, bool refresh)?
        loadProductsByCategory,
    TResult Function(String brandId, String excludeProductId, int page,
            int pageSize, bool refresh)?
        loadProductsByBrand,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that.category);
      case _SelectSubcategory() when selectSubcategory != null:
        return selectSubcategory(_that.subCategory);
      case _LoadProductsByCategory() when loadProductsByCategory != null:
        return loadProductsByCategory(_that.categoryId, _that.excludeProductId,
            _that.page, _that.pageSize, _that.refresh);
      case _LoadProductsByBrand() when loadProductsByBrand != null:
        return loadProductsByBrand(_that.brandId, _that.excludeProductId,
            _that.page, _that.pageSize, _that.refresh);
      case _Reset() when reset != null:
        return reset();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(CategoryEntity? category) initial,
    required TResult Function(CategoryEntity? subCategory) selectSubcategory,
    required TResult Function(String categoryId, String excludeProductId,
            int page, int pageSize, bool refresh)
        loadProductsByCategory,
    required TResult Function(String brandId, String excludeProductId, int page,
            int pageSize, bool refresh)
        loadProductsByBrand,
    required TResult Function() reset,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that.category);
      case _SelectSubcategory():
        return selectSubcategory(_that.subCategory);
      case _LoadProductsByCategory():
        return loadProductsByCategory(_that.categoryId, _that.excludeProductId,
            _that.page, _that.pageSize, _that.refresh);
      case _LoadProductsByBrand():
        return loadProductsByBrand(_that.brandId, _that.excludeProductId,
            _that.page, _that.pageSize, _that.refresh);
      case _Reset():
        return reset();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CategoryEntity? category)? initial,
    TResult? Function(CategoryEntity? subCategory)? selectSubcategory,
    TResult? Function(String categoryId, String excludeProductId, int page,
            int pageSize, bool refresh)?
        loadProductsByCategory,
    TResult? Function(String brandId, String excludeProductId, int page,
            int pageSize, bool refresh)?
        loadProductsByBrand,
    TResult? Function()? reset,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that.category);
      case _SelectSubcategory() when selectSubcategory != null:
        return selectSubcategory(_that.subCategory);
      case _LoadProductsByCategory() when loadProductsByCategory != null:
        return loadProductsByCategory(_that.categoryId, _that.excludeProductId,
            _that.page, _that.pageSize, _that.refresh);
      case _LoadProductsByBrand() when loadProductsByBrand != null:
        return loadProductsByBrand(_that.brandId, _that.excludeProductId,
            _that.page, _that.pageSize, _that.refresh);
      case _Reset() when reset != null:
        return reset();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements ProductListingEvent {
  const _Initial({this.category});

  final CategoryEntity? category;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitialCopyWith<_Initial> get copyWith =>
      __$InitialCopyWithImpl<_Initial>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Initial &&
            (identical(other.category, category) ||
                other.category == category));
  }

  @override
  int get hashCode => Object.hash(runtimeType, category);

  @override
  String toString() {
    return 'ProductListingEvent.initial(category: $category)';
  }
}

/// @nodoc
abstract mixin class _$InitialCopyWith<$Res>
    implements $ProductListingEventCopyWith<$Res> {
  factory _$InitialCopyWith(_Initial value, $Res Function(_Initial) _then) =
      __$InitialCopyWithImpl;
  @useResult
  $Res call({CategoryEntity? category});
}

/// @nodoc
class __$InitialCopyWithImpl<$Res> implements _$InitialCopyWith<$Res> {
  __$InitialCopyWithImpl(this._self, this._then);

  final _Initial _self;
  final $Res Function(_Initial) _then;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? category = freezed,
  }) {
    return _then(_Initial(
      category: freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
    ));
  }
}

/// @nodoc

class _SelectSubcategory implements ProductListingEvent {
  const _SelectSubcategory({this.subCategory});

  final CategoryEntity? subCategory;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectSubcategoryCopyWith<_SelectSubcategory> get copyWith =>
      __$SelectSubcategoryCopyWithImpl<_SelectSubcategory>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectSubcategory &&
            (identical(other.subCategory, subCategory) ||
                other.subCategory == subCategory));
  }

  @override
  int get hashCode => Object.hash(runtimeType, subCategory);

  @override
  String toString() {
    return 'ProductListingEvent.selectSubcategory(subCategory: $subCategory)';
  }
}

/// @nodoc
abstract mixin class _$SelectSubcategoryCopyWith<$Res>
    implements $ProductListingEventCopyWith<$Res> {
  factory _$SelectSubcategoryCopyWith(
          _SelectSubcategory value, $Res Function(_SelectSubcategory) _then) =
      __$SelectSubcategoryCopyWithImpl;
  @useResult
  $Res call({CategoryEntity? subCategory});
}

/// @nodoc
class __$SelectSubcategoryCopyWithImpl<$Res>
    implements _$SelectSubcategoryCopyWith<$Res> {
  __$SelectSubcategoryCopyWithImpl(this._self, this._then);

  final _SelectSubcategory _self;
  final $Res Function(_SelectSubcategory) _then;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? subCategory = freezed,
  }) {
    return _then(_SelectSubcategory(
      subCategory: freezed == subCategory
          ? _self.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as CategoryEntity?,
    ));
  }
}

/// @nodoc

class _LoadProductsByCategory implements ProductListingEvent {
  const _LoadProductsByCategory(
      {required this.categoryId,
      required this.excludeProductId,
      required this.page,
      required this.pageSize,
      required this.refresh});

  final String categoryId;
  final String excludeProductId;
  final int page;
  final int pageSize;
  final bool refresh;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadProductsByCategoryCopyWith<_LoadProductsByCategory> get copyWith =>
      __$LoadProductsByCategoryCopyWithImpl<_LoadProductsByCategory>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadProductsByCategory &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.excludeProductId, excludeProductId) ||
                other.excludeProductId == excludeProductId) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, categoryId, excludeProductId, page, pageSize, refresh);

  @override
  String toString() {
    return 'ProductListingEvent.loadProductsByCategory(categoryId: $categoryId, excludeProductId: $excludeProductId, page: $page, pageSize: $pageSize, refresh: $refresh)';
  }
}

/// @nodoc
abstract mixin class _$LoadProductsByCategoryCopyWith<$Res>
    implements $ProductListingEventCopyWith<$Res> {
  factory _$LoadProductsByCategoryCopyWith(_LoadProductsByCategory value,
          $Res Function(_LoadProductsByCategory) _then) =
      __$LoadProductsByCategoryCopyWithImpl;
  @useResult
  $Res call(
      {String categoryId,
      String excludeProductId,
      int page,
      int pageSize,
      bool refresh});
}

/// @nodoc
class __$LoadProductsByCategoryCopyWithImpl<$Res>
    implements _$LoadProductsByCategoryCopyWith<$Res> {
  __$LoadProductsByCategoryCopyWithImpl(this._self, this._then);

  final _LoadProductsByCategory _self;
  final $Res Function(_LoadProductsByCategory) _then;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categoryId = null,
    Object? excludeProductId = null,
    Object? page = null,
    Object? pageSize = null,
    Object? refresh = null,
  }) {
    return _then(_LoadProductsByCategory(
      categoryId: null == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      excludeProductId: null == excludeProductId
          ? _self.excludeProductId
          : excludeProductId // ignore: cast_nullable_to_non_nullable
              as String,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      refresh: null == refresh
          ? _self.refresh
          : refresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _LoadProductsByBrand implements ProductListingEvent {
  const _LoadProductsByBrand(
      {required this.brandId,
      required this.excludeProductId,
      required this.page,
      required this.pageSize,
      required this.refresh});

  final String brandId;
  final String excludeProductId;
  final int page;
  final int pageSize;
  final bool refresh;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadProductsByBrandCopyWith<_LoadProductsByBrand> get copyWith =>
      __$LoadProductsByBrandCopyWithImpl<_LoadProductsByBrand>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadProductsByBrand &&
            (identical(other.brandId, brandId) || other.brandId == brandId) &&
            (identical(other.excludeProductId, excludeProductId) ||
                other.excludeProductId == excludeProductId) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, brandId, excludeProductId, page, pageSize, refresh);

  @override
  String toString() {
    return 'ProductListingEvent.loadProductsByBrand(brandId: $brandId, excludeProductId: $excludeProductId, page: $page, pageSize: $pageSize, refresh: $refresh)';
  }
}

/// @nodoc
abstract mixin class _$LoadProductsByBrandCopyWith<$Res>
    implements $ProductListingEventCopyWith<$Res> {
  factory _$LoadProductsByBrandCopyWith(_LoadProductsByBrand value,
          $Res Function(_LoadProductsByBrand) _then) =
      __$LoadProductsByBrandCopyWithImpl;
  @useResult
  $Res call(
      {String brandId,
      String excludeProductId,
      int page,
      int pageSize,
      bool refresh});
}

/// @nodoc
class __$LoadProductsByBrandCopyWithImpl<$Res>
    implements _$LoadProductsByBrandCopyWith<$Res> {
  __$LoadProductsByBrandCopyWithImpl(this._self, this._then);

  final _LoadProductsByBrand _self;
  final $Res Function(_LoadProductsByBrand) _then;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? brandId = null,
    Object? excludeProductId = null,
    Object? page = null,
    Object? pageSize = null,
    Object? refresh = null,
  }) {
    return _then(_LoadProductsByBrand(
      brandId: null == brandId
          ? _self.brandId
          : brandId // ignore: cast_nullable_to_non_nullable
              as String,
      excludeProductId: null == excludeProductId
          ? _self.excludeProductId
          : excludeProductId // ignore: cast_nullable_to_non_nullable
              as String,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      refresh: null == refresh
          ? _self.refresh
          : refresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _Reset implements ProductListingEvent {
  const _Reset();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Reset);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductListingEvent.reset()';
  }
}

// dart format on
