class PlaceSuggestionModel {
  String? placeId;
  String? description;
  String? mainText;
  String? secondaryText;
  List<String>? types;

  PlaceSuggestionModel({
    this.placeId,
    this.description,
    this.mainText,
    this.secondaryText,
    this.types,
  });

  PlaceSuggestionModel.fromJson(Map<String, dynamic> json) {
    placeId = json['placeId']?.toString();
    description = json['description']?.toString();
    mainText = json['mainText']?.toString();
    secondaryText = json['secondaryText']?.toString();
    if (json['types'] is List) {
      types = <String>[];
      json['types'].forEach((v) {
        if (v != null) {
          types!.add(v.toString());
        }
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (placeId is String) {
      data['placeId'] = placeId;
    }
    if (description is String) {
      data['description'] = description;
    }
    if (mainText is String) {
      data['mainText'] = mainText;
    }
    if (secondaryText is String) {
      data['secondaryText'] = secondaryText;
    }
    if (types is List) {
      data['types'] = <String>[];
      types?.forEach((v) {
        data['types']!.add(v.toString());
      });
    }
    return data;
  }
}
