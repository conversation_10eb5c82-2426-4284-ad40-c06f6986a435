import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/services/user_profile_service.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/features/profile/presentation/widgets/profile_setup_modal.dart';
import 'package:rozana/features/search/presentation/screens/search_screen.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_border.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:sms_autofill/sms_autofill.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../core/utils/text_field_manager.dart';
import '../../../../widgets/custom_textfield.dart';
import '../../bloc/login_bloc/login_bloc.dart';
import 'package:rozana/core/services/analytics_events/app_analytics_events.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with
        SingleTickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<LoginScreen> {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String? _returnRoute;
  String? _forcedReturnRoute;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _animationController.forward();

    // Extract return route from navigation arguments and listen for auth changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final Object? extra = GoRouterState.of(context).extra;
      if (extra != null && extra is Map<String, dynamic>) {
        _returnRoute = extra['returnRoute'] as String?;
      }

      final appBloc = context.read<AppBloc>();
      appBloc.stream.listen((state) {
        state.maybeMap(
            loaded: (loaded) {
              if (loaded.isAuthenticated && mounted) {
                // Check if profile setup is needed
                _checkAndShowProfileSetup();
              }
            },
            orElse: () {});
      });
    });
  }

  Future<void> _checkAndShowProfileSetup() async {
    try {
      final userProfileService = UserProfileService();
      final isProfileComplete = await userProfileService.isProfileComplete();

      if (!isProfileComplete && mounted) {
        // Show modal first while context is still mounted
        try {
          await showProfileSetupModal(context);

          // Navigate to destination regardless of modal result
          if (mounted) {
            _navigateToDestination();
          }
        } catch (modalError) {
          // Even if modal fails, still navigate to destination
          if (mounted) {
            _navigateToDestination();
          }
        }
      } else if (mounted) {
        // Profile is complete, navigate normally
        _navigateToDestination();
      }
    } catch (e) {
      // Error checking profile, navigate to destination anyway
      if (mounted) {
        _navigateToDestination();
      }
    }
  }

  void _navigateToDestination() {
    if (_returnRoute != null) {
      // Replace login screen with destination to avoid back to login
      // context.pushReplacement(_returnRoute!);
      context.replace(_returnRoute!);
      if (_returnRoute == RouteNames.cart) {
        context.go(RouteNames.home);
        context.push(RouteNames.cart);
      }
    } else if (context.canPop()) {
      // Go back if possible
      context.pop();
    } else {
      // Default to home screen
      context.go(RouteNames.home);
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    return SafeArea(
      top: false,
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          context.read<LoginBloc>().state.map(
            initial: (_) {
              if (_forcedReturnRoute?.isNotEmpty ?? false) {
                context.go(_forcedReturnRoute!);
              } else if (context.canPop()) {
                context.pop();
              } else {
                context.go(RouteNames.home);
              }
            },
            otp: (_) {
              context.read<LoginBloc>().add(LoginEvent.loginFailed());
            },
          );
        },
        child: BlocBuilder<LoginBloc, LoginState>(
          builder: (context, state) {
            return state.map(initial: (initialState) {
              return Scaffold(
                backgroundColor: theme.colorScheme.surface,
                body: LayoutBuilder(builder: (context, constraints) {
                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height -
                              ((MediaQuery.of(context).viewPadding.vertical)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              CustomBorder(
                                color: AppColors.neutral150,
                                radius: 20,
                                child: SizedBox(
                                    height: 80.sp,
                                    width: 80.sp,
                                    child: Center(
                                      child: Image.asset(
                                        'assets/images/Rozana_logo.webp',
                                      ),
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: AppDimensions.screenHzPadding),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    SizedBox(height: 24.sp),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child: SlideTransition(
                                        position: _slideAnimation,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8.sp),
                                          child: CustomText(
                                            'Roz ki kharidaari, Rozana ke saath',
                                            fontSize: 28,
                                            fontWeight: FontWeight.w800,
                                            color: AppColors.primary700,
                                            textHeight: 1.3,
                                            maxLines: 2,
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 6.sp),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child: SlideTransition(
                                        position: _slideAnimation,
                                        child: CustomText(
                                          'Log in or sign up',
                                          color: AppColors.neutral500,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 24.sp),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child: SlideTransition(
                                        position: _slideAnimation,
                                        child: CustomField(state: state),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child: SlideTransition(
                                        position: _slideAnimation,
                                        child: SizedBox(
                                          width: double.infinity,
                                          height: 48.sp,
                                          child: CustomButton(
                                            isLoading: state.isLoading,
                                            backgroundColor:
                                                AppColors.primary500,
                                            onPressed: () async {
                                              if (state.isLoading) return;

                                              try {
                                                context.read<LoginBloc>().add(
                                                    LoginEvent.submitLogin());
                                              } catch (e) {
                                                debugPrint(
                                                    'Error tracking continue button clicked: $e');
                                              }
                                            },
                                            text: state.map(
                                                initial: (_) => 'Continue',
                                                otp: (value) =>
                                                    'Verify & Proceed'),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(top: 16.sp),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              Spacer(
                                                flex: 2,
                                              ),
                                              Expanded(
                                                  flex: 3,
                                                  child: DottedLine(
                                                    color: AppColors.primary100,
                                                  )),
                                              Padding(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 8.sp),
                                                child: CustomText(
                                                  'OR',
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors.primary700,
                                                ),
                                              ),
                                              Expanded(
                                                  flex: 3,
                                                  child: DottedLine(
                                                    color: AppColors.primary100,
                                                  )),
                                              Spacer(
                                                flex: 2,
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 16.sp),
                                          SizedBox(
                                            width: double.infinity,
                                            height: 48.sp,
                                            child: CustomButton(
                                              isOutlined: true,
                                              onPressed: () {
                                                HapticFeedback.lightImpact();
                                                AppAnalyticsEvents()
                                                    .trackTakeMeToHome();
                                                _forcedReturnRoute =
                                                    RouteNames.home;
                                                //context.pop();
                                                context.go(
                                                    _forcedReturnRoute!); // Navigate to destination
                                              },
                                              text: 'Take me to Home',
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 24.sp),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child: Center(
                                          child: Text.rich(
                                        TextSpan(
                                            text:
                                                'By continuing, you agree to our:',
                                            children: [
                                              TextSpan(
                                                  text: ' Terms of service ',
                                                  style: TextStyle(
                                                    color: AppColors.primary400,
                                                  ),
                                                  recognizer:
                                                      TapGestureRecognizer()
                                                        ..onTap = () {
                                                          HapticFeedback
                                                              .lightImpact();
                                                          context.push(RouteNames
                                                              .termsConditions);
                                                        }),
                                              TextSpan(
                                                text: 'and',
                                              ),
                                              TextSpan(
                                                  text: ' Privacy policy.',
                                                  style: TextStyle(
                                                    color: AppColors.primary400,
                                                  ),
                                                  recognizer:
                                                      TapGestureRecognizer()
                                                        ..onTap = () {
                                                          HapticFeedback
                                                              .lightImpact();
                                                          context.push(RouteNames
                                                              .privacyPolicy);
                                                        }),
                                            ]),
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontFamily: 'Mukta',
                                          fontSize: 10.sp,
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.neutral500,
                                        ),
                                      )),
                                    ),
                                    SizedBox(height: 12.sp),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              );
            }, otp: (otpState) {
              return Scaffold(
                appBar: AppBar(
                  backgroundColor: AppColors.neutral100,
                  scrolledUnderElevation: 0,
                  elevation: 0,
                  bottom: PreferredSize(
                      preferredSize: Size(
                        MediaQuery.of(context).size.width,
                        1,
                      ),
                      child: Divider(
                        height: 1,
                        thickness: 1,
                        color: AppColors.neutral150,
                      )),
                  forceMaterialTransparency: true,
                  centerTitle: false,
                  titleSpacing: 0,
                  leadingWidth: 0,
                  automaticallyImplyLeading: false,
                  title: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () {
                          context
                              .read<LoginBloc>()
                              .add(LoginEvent.loginFailed());
                        },
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                          child: Image.asset(
                            'assets/new/icons/chevron_left.png',
                            height: 26,
                            width: 26,
                            color: AppColors.primary700,
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: CustomText(
                          "OTP Verification",
                          color: AppColors.primary700,
                          fontSize: 20,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ],
                  ),
                ),
                body: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: 24.h),
                      CustomText(
                        'We have sent a verification code to',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.neutral500,
                      ),
                      SizedBox(height: 2.sp),
                      CustomText(
                        '+91 - ${otpState.mobile}',
                        textAlign: TextAlign.center,
                        color: AppColors.neutral700,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                      SizedBox(height: 40.sp),
                      Row(
                        children: List.generate(
                            6,
                            (i) => Padding(
                                  padding:
                                      EdgeInsets.only(right: i < 5 ? 6.sp : 0),
                                  child: OTPField(
                                    fieldManager: LoginBloc.otpManagers[i],
                                    index: i,
                                    isReadOnly: otpState.isLoading,
                                    error: otpState.showError,
                                  ),
                                )),
                      ),
                      SizedBox(height: 40.sp),
                      TextButton(
                        onPressed: otpState.canResend
                            ? () => context
                                .read<LoginBloc>()
                                .add(const LoginEvent.resendOTP())
                            : null,
                        child: IntrinsicWidth(
                          child: Column(
                            children: [
                              CustomText(
                                otpState.canResend
                                    ? 'Resend OTP'
                                    : 'Resend OTP in ${otpState.resendSeconds}s',
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                                color: otpState.canResend
                                    ? AppColors.primary600
                                    : AppColors.primary200,
                              ),
                              SizedBox(height: 2.sp),
                              SizedBox(
                                width: double.infinity,
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 2),
                                  child: DottedLine(
                                    color: otpState.canResend
                                        ? AppColors.primary600
                                        : AppColors.primary200,
                                    height: 2,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 40.h),
                    ],
                  ),
                ),
              );
            });
          },
        ),
      ),
    );
  }
}

class CustomField extends StatelessWidget {
  const CustomField({super.key, required this.state});

  final LoginState state;

  @override
  Widget build(BuildContext context) {
    return !kIsWeb
        ? GestureDetector(
            onTap: () async {
              try {
                FocusManager.instance.primaryFocus?.unfocus();
                await Future<void>.delayed(const Duration(milliseconds: 100));
                final phoneNumber = await SmsAutoFill().hint;
                if (phoneNumber != null && phoneNumber.isNotEmpty) {
                  final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
                  final last10Digits = digits.length > 10
                      ? digits.substring(digits.length - 10)
                      : digits;
                  LoginBloc.mobileNumberManager?.text = last10Digits;
                }
              } catch (e) {
                debugPrint('Error getting phone hint: $e');
              }
            },
            child: AbsorbPointer(
              absorbing: false,
              child: RegularPhoneField(),
            ),
          )
        : RegularPhoneField();
  }
}

class RegularPhoneField extends StatelessWidget {
  const RegularPhoneField({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomBorderedTextField(
      fieldManager: LoginBloc.mobileNumberManager,
      label: 'Enter phone number',
      hintOnly: true,
      textInputAction: TextInputAction.done,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
      ],
      validator: (value) {
        ValidationState contacNumberValidation =
            AppValidator.mobileNumberValidator(value.trim());
        if (!contacNumberValidation.valid) {
          LoginBloc.mobileNumberManager
              ?.throwError(contacNumberValidation.message ?? '');
        } else {
          LoginBloc.mobileNumberManager?.throwError('');
        }
      },
      autofillHints: const [AutofillHints.telephoneNumber],
      onChanged: (value) {
        // Auto-submit when a valid 10-digit number is entered (for iOS autofill)
        if (value.length == 10 && !kIsWeb && Platform.isIOS) {
          // Delay slightly to ensure the field is fully populated
          Future.delayed(const Duration(milliseconds: 300), () {
            // ignore: use_build_context_synchronously
            context.read<LoginBloc>().add(LoginEvent.submitLogin());
          });
        }
      },
      prefixIcon: Padding(
        padding: const EdgeInsets.only(left: 16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/new/icons/flags.png',
              width: 24.sp,
              height: 18.sp,
            ),
            SizedBox(width: 4.sp),
            CustomText(
              '+91',
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.neutral700,
            ),
            SizedBox(width: 4),
          ],
        ),
      ),
    );
  }
}

class OTPField extends StatefulWidget {
  const OTPField({
    super.key,
    this.fieldManager,
    required this.index,
    required this.isReadOnly,
    required this.error,
  });
  final TextFieldManager? fieldManager;
  final int index;
  final bool isReadOnly;
  final bool error;

  @override
  State<OTPField> createState() => _OTPFieldState();
}

class _OTPFieldState extends State<OTPField> with WidgetsBindingObserver {
  bool isError = false;
  bool isFocused = false;

  @override
  void initState() {
    super.initState();
    isError = widget.error;
    widget.fieldManager?.focusNode.addListener(() {
      setState(() {
        isFocused = widget.fieldManager?.focusNode.hasFocus ?? false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    double width = ((MediaQuery.of(context).size.width -
            (AppDimensions.screenHzPadding * 2) -
            30.sp) /
        6);
    bool isFilled = widget.index == 0
        ? ((widget.fieldManager?.controller.text.length ?? 0) > 0)
        : ((widget.fieldManager?.controller.text.length ?? 1) > 1);
    bool isError = widget.error;
    return SizedBox.square(
      dimension: ((MediaQuery.of(context).size.width -
              (AppDimensions.screenHzPadding * 2) -
              30.sp) /
          6),
      child: CustomBorder(
        radius: 12,
        side: BorderSide(
            color: isError
                ? AppColors.red200
                : isFocused
                    ? AppColors.primary400
                    : AppColors.primary100,
            width: 1),
        color: isError ? AppColors.red100 : AppColors.neutral100,
        child: Center(
          child: TextField(
            controller: widget.fieldManager?.controller,
            focusNode: widget.fieldManager?.focusNode,
            onTapOutside: (_) {
              widget.fieldManager?.focusNode.unfocus();
            },
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w800,
              color: AppColors.neutral700,
              fontFamily: 'Mukta',
              height: 1,
            ),
            decoration: InputDecoration(
              contentPadding: EdgeInsets.fromLTRB(
                  (width / 2) -
                      (isFilled
                          ? (width / (widget.index == 0 ? 5 : 3))
                          : (width / (widget.index == 0 ? 15 : 5))),
                  (width * 0.05),
                  0,
                  16),
              border: TransparentInputBorder(),
              errorBorder: TransparentInputBorder(),
              enabledBorder: TransparentInputBorder(),
              focusedBorder: TransparentInputBorder(),
              alignLabelWithHint: true,
              fillColor: isError ? AppColors.red100 : AppColors.neutral100,
            ),
            scrollPadding: EdgeInsets.only(bottom: 200),
            keyboardType: TextInputType.number,
            // Add autofillHints for iOS OTP detection
            autofillHints:
                widget.index == 0 ? const [AutofillHints.oneTimeCode] : null,
            maxLength: widget.index == 0 ? 6 : 3,
            buildCounter: (context,
                    {required currentLength,
                    required isFocused,
                    required maxLength}) =>
                null,
            readOnly: widget.isReadOnly,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9\s]')),
            ],
            onTap: () {
              if ((widget.fieldManager?.text.isEmpty ?? false) &&
                  widget.index > 0) {
                widget.fieldManager?.text = ' ';
              }
            },
            onChanged: (value) {
              if (value.length == 6) {
                LoginBloc.otpManager?.text = value;
                for (int i = 0; i < LoginBloc.otpManagers.length; i++) {
                  LoginBloc.otpManagers[i].text = value[i];
                }
                context.read<LoginBloc>().add(LoginEvent.submitOTP());
                return;
              }
              if (value.length > 2) {
                widget.fieldManager?.text =
                    ' ${widget.fieldManager?.text.split('').last}';
              } else if (widget.index == 0 && value.length > 1) {
                widget.fieldManager?.text =
                    widget.fieldManager?.text.split('').last;
              }

              final String actualValue = value.trim();

              if (actualValue.isNotEmpty) {
                if (widget.index < 5) {
                  widget.fieldManager?.focusNode.nextFocus();
                } else {
                  widget.fieldManager?.focusNode.unfocus();
                }
                isError = false;
              } else if ((value.isEmpty && widget.index > 0) ||
                  (value == ' ' && widget.index > 0)) {
                widget.fieldManager?.focusNode.previousFocus();
              } else {
                widget.fieldManager?.focusNode.unfocus();
              }

              String otpText = LoginBloc.otpManagers
                  .map((e) => e.text.trim())
                  .toList()
                  .join();
              if (otpText.length == 6) {
                LoginBloc.otpManager?.text = otpText;
                context.read<LoginBloc>().add(LoginEvent.submitOTP());
              }
            },
          ),
        ),
      ),
    );
  }
}
