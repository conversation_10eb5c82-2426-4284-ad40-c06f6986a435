import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/domain/entities/product_entity.dart';
import 'package:rozana/features/home/<USER>/widgets/section_most_bought.dart';
import 'package:rozana/features/products/presentation/widgets/expansion_card.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';

import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/services/analytics_events/product_analytics_events.dart';
import '../../../../core/services/appflyer_services/app_flyer_deeplink.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../bloc/detail_bloc/product_detail_bloc.dart';
import '../../services/product_navigation_service.dart';
import '../widgets/product_section.dart';
import '../widgets/product_image_slider.dart';
import '../widgets/product_variant_selector.dart';
import '../widgets/collection_products_section.dart';

class ProductDetailPage extends StatelessWidget {
  const ProductDetailPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        ProductNavigationService.instance.pop();
      },
      child: SafeArea(
        top: false,
        child: BlocBuilder<ProductDetailBloc, ProductDetailState>(
          builder: (context, state) {
            bool isLoading = true;

            ProductEntity? productData;
            ProductEntity? displayProduct;
            state.maybeMap(
              loaded: (value) {
                isLoading = false;
                productData = value.product;
                displayProduct = value.selectedVariant;

                // Track PDP opened analytics
                if (productData != null) {
                  ProductAnalyticsEvents().trackPDPOpened(
                    productSkuId:
                        displayProduct?.skuID ?? productData!.skuID ?? '',
                    productName: productData!.name,
                    mrp: (displayProduct?.originalPrice ??
                            productData!.originalPrice ??
                            0)
                        .toString(),
                    sellingPrice: (displayProduct?.price ?? productData!.price)
                        .toString(),
                    discount: ((displayProduct?.originalPrice ??
                                productData!.originalPrice ??
                                0) -
                            (displayProduct?.price ?? productData!.price))
                        .toString(),
                    categoryName: productData!.category,
                    subCategoryName: productData!.subcategory ?? 'Unknown',
                    pdpLocation: 'Product Detail Screen',
                    ipAddress: null,
                    latitude: null,
                    longitude: null,
                  );
                }
              },
              orElse: () {
                isLoading = true;
              },
            );

            final List<ProductEntity> variants = productData?.variants ?? [];

            final String name = productData?.name ?? '--';
            final String productId =
                displayProduct?.id ?? productData?.id ?? '';
            final String sku =
                displayProduct?.skuID ?? productData?.skuID ?? '';
            final String description =
                productData?.description ?? 'No description available';
            final String imageUrl =
                displayProduct?.imageUrl ?? productData?.imageUrl ?? '';
            final List<String> photos =
                (displayProduct?.photos?.isNotEmpty ?? false)
                    ? displayProduct!.photos!
                    : (productData?.photos?.isNotEmpty ?? false)
                        ? productData!.photos!
                        : [imageUrl].where((url) => url.isNotEmpty).toList();
            final double price = displayProduct?.price.toDouble() ??
                productData?.price.toDouble() ??
                0.0;
            final double originalPrice =
                displayProduct?.originalPrice?.toDouble() ??
                    productData?.originalPrice?.toDouble() ??
                    price;
            final num availableQty =
                displayProduct?.availableQty ?? productData?.availableQty ?? 0;
            final num maxQty =
                displayProduct?.maxLimit ?? productData?.maxLimit ?? 0;
            final String? variantName =
                displayProduct?.variantName ?? productData?.variantName;

            final bool isReturnable = (productData?.isReturnable ?? false) &&
                (productData?.returnType == '10' ||
                    productData?.returnType == '11');

            final String source =
                displayProduct?.source ?? productData?.source ?? 'store';

            return Scaffold(
              backgroundColor: AppColors.neutral150,
              appBar: AppBar(
                backgroundColor: AppColors.neutral100,
                elevation: 1,
                foregroundColor: Colors.black,
                leading: IconButton(
                  icon: CustomImage(
                    imageUrl: 'assets/new/icons/close.png',
                    width: 24,
                    height: 24,
                    imageColor: AppColors.primary600,
                  ),
                  onPressed: () {
                    context.pop(); // or your custom action
                  },
                ),
                actions: [
                  if (isLoading)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: ShimmerBox(height: 30, width: 30),
                    )
                  else ...[
                    IconButton(
                      icon: CustomImage(
                        imageUrl: 'assets/new/icons/ios_share.png',
                        width: 24,
                        height: 24,
                        imageColor: AppColors.primary600,
                      ),
                      onPressed: () {
                        _createAndShareDeepLink(
                          productId,
                          name,
                          imageUrl,
                          description,
                          sku,
                          variantName,
                          price: price,
                          originalPrice: originalPrice,
                          productData: productData,
                        );
                      },
                    ),
                    IconButton(
                      icon: CustomImage(
                        imageUrl: 'assets/new/icons/search.png',
                        width: 24,
                        height: 24,
                        imageColor: AppColors.primary600,
                      ),
                      onPressed: () {
                        context.push('${RouteNames.search}?initialQuery=');
                      },
                    ),
                  ],
                ],
              ),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          isLoading
                              ? AspectRatio(
                                  aspectRatio: 1,
                                  child: ShimmerBox(
                                    width: double.infinity,
                                    radius: 0,
                                  ),
                                )
                              : ProductImageSlider(
                                  images: photos,
                                  aspectRatio: 1,
                                  showIndicators: true,
                                ),

                          _buildProductMainDetails(
                            displayProduct ?? productData,
                            variants: variants,
                            isLoading: isLoading,
                            onVariantSelected: (variant) {
                              context.read<ProductDetailBloc>().add(
                                  ProductDetailEvent.switchVariant(
                                      variant: variant));
                            },
                            selectedVariant: displayProduct,
                          ),

                          isLoading
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(24),
                                    ),
                                    child: Row(
                                      children: List.generate(
                                        4,
                                        (index) => Expanded(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 24, horizontal: 12),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                ShimmerBox(
                                                  height: 48,
                                                  width: 48,
                                                  radius: 24,
                                                ),
                                                const SizedBox(height: 8),
                                                ShimmerText(
                                                  height: 14,
                                                  width: 40,
                                                ),
                                                const SizedBox(height: 4),
                                                ShimmerText(
                                                  height: 12,
                                                  width: 50,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : const PromoCardRow(),
                          isLoading
                              ? AppCard(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  borderRadius: 16,
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ShimmerText(
                                        height: 18,
                                        width: 80,
                                      ),
                                      const SizedBox(height: 12),
                                      ShimmerText(
                                        height: 12,
                                        width: double.infinity,
                                      ),
                                      const SizedBox(height: 6),
                                      ShimmerText(
                                        height: 12,
                                        width: double.infinity,
                                      ),
                                      const SizedBox(height: 6),
                                      ShimmerText(
                                        height: 12,
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.7,
                                      ),
                                    ],
                                  ),
                                )
                              : (description.isNotEmpty
                                  ? ExpansionCard(
                                      title: 'Info',
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 12),
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              SizedBox(
                                                width: 100,
                                                child: CustomText(
                                                  'Description',
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w700,
                                                  color: AppColors.neutral500,
                                                ),
                                              ),
                                              Expanded(
                                                child: CustomText(
                                                  productData?.description ??
                                                      '',
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors.neutral500,
                                                  overflow:
                                                      TextOverflow.visible,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    )
                                  : const SizedBox.shrink()),

                          const SizedBox(height: 12),

                          // Similar Products Section
                          BlocBuilder<ProductDetailBloc, ProductDetailState>(
                            builder: (context, state) {
                              return state.maybeWhen(
                                loaded: (product,
                                    selectedVariant,
                                    similarProducts,
                                    isSimilarProductsLoading,
                                    hasMoreSimilarProducts,
                                    similarProductsPage) {
                                  // Show loading state for initial load
                                  if (isSimilarProductsLoading &&
                                      similarProducts.isEmpty) {
                                    return _buildSimilarProductsSection(
                                      context: context,
                                      child: ProductGrid2(
                                          productList: null), // Shows shimmer
                                    );
                                  }

                                  // Show similar products if available
                                  if (similarProducts.isNotEmpty) {
                                    return _buildSimilarProductsSection(
                                      context: context,
                                      child: _SimilarProductsGrid(
                                        products: similarProducts,
                                        isLoading: isSimilarProductsLoading,
                                        hasMore: hasMoreSimilarProducts,
                                        onLoadMore: () =>
                                            _loadMoreSimilarProducts(
                                                context, product),
                                      ),
                                    );
                                  }

                                  // Don't show anything if no similar products
                                  return const SizedBox.shrink();
                                },
                                orElse: () => const SizedBox.shrink(),
                              );
                            },
                          ),

                          // isLoading
                          //     ? AppCard(
                          //         margin: const EdgeInsets.symmetric(horizontal: 16),
                          //         backgroundColor: AppColors.yellow100,
                          //         borderRadius: 16,
                          //         padding: const EdgeInsets.all(16),
                          //         child: Column(
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             ShimmerText(
                          //               height: 18,
                          //               width: 150,
                          //             ),
                          //             const SizedBox(height: 16),
                          //             SizedBox(
                          //               height: 220,
                          //               child: ListView.builder(
                          //                 scrollDirection: Axis.horizontal,
                          //                 itemCount: 4,
                          //                 itemBuilder: (context, index) {
                          //                   return Container(
                          //                     width: 140,
                          //                     margin: const EdgeInsets.only(right: 12),
                          //                     child: ShimmerBox(
                          //                       height: 220,
                          //                       width: 140,
                          //                       radius: 12,
                          //                     ),
                          //                   );
                          //                 },
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       )
                          //     : (productData?.categoryId?.toString().isNotEmpty == true
                          //         ? AppCard(
                          //             margin: const EdgeInsets.symmetric(horizontal: 16),
                          //             backgroundColor: AppColors.yellow100,
                          //             borderRadius: 16,
                          //             child: ProductsSection.category(
                          //               categoryId: productData?.categoryId.toString() ?? '',
                          //               title: 'Similar products',
                          //               height: 250,
                          //               showSeeAll: false, // Don't show see all for similar products
                          //               excludeProductId: productData?.id.toString(),
                          //               padding: EdgeInsets.zero,
                          //             ),
                          //           )
                          //         : const SizedBox.shrink()),

                          const SizedBox(height: 12),

                          // Collection Products Section
                          isLoading
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ShimmerText(
                                        height: 18,
                                        width: 220,
                                      ),
                                      const SizedBox(height: 16),
                                      SizedBox(
                                        height: 220,
                                        child: ListView.builder(
                                          scrollDirection: Axis.horizontal,
                                          itemCount: 4,
                                          itemBuilder: (context, index) {
                                            return Container(
                                              width: 140,
                                              margin: const EdgeInsets.only(
                                                  right: 12),
                                              child: ShimmerBox(
                                                height: 220,
                                                width: 140,
                                                radius: 12,
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : (productData?.collectionId
                                          ?.toString()
                                          .isNotEmpty ==
                                      true
                                  ? CollectionProductsSection(
                                      collectionId:
                                          productData?.collectionId ?? '',
                                      categoryId: productData?.categoryId ?? '',
                                      title: 'Top products in this category',
                                      onSeeAllTap: () {},
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                    )
                                  : const SizedBox.shrink()),

                          // if ((!isLoading) &&
                          //     productData?.brandId?.toString().isNotEmpty ==
                          //         true)
                          //   Padding(
                          //     padding: const EdgeInsets.only(top: 32),
                          //     child: ProductsSection.brand(
                          //       brandId: productData?.brandId.toString() ?? '',
                          //       title: 'More from ${productData?.brandName}',
                          //       height: 250,
                          //       showSeeAll:
                          //           false, // Don't show see all for similar products
                          //       excludeProductId: productData?.id.toString(),
                          //     ),
                          //   ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              bottomNavigationBar: isLoading
                  ? Container(
                      constraints:
                          const BoxConstraints(minHeight: 80, maxHeight: 120),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            blurRadius: 6,
                            color: Colors.black12,
                            offset: Offset(0, -2),
                          ),
                        ],
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ShimmerText(
                                  height: 14,
                                  width: 120,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    ShimmerText(
                                      height: 16,
                                      width: 60,
                                    ),
                                    const SizedBox(width: 6),
                                    ShimmerText(
                                      height: 12,
                                      width: 50,
                                    ),
                                    const SizedBox(width: 6),
                                    ShimmerBox(
                                      height: 22,
                                      width: 60,
                                      radius: 4,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                ShimmerText(
                                  height: 10,
                                  width: 100,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(right: 16),
                            child: ShimmerBox(
                              height: 56,
                              width: 120,
                              radius: 28,
                            ),
                          ),
                        ],
                      ),
                    )
                  : addToCartBar(displayProduct ?? productData),
            );
          },
        ),
      ),
    );
  }

  /// Helper method to build similar products section (DRY principle)
  Widget _buildSimilarProductsSection({
    required BuildContext context,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            "Similar products",
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
          ),
        ),
        child,
      ],
    );
  }

  /// Helper method to load more similar products (DRY principle)
  void _loadMoreSimilarProducts(BuildContext context, ProductEntity product) {
    if (product.categoryId?.isNotEmpty == true) {
      context.read<ProductDetailBloc>().add(
            ProductDetailEvent.loadMoreSimilarProducts(
              categoryId: product.categoryId!,
              excludeProductId: product.id,
            ),
          );
    }
  }
}

Widget _buildProductMainDetails(
  ProductEntity? product, {
  required List<ProductEntity> variants,
  required bool isLoading,
  required Function(ProductEntity) onVariantSelected,
  ProductEntity? selectedVariant,
}) {
  if (isLoading) {
    return AppCard(
      backgroundColor: AppColors.neutral100,
      borderRadius: 16,
      elevation: 2,
      margin: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product title and veg icon
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ShimmerText(
                  height: 18,
                  width: double.infinity,
                  margin: EdgeInsets.zero,
                ),
              ),
              const SizedBox(width: 8),
              ShimmerBox(
                height: 20,
                width: 20,
                radius: 4,
              ),
            ],
          ),

          const SizedBox(height: 6),

          // Variant name
          ShimmerText(
            height: 14,
            width: 120,
            margin: EdgeInsets.zero,
          ),

          const SizedBox(height: 12),

          // Price section
          Row(
            children: [
              ShimmerText(
                height: 16,
                width: 60,
                margin: EdgeInsets.zero,
              ),
              const SizedBox(width: 6),
              ShimmerText(
                height: 12,
                width: 50,
                margin: EdgeInsets.zero,
              ),
              const SizedBox(width: 6),
              ShimmerBox(
                height: 22,
                width: 60,
                radius: 4,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Variant selector shimmer
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShimmerText(
                height: 14,
                width: 100,
                margin: const EdgeInsets.only(bottom: 8),
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(
                    3,
                    (index) => Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: ShimmerBox(
                        height: 40,
                        width: 80,
                        radius: 8,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  return AppCard(
    backgroundColor: AppColors.neutral100,
    borderRadius: 16,
    elevation: 2,
    margin: const EdgeInsets.only(left: 16, right: 16, top: 12),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Top row: Product title + veg icon
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: CustomText(
                product?.name ?? '--',
                overflow: TextOverflow.visible,
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: Colors.black,
              ),
            ),
            // Veg / Non-veg icon
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.green700, width: 1.5),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.green700,
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 6),
        CustomText(
          product?.variantName ?? '',
          color: AppColors.neutral600,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),

        const SizedBox(height: 4),

        // Variant Selector
        if (variants.isNotEmpty) ...[
          const SizedBox(height: 8),
          ProductVariantSelector(
            variants: variants,
            selectedVariant: selectedVariant,
            onVariantSelected: (variant) {
              // Call your parent callback first
              onVariantSelected(variant);

              // Track PDP size selected analytics
              if (selectedVariant != null) {
                ProductAnalyticsEvents().trackPDPSizeSelected(
                  productSkuId: variant.skuID ?? '',
                  productName: selectedVariant.name,
                  mrp: (variant.originalPrice ?? 0).toString(),
                  sellingPrice: (variant.price).toString(),
                  discount: ((variant.originalPrice ?? 0) - (variant.price))
                      .toString(),
                  categoryName: selectedVariant.category,
                  subCategoryName: selectedVariant.subcategory ?? 'Unknown',
                  sizeSelected: variant.variantName ?? '',
                  screenName: 'Product Detail Screen',
                );
              }
            },
            isLoading: isLoading,
          )
        ] else ...[
          // Price and discount
          Row(
            children: [
              CustomText(
                '₹${product?.price.round() ?? 0}',
                color: AppColors.neutral700,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
              const SizedBox(width: 6),
              if ((product?.originalPrice ?? 0) > (product?.price ?? 0))
                CustomText(
                  "₹${product?.originalPrice?.round() ?? 0}",
                  color: AppColors.neutral400,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.lineThrough,
                ),
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.blue100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: CustomText(
                    "${(100 - ((product?.price ?? 0) / (product?.originalPrice ?? 1) * 100)).round()}% OFF",
                    color: AppColors.blue600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ],
      ],
    ),
  );
}

/// Generates a Branch deep link and opens the native share sheet.
Future<void> _createAndShareDeepLink(
  String productId,
  String name,
  String imageUrl,
  String description,
  String sku,
  String? variantName, {
  double? price,
  double? originalPrice,
  ProductEntity? productData,
}) async {
  // Track product shared analytics
  ProductAnalyticsEvents().trackProductShared(
    productSkuId: sku,
    productName: name,
    mrp: (originalPrice ?? 0).toString(),
    sellingPrice: (price ?? 0).toString(),
    discount: ((originalPrice ?? 0) - (price ?? 0)).toString(),
    allAttributes: {
      'product_id': productId,
      'variant_name': variantName ?? '',
      'image_url': imageUrl,
      'description': description,
      'category': productData?.category ?? 'Unknown',
      'subcategory': productData?.subcategory ?? 'Unknown',
      'brand_name': productData?.brandName ?? 'Unknown',
      'facility_id': productData?.facilityId?.toString() ?? '',
      'facility_name': productData?.facilityName?.toString() ?? '',
    },
  );

  AppFlyerDeeplink.createDeepLink(
    data: {
      'screen': RouteNames.productDetail,
      'sku': sku,
      'variantName': variantName ?? ''
    },
    name: name,
  );
}

Widget addToCartBar(ProductEntity? product) {
  return Container(
    constraints: const BoxConstraints(minHeight: 72, maxHeight: 80),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    decoration: const BoxDecoration(
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          blurRadius: 6,
          color: Colors.black12,
          offset: Offset(0, -2),
        ),
      ],
    ),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                product?.variantName ?? '',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral600,
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  CustomText(
                    '₹${product?.price.round() ?? 0}',
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: AppColors.neutral700,
                  ),
                  const SizedBox(width: 6),
                  if ((product?.originalPrice ?? 0) > (product?.price ?? 0))
                    CustomText(
                      '₹${product?.originalPrice?.round() ?? 0}',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.neutral400,
                      decoration: TextDecoration.lineThrough,
                    ),
                  const SizedBox(width: 6),
                  if ((product?.originalPrice ?? 0) > (product?.price ?? 0))
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.blue100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: CustomText(
                        "${(100 - ((product?.price ?? 0) / (product?.originalPrice ?? 1) * 100)).round()}% OFF",
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.blue600,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 2),
              CustomText(
                "Inclusive of all taxes",
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral400,
              ),
            ],
          ),
        ),
        Center(
          child: QuantityTogglerWidget(
            height: 56,
            product: product,
            selectedProduct: product,
            showVariantsBottomSheet: false,
            isProductDetail: true,
            minWidth: 120,
            iconSize: 24,
            quantityFontSize: 18,
          ),
        ),
      ],
    ),
  );
}

class PromoCardRow extends StatelessWidget {
  const PromoCardRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Colors.white,
              Color(0xFFEDE7F6), // subtle gradient
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(24),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: const [
            PromoCard(
              imageUrl: "assets/new/icons/undo_primary.png", // return arrow
              title: "72 hours",
              subtitle: "Return",
            ),
            _PromoDivider(),
            PromoCard(
              imageUrl: "assets/new/icons/eco.png", // headset
              title: "Freshly",
              subtitle: "Sourced",
            ),
            _PromoDivider(),
            PromoCard(
              imageUrl: "assets/new/icons/support_agent.png", // headset
              title: "24/7",
              subtitle: "Support",
            ),
            _PromoDivider(),
            PromoCard(
              imageUrl:
                  "assets/new/icons/electric_moped.png", // delivery scooter
              title: "Fast",
              subtitle: "Delivery",
            ),
          ],
        ),
      ),
    );
  }
}

class PromoCard extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String subtitle;

  const PromoCard({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImage(
              imageUrl: imageUrl,
              width: 48,
              height: 48,
              imageColor: AppColors.primary600,
            ),
            const SizedBox(height: 8),
            CustomText(
              title,
              fontWeight: FontWeight.w700,
              fontSize: 14,
              color: AppColors.neutral700,
            ),
            const SizedBox(height: 4),
            CustomText(
              subtitle,
              fontSize: 12,
              color: AppColors.neutral500,
            ),
          ],
        ),
      ),
    );
  }
}

class _PromoDivider extends StatelessWidget {
  const _PromoDivider();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1,
      height: 150,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white,
            Color(0xFFD8D5EA),
          ],
        ),
      ),
    );
  }
}

class _SimilarProductsGrid extends StatefulWidget {
  final List<ProductEntity> products;
  final bool isLoading;
  final bool hasMore;
  final VoidCallback onLoadMore;

  const _SimilarProductsGrid({
    required this.products,
    required this.isLoading,
    required this.hasMore,
    required this.onLoadMore,
  });

  @override
  State<_SimilarProductsGrid> createState() => _SimilarProductsGridState();
}

class _SimilarProductsGridState extends State<_SimilarProductsGrid> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more when user is 200px from the end
      if (widget.hasMore && !widget.isLoading) {
        widget.onLoadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final double itemWidth = (MediaQuery.of(context).size.width / 3) - 16;

    return SizedBox(
      height: 250, // Fixed height for horizontal scroll
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemCount: widget.products.length + (widget.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= widget.products.length) {
            // Show loading indicator at the end
            return Container(
              width: itemWidth,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              child: widget.isLoading
                  ? ProductCardShimmer()
                  : const SizedBox.shrink(),
            );
          }

          return Container(
            width: itemWidth,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: DiscountedProductCard(
              product: widget.products[index],
              isLoading: false,
              sourceSection: 'similar_products',
            ),
          );
        },
      ),
    );
  }
}
