/// Domain entity representing a home banner
/// Pure business object without external dependencies
class BannerEntity {
  final String id;
  final String imageUrl;
  final String? collectionId;
  final String? name;

  const BannerEntity({
    required this.id,
    required this.imageUrl,
    this.collectionId,
    this.name,
  });

  /// Check if banner is linked to a category
  bool get hasCategory => collectionId != null && collectionId!.isNotEmpty;

  /// Create a copy with updated values
  BannerEntity copyWith({
    String? id,
    String? imageUrl,
    String? collectionId,
    String? name,
  }) {
    return BannerEntity(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      collectionId: collectionId ?? this.collectionId,
      name: name ?? this.name,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BannerEntity &&
        other.id == id &&
        other.imageUrl == imageUrl &&
        other.collectionId == collectionId &&
        other.name == name;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        imageUrl.hashCode ^
        collectionId.hashCode ^
        name.hashCode;
  }

  @override
  String toString() {
    return 'BannerEntity(id: $id, imageUrl: $imageUrl, collectionId: $collectionId, name: $name)';
  }
}
