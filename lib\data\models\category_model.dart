class CategoryModel {
  String? id;
  String? categoryId;
  String? name;
  num? count;
  String? imageUrl;
  String? icon;
  IconColor? iconColor;
  String? parentID;
  String? storeID;
  String? collectionId;

  CategoryModel({
    this.id,
    this.categoryId,
    this.name,
    this.count,
    this.imageUrl,
    this.icon,
    this.iconColor,
    this.parentID,
    this.storeID,
    this.collectionId,
  });

  CategoryModel.fromJson(Map<String, dynamic> json) {
    // Use objectID as id if id is not present
    id = json['id']?.toString();
    categoryId = json['category_id']?.toString();
    name = json['name']?.toString();
    // Try 'count' first, then 'productCount' for backward compatibility
    if (json['count'] is num) {
      count = json['count'];
    } else if (json['count'] != null) {
      var numb = num.tryParse(json['count']!.toString());
      if (numb is num) {
        count = numb;
      }
    } else if (json['productCount'] is num) {
      count = json['productCount'];
    } else if (json['productCount'] != null) {
      var numb = num.tryParse(json['productCount']!.toString());
      if (numb is num) {
        count = numb;
      }
    }
    imageUrl = json['imageUrl']?.toString();
    icon = json['icon']?.toString();
    if (json['iconColor'] is Map) {
      iconColor = IconColor.fromJson(json['iconColor']);
    }
    parentID = json['parentID']?.toString();
    storeID = json['storeID']?.toString();
    collectionId = json['collectionId']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (id is String) {
      data['id'] = id;
    }
    if (categoryId is String) {
      data['category_id'] = categoryId;
    }
    if (name is String) {
      data['name'] = name;
    }
    if (count is num) {
      data['count'] = count;
    } else if (count != null) {
      var numb = num.tryParse(count.toString());
      if (numb is num) {
        data['count'] = numb;
      }
    }
    if (imageUrl is String) {
      data['imageUrl'] = imageUrl;
    }
    if (icon is String) {
      data['icon'] = icon;
    }
    if (iconColor is IconColor) {
      data['iconColor'] = iconColor?.toJson();
    }
    if (parentID is String) {
      data['parentID'] = parentID;
    }
    if (storeID is String) {
      data['storeID'] = storeID;
    }
    if (collectionId is String) {
      data['collectionId'] = collectionId;
    }
    return data;
  }
}

class IconColor {
  num? alpha;
  num? red;
  num? green;
  num? blue;
  String? colorSpace;

  IconColor({
    this.alpha,
    this.red,
    this.green,
    this.blue,
    this.colorSpace,
  });

  IconColor.fromJson(Map<String, dynamic> json) {
    if (json['alpha'] is num) {
      alpha = json['alpha'];
    } else if (json['alpha'] != null) {
      var numb = num.tryParse(json['alpha']!.toString());
      if (numb is num) {
        alpha = numb;
      }
    }
    if (json['red'] is num) {
      red = json['red'];
    } else if (json['red'] != null) {
      var numb = num.tryParse(json['red']!.toString());
      if (numb is num) {
        red = numb;
      }
    }
    if (json['green'] is num) {
      green = json['green'];
    } else if (json['green'] != null) {
      var numb = num.tryParse(json['green']!.toString());
      if (numb is num) {
        green = numb;
      }
    }
    if (json['blue'] is num) {
      blue = json['blue'];
    } else if (json['blue'] != null) {
      var numb = num.tryParse(json['blue']!.toString());
      if (numb is num) {
        blue = numb;
      }
    }
    colorSpace = json['colorSpace']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (alpha is num) {
      data['alpha'] = alpha;
    } else if (alpha != null) {
      var numb = num.tryParse(alpha.toString());
      if (numb is num) {
        data['alpha'] = numb;
      }
    }
    if (red is num) {
      data['red'] = red;
    } else if (red != null) {
      var numb = num.tryParse(red.toString());
      if (numb is num) {
        data['red'] = numb;
      }
    }
    if (green is num) {
      data['green'] = green;
    } else if (green != null) {
      var numb = num.tryParse(green.toString());
      if (numb is num) {
        data['green'] = numb;
      }
    }
    if (blue is num) {
      data['blue'] = blue;
    } else if (blue != null) {
      var numb = num.tryParse(blue.toString());
      if (numb is num) {
        data['blue'] = numb;
      }
    }
    if (colorSpace is String) {
      data['colorSpace'] = colorSpace;
    }
    return data;
  }
}
