import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:rozana/data/models/place_suggestion_model.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../bloc/address_bloc.dart';

class AddressSearchView extends StatelessWidget {
  const AddressSearchView({
    super.key,
    required this.suggestions,
  });

  final List<PlaceSuggestionModel> suggestions;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: suggestions.isNotEmpty,
      replacement: Center(
          child: CustomText(
        'No results found.',
        color: AppColors.neutral400,
      )),
      child: ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.fromLTRB(16, 16, 16, 150),
        itemCount: suggestions.length,
        itemBuilder: (context, index) {
          final suggestion = suggestions[index];
          return PlacesListTile(
            place: suggestion,
          );
        },
        separatorBuilder: (context, index) => SizedBox(height: 12),
      ),
    );
  }
}

class PlacesListTile extends StatelessWidget {
  const PlacesListTile({
    super.key,
    this.place,
  });
  final PlaceSuggestionModel? place;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        FocusScope.of(context).unfocus();
        // Clear the search text when a result is selected
        AddressBloc.searchController?.clear();
        // Dispatch the select place event
        getIt<AddressBloc>()
            .add(AddressEvent.selectPlace(place?.placeId ?? ''));
        // Close the search results
        getIt<AddressBloc>().add(
          const AddressEvent.searchPlaces(''),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.neutral100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(12, 12, 12, 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 40.sp,
                    width: 40.sp,
                    decoration: BoxDecoration(
                      color: Color(0xFFF7F7F7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/new/icons/pin_drop.png',
                        height: 24.sp,
                        width: 24.sp,
                        color: AppColors.primary600,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          place?.mainText ?? '',
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: AppColors.primary700,
                        ),
                        CustomText(
                          place?.description ?? '',
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral500,
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
