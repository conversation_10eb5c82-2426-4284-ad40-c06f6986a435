class PaymentMethodModel {
  final String type;
  final double amount;
  final Map<String, dynamic>? metadata;

  static const String TYPE_WALLET = 'wallet';
  static const String TYPE_CASH = 'cash';
  static const String TYPE_ONLINE = 'online';

  PaymentMethodModel({
    required this.type,
    required this.amount,
    this.metadata,
  });

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      type: json['type'] ?? '',
      amount: _parseAmount(json['amount']),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'amount': amount,
      if (metadata != null) 'metadata': metadata,
    };
  }

  static double _parseAmount(dynamic amount) {
    if (amount == null) return 0.0;
    
    if (amount is num) {
      return amount.toDouble();
    }
    
    try {
      return double.parse(amount.toString());
    } catch (_) {
      return 0.0;
    }
  }
}
