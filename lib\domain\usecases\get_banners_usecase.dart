import 'package:rozana/domain/entities/banner_entity.dart';

import '../../../../data/services/data_loading_manager.dart';

/// Use case for getting home banners
/// Encapsulates the business logic for retrieving banners
class GetBannersUseCase {
  final DataLoadingManager _dataLoadingManager;

  const GetBannersUseCase(this._dataLoadingManager);

  /// Execute the use case to get banners
  ///
  /// Returns a list of [BannerEntity]
  Future<List<BannerEntity>> execute({String? level}) async {
    // Business logic can be added here
    // For example: validation, caching, filtering, etc.

    // Call repository to get data
    final banners = await _dataLoadingManager.getBanners(level: level);

    // Apply business rules
    // For example: filter out banners with invalid image URLs
    return banners;
  }
}
