import 'package:equatable/equatable.dart';
import 'package:rozana/domain/entities/order_entity.dart';

abstract class OrderTrackingState extends Equatable {
  const OrderTrackingState();

  @override
  List<Object?> get props => [];
}

class OrderTrackingInitial extends OrderTrackingState {
  const OrderTrackingInitial();
}

class OrderTrackingLoading extends OrderTrackingState {
  const OrderTrackingLoading();
}

class OrderTrackingLoaded extends OrderTrackingState {
  final List<OrderEntity> pendingOrders;

  const OrderTrackingLoaded({
    required this.pendingOrders,
  });

  @override
  List<Object?> get props => [pendingOrders];

  OrderTrackingLoaded copyWith({
    List<OrderEntity>? pendingOrders,
  }) {
    return OrderTrackingLoaded(
      pendingOrders: pendingOrders ?? this.pendingOrders,
    );
  }
}

class OrderTrackingError extends OrderTrackingState {
  final String message;

  const OrderTrackingError({required this.message});

  @override
  List<Object?> get props => [message];
}

class OrderTrackingEmpty extends OrderTrackingState {
  const OrderTrackingEmpty();
}
