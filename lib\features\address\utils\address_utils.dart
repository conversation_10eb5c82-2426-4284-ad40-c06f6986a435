import 'package:rozana/data/models/adress_model.dart';

class AddressUtils {
  /// Adds 30 minutes to the duration text from Google Maps API
  /// If the original duration is "2 mins", it will return "32 mins"
  static String addExtraTimeToDelivery(String originalDurationText) {
    final durationRegExp = RegExp(
        r'(?:(\d+)\s*day[s]?)?\s*(?:(\d+)\s*hour[s]?)?\s*(?:(\d+)\s*min[s]?)?');

    final match = durationRegExp.firstMatch(originalDurationText.toLowerCase());

    if (match == null) return originalDurationText; // fallback

    final days = int.tryParse(match.group(1) ?? '0') ?? 0;
    final hours = int.tryParse(match.group(2) ?? '0') ?? 0;
    final minutes = int.tryParse(match.group(3) ?? '0') ?? 0;

    final totalDuration =
        Duration(days: days, hours: hours, minutes: minutes + 30);

    // Break down the total duration into components again
    final newDays = totalDuration.inDays;
    final newHours = totalDuration.inHours % 24;
    final newMinutes = totalDuration.inMinutes % 60;

    final parts = <String>[];
    if (newDays > 0) parts.add('$newDays day${newDays > 1 ? 's' : ''}');
    if (newHours > 0) parts.add('$newHours hour${newHours > 1 ? 's' : ''}');
    if (newMinutes > 0) {
      parts.add('$newMinutes min${newMinutes > 1 ? 's' : ''}');
    }
    return parts.join(' ');
  }

  static String getAddressTitle(AddressModel? address) {
    String? result;

    if (address?.addressType?.toLowerCase() == 'home' ||
        address?.addressType?.toLowerCase() == 'work') {
      result = address?.addressType == 'home' ? 'Home' : 'Work';
    } else {
      if (address?.savedFor?.toLowerCase() == 'myself') {
        result = address?.addressName?.isNotEmpty == true
            ? address!.addressName
            : address?.addressLine1;
      } else {
        result = address?.name?.isNotEmpty == true
            ? address!.name
            : address?.addressLine1;
      }
    }

    return result ?? address?.addressLine1 ?? '';
  }
}
