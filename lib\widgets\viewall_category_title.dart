import 'package:flutter/material.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../core/themes/color_schemes.dart';

class ViewAllCategoryTitle extends StatelessWidget {
  const ViewAllCategoryTitle(
      {super.key,
      required this.title,
      this.onTap,
      this.offerLabel,
      this.showViewAll = true});
  final String title;
  final Widget? offerLabel;
  final void Function()? onTap;
  final bool showViewAll;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CustomText(
                title,
                fontSize: 20,
                fontWeight: FontWeight.w800,
                color: AppColors.primary700,
                // textHeight: 1.4,
              ),
              offerLabel ?? SizedBox(),
            ],
          ),
        ),
        SizedBox(width: 10),
        Visibility(
          visible: onTap != null && showViewAll,
          child: InkWell(
            onTap: () {
              onTap?.call();
            },
            borderRadius: BorderRadius.circular(20),
            splashColor: AppColors.primary.withValues(alpha: 0.2),
            highlightColor: AppColors.primary.withValues(alpha: 0.2),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
              child: Row(
                children: [
                  CustomText(
                    'View All',
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    color: AppColors.primary,
                  ),
                  SizedBox(width: 5),
                  Image.asset(
                    'assets/icons/right_arrow.png',
                    width: 10,
                  )
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
