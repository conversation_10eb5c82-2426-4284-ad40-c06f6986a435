import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';

import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/widgets/custom_bottom_sheet.dart';
import 'package:rozana/features/cart/presentation/screens/cart_screen.dart';
import 'package:rozana/features/cart/presentation/widgets/cart_section_card.dart';
import 'package:rozana/features/search/presentation/screens/search_screen.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import '../../bloc/order_bloc.dart';
import '../widgets/ravenlink_tracker.dart';
import 'package:carousel_slider/carousel_slider.dart';

const Map<String, String> _orderStatusMessages = {
  "Payment Pending": "Waiting for payment confirmation",
  "Processing": "Your order is being processed",
  "Delivered": "Your order has been delivered",
  "Delivery Failed": "Delivery failed, please contact support",
  "Cancelled": "Your order has been cancelled",
  "Return Initiated": "Your return request has been initiated",
  "Returned": "Your order has been returned successfully",
  "Confirmed": "Your order is confirmed",
  "Packing Your Order": "We are packing your order",
  "Packed": "Your order is packed",
  "Ready For Dispatch": "Your order is ready for dispatch",
  "Finding Rider": "We are finding a rider for delivery",
  "Rider Assigned": "A rider has been assigned to deliver your order",
  "Out For Delivery": "Your order is out for delivery",
};

Map<String, String> getEtaStatus(String? etaStr) {
  if (etaStr == null || etaStr.isEmpty) {
    return {"flag": "negative", "time": "-"};
  }
  // Parse backend ETA (ISO 8601 format)
  DateTime eta = DateTime.parse(etaStr).toUtc();
  DateTime now = DateTime.now().toUtc();

  Duration diff = eta.difference(now);

  if (diff.inSeconds >= 0) {
    int minutes = diff.inMinutes;
    int hours = minutes ~/ 60;

    if (hours > 0) {
      return {"flag": "positive", "time": "$hours hrs ${minutes % 60} mins"};
    } else {
      return {"flag": "positive", "time": "$minutes mins"};
    }
  } else {
    int minutesAgo = diff.inMinutes.abs();
    return {"flag": "negative", "time": "$minutesAgo mins ago"};
  }
}

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final bool fromOrderSuccess;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
    this.fromOrderSuccess = false,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  int _currentCarouselIndex = 0;
  // bool _isOrderAgainLoading = false;
  @override
  void initState() {
    super.initState();
    // Load order details
    getIt<OrderBloc>().add(OrderEvent.loadOrderDetails(widget.orderId));
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: !widget.fromOrderSuccess,
        onPopInvoked: (_) {
          if (widget.fromOrderSuccess) {
            context.go(RouteNames.home);
          } else {
            getIt<OrderBloc>().add(OrderEvent.loadOrderHistory());
          }
        },
        child: SafeArea(
          top: false,
          child: Scaffold(
            backgroundColor: AppColors.neutral150,
            appBar: AppBar(
              backgroundColor: AppColors.neutral100,
              scrolledUnderElevation: 0,
              elevation: 0,
              centerTitle: false,
              titleSpacing: 0,
              leadingWidth: 0,
              automaticallyImplyLeading: false,
              title: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      if (widget.fromOrderSuccess) {
                        context.go(RouteNames.home);
                      } else {
                        context.pop();
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                      child: Image.asset(
                        'assets/new/icons/chevron_left.png',
                        height: 26,
                        width: 26,
                        color: AppColors.primary600,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: CustomText(
                      // "#ORD${widget.orderId}",
                      "Track your order",
                      color: AppColors.primary700,
                      fontSize: 20,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ],
              ),
              actions: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: OutlinedButton.icon(
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.primary500),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(10),
                    ),
                    onPressed: _contactSupport,
                    icon: Image.asset(
                      'assets/new/icons/support_agent.png',
                      height: 16,
                      width: 16,
                      color: AppColors.primary500,
                    ),
                    label: CustomText(
                      "Get Help",
                      color: AppColors.primary500,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            body: BlocConsumer<OrderBloc, OrderState>(
              listener: (context, state) {
                state.maybeWhen(
                  error: (message, orderId) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(message),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  },
                  orderCancelled: (orderId, message) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(message),
                        backgroundColor: AppColors.success,
                      ),
                    );
                    if (widget.fromOrderSuccess) {
                      context.go(RouteNames.home);
                    } else {
                      context.pop();
                    }
                  },
                  orElse: () {},
                );
              },
              builder: (context, state) {
                return state.when(
                  initial: () => _buildSkeletonLoader(),
                  loading: () => _buildSkeletonLoader(),
                  orderDetailsLoaded: (order) => _buildOrderDetails(order),
                  error: (message, orderId) => _buildErrorState(message),
                  orderHistoryLoaded: (_, __, ___, ____, _____) =>
                      _buildSkeletonLoader(),
                  empty: (_) => _buildErrorState('Order not found'),
                  orderCancelled: (_, __) => _buildSkeletonLoader(),
                  itemsAddedToCart: (_, __) => _buildSkeletonLoader(),
                );
              },
            ),
          ),
        ));
  }

  Widget _buildOrderDetails(OrderEntity order) {
    OrderBloc bloc = getIt<OrderBloc>();

    final etaStatus = order.status != 'Delivered'
        ? getEtaStatus(order.estimatedDeliveryTime)
        : {"flag": "negative", "time": "-"};
    final isPositive = etaStatus['flag'] == 'positive';

    // Choose colors dynamically
    final bgColor = isPositive ? AppColors.green200 : AppColors.red100;
    final borderColor = isPositive ? AppColors.green400 : AppColors.red400;
    final textColor = isPositive ? AppColors.green700 : AppColors.red400;
    debugPrint('etaStatus: $etaStatus');
    return Scaffold(
      backgroundColor: AppColors.neutral150,
      body: RefreshIndicator(
        onRefresh: () async {
          bloc.add(OrderEvent.loadOrderDetails(order.id, showLoader: false));
          await bloc.stream.firstWhere(
            (state) {
              return state.maybeMap(
                orElse: () => false,
                orderDetailsLoaded: (value) {
                  return true;
                },
              );
            },
          );
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ravenlink order tracking
              Builder(builder: (context) {
                final validInvoices = order.invoices
                    .where((invoice) =>
                        invoice['raven_link'] != null &&
                        invoice['raven_link'].toString().isNotEmpty)
                    .toList();

                return AppCard(
                  backgroundColor: AppColors.neutral100,
                  padding: EdgeInsetsGeometry.all(0),
                  borderRadius: 16,
                  elevation: 0,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: CustomText(
                                _orderStatusMessages[order.status] ?? '',
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: AppColors.primary700,
                                maxLines: 2,
                              ),
                            ),

                            // Show refresh only when time is not available
                            if (etaStatus['time'] == '-')
                              Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: InkWell(
                                  onTap: () {
                                    HapticFeedback.lightImpact();
                                    getIt<OrderBloc>().add(
                                      OrderEvent.loadOrderDetails(
                                          widget.orderId),
                                    );
                                  },
                                  child: CustomImage(
                                    imageUrl: 'assets/new/icons/autorenew.png',
                                    width: 24,
                                    height: 24,
                                    imageColor: AppColors.primary600,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Show invoices only if available
                      if (validInvoices.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10.0, vertical: 12.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Status pill
                              if (etaStatus['time'] != '-')
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: bgColor,
                                    border: Border.all(color: borderColor),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomText(
                                        etaStatus['time'] ?? '',
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: textColor,
                                      ),
                                      const SizedBox(width: 4),
                                      CustomText(
                                        '•',
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: textColor,
                                      ),
                                      const SizedBox(width: 4),
                                      CustomText(
                                        isPositive ? 'On time' : "Delayed",
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: textColor,
                                      ),
                                    ],
                                  ),
                                )
                              else
                                const SizedBox(),

                              // Refresh icon button
                              if (etaStatus['time'] != '-')
                                InkWell(
                                  onTap: () {
                                    HapticFeedback.lightImpact();
                                    getIt<OrderBloc>().add(
                                      OrderEvent.loadOrderDetails(
                                          widget.orderId),
                                    );
                                  },
                                  child: CustomImage(
                                    imageUrl: 'assets/new/icons/autorenew.png',
                                    width: 24,
                                    height: 24,
                                    imageColor: AppColors.primary600,
                                  ),
                                ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 400,
                          child: CarouselSlider.builder(
                            itemCount: validInvoices.length,
                            options: CarouselOptions(
                              height: 400,
                              viewportFraction: 1.0,
                              enableInfiniteScroll: false,
                              onPageChanged: (index, reason) {
                                setState(() {
                                  _currentCarouselIndex = index;
                                });
                              },
                            ),
                            itemBuilder: (context, index, realIndex) {
                              return RavenlinkTracker(
                                invoice: validInvoices[index],
                                showTitle: false,
                              );
                            },
                          ),
                        ),
                        if (validInvoices.length > 1) ...[
                          const SizedBox(height: 8),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 10.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children:
                                  validInvoices.asMap().entries.map((entry) {
                                final index = entry.key;
                                return Container(
                                  width: 8,
                                  height: 8,
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 4),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: index == _currentCarouselIndex
                                        ? AppColors.primary
                                        : AppColors.neutral300,
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      ],
                    ],
                  ),
                );
              }),
              const SizedBox(height: 16),

              _buildAddressCard(order),
              const SizedBox(height: 16),

              // Order items
              _buildOrderItems(order),
              const SizedBox(height: 16),

              _buildPaymentDetails(order),
              const SizedBox(height: 16),

              OrderActionCard(
                onHelpTap: _contactSupport,
                order: order,
              ),
              const SizedBox(
                height: 16,
              ),
            ],
          ),
        ),
      ),
      // bottomNavigationBar: SafeArea(
      //   child: _buildActionButtons(order),
      // ),
    );
  }

  Widget _buildOrderItems(OrderEntity order) {
    return CartSectionCard(
      title: CustomText(
        "#ORD${order.id}",
        fontSize: 18,
        fontWeight: FontWeight.w700,
        color: AppColors.primary700,
      ),
      logo: 'assets/new/icons/shopping_bag.png',
      subTitle: "${order.items.length} items in order",
      cardColor: Colors.white,
      dividerColor: AppColors.neutral150,
      children: [
        ListView.separated(
          padding: const EdgeInsets.all(0),
          primary: false,
          shrinkWrap: true,
          itemCount: order.items.length,
          separatorBuilder: (ctx, index) => Divider(
            thickness: 0.5,
            color: AppColors.neutral150,
            height: 0.5,
          ),
          itemBuilder: (ctx, index) {
            final item = order.items[index];
            return Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () {
                      final sku = item.skuID?.split('-').first;
                      context.pushNamed(
                        RouteNames.productDetail,
                        pathParameters: {'id': sku ?? ''},
                        extra: {
                          'sku': sku,
                          'validate_quantity': false,
                          'variant_name': item.skuID?.split('-').last,
                        },
                      );
                    },
                    child: CustomImage(
                      imageUrl: item.imageUrl,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 12),
                  // Title + Subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          item.name,
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: AppColors.neutral600,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        CustomText(
                          item.quantity > 1
                              ? '${item.variantName ?? ''} × ${item.quantity}'
                              : item.variantName ?? '',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral500,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  // Price
                  SizedBox(width: 6),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (item.hasDiscount) ...[
                        CustomText(
                          '₹${(item.price * item.quantity).toStringAsFixed(2)}',
                          fontSize: 12,
                          color: AppColors.neutral400,
                          fontWeight: FontWeight.w500,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ],
                      SizedBox(width: 6),
                      CustomText(
                        '₹${item.totalPrice.toStringAsFixed(2)}',
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: AppColors.neutral700,
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            CustomText(
              'Order Not Found',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            const SizedBox(height: 8),
            CustomText(
              message,
              fontSize: 14,
              color: AppColors.neutral400,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Go Back',
              onPressed: () => context.pop(),
              backgroundColor: AppColors.primary,
              textColor: AppColors.neutral100,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails(OrderEntity order) {
    return CartSectionCard(
      logo: 'assets/new/icons/receipt_long.png',
      titleText: "Bill details",
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 8.sp),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Payment Mode Section
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox.square(
                    dimension: 14.sp,
                    child: Icon(
                      Icons.payment,
                      size: 14.sp,
                      color: AppColors.neutral600,
                    ),
                  ),
                  SizedBox(width: 6),
                  Expanded(
                    child: CustomText(
                      'Payment Mode',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.neutral600,
                      textHeight: 1.4,
                    ),
                  ),
                  CustomText(
                    order.paymentMethod.toUpperCase(),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.neutral700,
                    textHeight: 1.4,
                  ),
                ],
              ),

              // Bill Details Section
              SizedBox(height: 10.sp),

              // Bill Details
              BillDetailRow(
                logo: 'assets/new/icons/local_mall.png',
                title: 'Items total',
                originalPrice: order.totalAmount,
                discountedPrice: order.totalAmount,
              ),
            ],
          ),
        ),
        Divider(
          thickness: 0.5,
          color: AppColors.neutral150,
          height: 0.5,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          child: Row(
            children: [
              CustomText(
                'Grand total',
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: AppColors.neutral600,
                textHeight: 1.4,
              ),
              Spacer(),
              SizedBox(width: 12),
              CustomText(
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                '₹${formattedPrice(order.totalAmount)}',
                fontSize: 14,
                fontWeight: FontWeight.w700,
                textHeight: 1.4,
                color: AppColors.neutral700,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSkeletonLoader() {
    return SingleChildScrollView(
      child: SkeletonLoaderFactory.createOrderDetailsSkeleton(),
    );
  }

  void _contactSupport() {
    final existingUserJson = AppPreferences.getUserdata();
    Map<String, dynamic> userData = {};
    if (existingUserJson != null && existingUserJson.isNotEmpty) {
      userData = jsonDecode(existingUserJson);
    }
    context.push(RouteNames.support,
        extra: {'customer_id': userData['enc_cus_code'], 'iv': userData['iv']});
  }

  Widget _buildAddressDisplay(AddressModel? address) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          address?.addressType ?? "Home",
          fontSize: 16,
          color: AppColors.primary700,
          fontWeight: FontWeight.w600,
        ),
        const SizedBox(height: 2),
        CustomText(
          [
            address?.addressLine1,
            if ((address?.addressLine2 ?? '').isNotEmpty) address?.addressLine2,
            if ((address?.city ?? '').isNotEmpty ||
                (address?.state ?? '').isNotEmpty)
              '${address?.city ?? ''}${(address?.city?.isNotEmpty ?? false) && (address?.state?.isNotEmpty ?? false) ? ', ' : ''}${address?.state ?? ''}',
            if ((address?.pincode ?? '').isNotEmpty) address?.pincode,
            if ((address?.landmark ?? '').isNotEmpty) address?.landmark,
          ]
              .where((part) => part != null && part.toString().isNotEmpty)
              .join(', '),
          maxLines: 2,
          fontSize: 13,
          color: AppColors.neutral400,
        ),
      ],
    );
  }

  Widget _buildAddressCard(OrderEntity order) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
          decoration: ShapeDecoration(
            color: AppColors.yellow100,
            shape: RoundedRectangleBorder(
              side: BorderSide(
                width: 1,
                color: AppColors.neutral150,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.sp),
                topRight: Radius.circular(16.sp),
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CustomText(
                'All your delivery details in one place 👇',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral600,
              ),
            ],
          ),
        ),
        AppCard(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          topLeftRadius: 0,
          topRightRadius: 0,
          borderRadius: 16,
          backgroundColor: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomImage(
                    imageUrl: 'assets/new/icons/account_circle.png',
                    height: 24,
                    width: 24,
                    imageColor: AppColors.primary600,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          "${order.customerName}, ${order.deliveryAddress?.phone}",
                          fontSize: 16,
                          color: AppColors.primary700,
                          fontWeight: FontWeight.w500,
                        ),
                        const SizedBox(height: 2),
                        CustomText(
                          "Delivery partner may call this number",
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral400,
                        ),
                      ],
                    ),
                  ),
                  // Add Edit here
                ],
              ),

              SizedBox(height: 12.h),
              Visibility(
                visible: true,
                child: SizedBox(
                  width: double.infinity,
                  child: DottedLine(
                    color: AppColors.neutral150,
                    height: 1.5,
                    dashWidth: 4,
                    dashSpace: 4,
                  ),
                ),
              ),
              SizedBox(height: 12.h),
              // Address row
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomImage(
                    imageUrl: 'assets/new/icons/location_on.png',
                    height: 24,
                    width: 24,
                    imageColor: AppColors.primary600,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildAddressDisplay(order.deliveryAddress),
                  ),
                  // Add Edit here
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class OrderActionCard extends StatelessWidget {
  final VoidCallback onHelpTap;
  final OrderEntity order;

  const OrderActionCard({
    super.key,
    required this.onHelpTap,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      borderRadius: 16,
      elevation: 0,
      backgroundColor: Colors.white,
      child: Column(
        children: [
          // Help Row
          InkWell(
            onTap: onHelpTap,
            child: Row(
              children: [
                // Left Icon inside circular background
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFFFDECEC),
                  ),
                  child: CustomImage(
                    imageUrl: 'assets/new/icons/support_agent.png',
                    height: 24,
                    width: 24,
                    imageColor: AppColors.secondary600,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        "Get help with this order",
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: AppColors.neutral600,
                      ),
                      const SizedBox(height: 4),
                      CustomText(
                        "Reach out to us with your query via chat",
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.neutral400,
                      ),
                    ],
                  ),
                ),
                CustomImage(
                  imageUrl: 'assets/new/icons/chevron_right.png',
                  height: 24,
                  width: 24,
                )
              ],
            ),
          ),

          // Divider
          SizedBox(height: 12.h),
          Visibility(
            visible: true,
            child: SizedBox(
              width: double.infinity,
              child: DottedLine(
                color: AppColors.neutral150,
                height: 1.5,
                dashWidth: 4,
                dashSpace: 4,
              ),
            ),
          ),

          // Cancel Row
          Padding(
            padding: const EdgeInsets.only(top: 12.0, bottom: 6),
            child: InkWell(
              onTap: order.canCancel
                  ? () {
                      _showCancelConfirmationBottomSheet(context, order.id,
                          order.paymentMethod, order.totalAmount);
                    }
                  : null, // disable tap if cannot cancel
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 6.0),
                    child: CustomImage(
                      imageUrl: 'assets/new/icons/contract_delete.png',
                      height: 24,
                      width: 24,
                      imageColor: order.canCancel
                          ? AppColors.red700
                          : AppColors.red200, // lighter when disabled
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          "Cancel order",
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: order.canCancel
                              ? AppColors.red600
                              : AppColors.red200,
                        ),
                        if (!order.canCancel) // show message when disabled
                          CustomText(
                            "Cannot cancel now because rider is on the way",
                            fontSize: 12,
                            color: AppColors.neutral400,
                            fontWeight: FontWeight.w500,
                            maxLines: 2,
                          ),
                      ],
                    ),
                  ),
                  CustomImage(
                    imageUrl: 'assets/new/icons/chevron_right.png',
                    height: 24,
                    width: 24,
                    imageColor: order.canCancel
                        ? AppColors.primary600
                        : AppColors.primary100,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

void _showCancelConfirmationBottomSheet(BuildContext context, String orderId,
    String paymentMethod, double totalAmount) {
  debugPrint('Cancel Order $paymentMethod');
  showConfirmationBottomSheet(
    context: context,
    title: 'Cancel Order?',
    onProceed: () {
      context.pop();
      getIt<OrderBloc>().add(OrderEvent.cancelOrder(orderId));
    },
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              'Are you sure you want to cancel this order?',
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: AppColors.neutral700,
            ),
            if (paymentMethod.toLowerCase() != 'cash on delivery') ...[
              SizedBox(height: 8),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: 'Your payment of ',
                      style: TextStyle(
                        color: AppColors.neutral500,
                        fontSize: 14,
                        fontFamily: 'Mukta',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    TextSpan(
                      text:
                          '₹$totalAmount will be refunded in 3-5 business days',
                      style: TextStyle(
                        color: AppColors.neutral500,
                        fontSize: 14,
                        fontFamily: 'Mukta',
                        fontWeight: FontWeight.w700,
                        height: 1.29,
                      ),
                    ),
                    TextSpan(
                      text: ' to original payment method.',
                      style: TextStyle(
                        color: AppColors.neutral500,
                        fontSize: 14,
                        fontFamily: 'Mukta',
                        fontWeight: FontWeight.w500,
                        height: 1.29,
                      ),
                    ),
                  ],
                ),
              )
            ]
          ],
        ),
      ),
      SizedBox(
        height: 20,
      ),
    ],
    onBack: () => context.pop(),
    backText: 'Go Back',
    proceedText: 'Cancel Order',
  );
}
