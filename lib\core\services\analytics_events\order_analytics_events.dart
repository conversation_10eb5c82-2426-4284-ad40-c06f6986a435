import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';


class OrderAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }


  /// Track Buy Again Page Landed
  Future<void> trackBuyAgainPageLanded() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logBuyAgainPageLanded(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
        );
      }
    } catch (e) {
      debugPrint('Error tracking buy again page landed: $e');
    }
  }

  /// Track Order History Page Landed
  Future<void> trackOrderHistoryPageLanded() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOrderHistoryPageLanded(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
        );
      }
    } catch (e) {
      debugPrint('Error tracking order history page landed: $e');
    }
  }

  /// Track Order List Track Order Clicked
  Future<void> trackOrderListTrackOrderClicked({
    required String orderId,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOrderListTrackOrderClicked(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          orderId: orderId,
        );
      }
    } catch (e) {
      debugPrint('Error tracking order list track order clicked: $e');
    }
  }

  /// Track Order List Order Again Clicked
  Future<void> trackOrderListOrderAgainClicked({
    required String orderId,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOrderListOrderAgainClicked(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          orderId: orderId,
        );
      }
    } catch (e) {
      debugPrint('Error tracking order list order again clicked: $e');
    }
  }

  /// Track Order Cancel Successful
  Future<void> trackOrderCancelSuccessful({
    required String orderId,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required int deliverySlots,
    required String paymentMethodSelected,
    required String paymentStatus,
    required String orderStatus,
    required String cancellationReason,
    required String deliveryETA,
    required String surchargeTag,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOrderCancelSuccessful(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          freeProductsAdded: freeProductsAdded,
          deliverySlots: deliverySlots,
          paymentMethodSelected: paymentMethodSelected,
          paymentStatus: paymentStatus,
          orderStatus: orderStatus,
          orderId: orderId,
          cancellationReason: cancellationReason,
          deliveryETA: deliveryETA,
          surchargeTag: surchargeTag,
        );
      }
    } catch (e) {
      debugPrint('Error tracking order cancel successful: $e');
    }
  }

  /// Track Order Success Screen
  Future<void> trackOrderSuccessScreen({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required String paymentMethodSelected,
    required String paymentStatus,
    required String orderStatus,
    required String orderId,
    String? orderFailReason,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOrderSuccessScreen(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          freeProductsAdded: freeProductsAdded,
          paymentMethodSelected: paymentMethodSelected,
          paymentStatus: paymentStatus,
          orderStatus: orderStatus,
          orderId: orderId,
          orderFailReason: orderFailReason,
          ipAddress: ipAddress,
          latitude: latitude,
          longitude: longitude,
        );
      }
    } catch (e) {
      debugPrint('Error tracking order success screen: $e');
    }
  }

  /// Track Order Cancel Selected
  Future<void> trackOrderCancelSelected({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required String paymentMethodSelected,
    required String paymentStatus,
    required String orderStatus,
    required String orderId,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOrderCancelSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          freeProductsAdded: freeProductsAdded,
          paymentMethodSelected: paymentMethodSelected,
          paymentStatus: paymentStatus,
          orderStatus: orderStatus,
          orderId: orderId,
        );
      }
    } catch (e) {
      debugPrint('Error tracking order cancel selected: $e');
    }
  }
}