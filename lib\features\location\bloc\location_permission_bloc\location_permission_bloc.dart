import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

import 'location_permission_event.dart';
import 'location_permission_state.dart';

class LocationPermissionBloc
    extends Bloc<LocationPermissionEvent, LocationPermissionState>
    with WidgetsBindingObserver {
  static bool isBottomSheetShowing = false;
  static bool isFirstCheck = true;
  static const MethodChannel _platform = MethodChannel(
    'com.example.app/location_settings',
  );
  LocationPermissionBloc() : super(const LocationPermissionState.initial()) {
    WidgetsBinding.instance.addObserver(this);
    on<LocationPermissionEvent>((event, emit) async {
      await event.when(
        checkPermissions: (showLoader) => _checkPermissions(emit, showLoader),
        requestPermissions: () => _requestPermissions(emit),
        openAppSettings: () => _openAppSettings(emit),
        requestLocationServiceToggle: () => _requestLocationServiceToggle(emit),
      );
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // App has come to foreground, re-check permissions
      add(const LocationPermissionEvent.checkPermissions());
    }
  }

  Future<void> _checkPermissions(
    Emitter<LocationPermissionState> emit,
    bool showLoader, {
    bool skipEnableCheck = false,
  }) async {
    if (showLoader) {
      emit(const LocationPermissionState.loading());
    }

    // Check if location service is enabled
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled && !skipEnableCheck) {
      emit(const LocationPermissionState.serviceDisabled());
      return;
    }

    // Check permission status
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      emit(const LocationPermissionState.permissionDenied());
    } else if (permission == LocationPermission.deniedForever) {
      emit(const LocationPermissionState.permissionPermanentlyDenied());
    } else {
      // Permission is granted (whileInUse or always) and service is enabled
      emit(const LocationPermissionState.grantedAndEnabled());
    }
  }

  Future<void> _requestPermissions(
    Emitter<LocationPermissionState> emit,
  ) async {
    emit(const LocationPermissionState.loading());

    LocationPermission permission = await Geolocator.requestPermission();

    if (permission == LocationPermission.denied) {
      // User denied permission after prompt
      emit(const LocationPermissionState.permissionDenied());
    } else if (permission == LocationPermission.deniedForever) {
      // User denied and selected "Don't ask again"
      emit(const LocationPermissionState.permissionPermanentlyDenied());
    } else {
      // Permission granted, now check service status again just in case
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        emit(const LocationPermissionState.serviceDisabled());
      } else {
        emit(const LocationPermissionState.grantedAndEnabled());
      }
    }
  }

  Future<void> _openAppSettings(Emitter<LocationPermissionState> emit) async {
    await openAppSettings();
    emit(
      const LocationPermissionState.loading(),
    ); // Emit loading and expect a subsequent checkPermissions
  }

  Future<void> _requestLocationServiceToggle(
    Emitter<LocationPermissionState> emit,
  ) async {
    try {
      // Call the native method
      await _platform.invokeMethod('showLocationServicesDialog');

      await _checkPermissions(emit, false, skipEnableCheck: true);
    } on PlatformException catch (e) {
      emit(
        LocationPermissionState.error(
          "Failed to show location services dialog: '${e.message}'.",
        ),
      );
    }
  }

  @override
  Future<void> close() {
    WidgetsBinding.instance.removeObserver(this);
    return super.close();
  }
}
