import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';

/// A widget that conditionally displays either the normal content
/// or a "Not Serving This Location" message based on the location state
/// This preserves the app's header and bottom navigation
class LocationStateHandler extends StatelessWidget {
  final Widget child;
  final double? builderHeight;

  const LocationStateHandler({
    super.key,
    required this.child,
    this.builderHeight,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddressBloc, AddressState>(
      buildWhen: (previous, current) =>
          previous.isServicable != current.isServicable,
      builder: (context, state) {
        if (!state.isServicable) {
          return _buildNotServiceableContent(context,
              state.selectedAddress ?? state.currentLocation ?? AddressModel());
        } else {
          return child;
        }
      },
    );
  }

  /// Builds the "Not Serving This Location" content
  Widget _buildNotServiceableContent(
      BuildContext context, AddressModel address) {
    return SizedBox(
      height: builderHeight,
      child: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                const SizedBox(height: 12),
                _buildNotServiceableInfo(context, address),
                const SizedBox(height: 12),
                _buildBottomButtons(context),
                const SizedBox(height: 100),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        SizedBox(
          height: 230,
          width: 230,
          child: Lottie.asset(
            'assets/lotties/location-not-found.json',
            fit: BoxFit.contain,
            repeat: true,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Not Serving This Location',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w700,
            color: AppColors.neutral600,
          ),
        ),
      ],
    );
  }

  Widget _buildNotServiceableInfo(BuildContext context, AddressModel address) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                address.fullAddress ?? 'Address not available',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.neutral600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    return AppButton(
      text: 'Try Another Location',
      onPressed: () {
        context.push(RouteNames.addresses, extra: {
          'selectMode': true,
          'onAddressSelected': (AddressModel? selectedAddress) {
            if (selectedAddress != null) {
              // Update the selected address and refresh location
              final locationBloc = context.read<AddressBloc>();
              locationBloc.add(AddressEvent.init());
              context.pop();
            }
          },
        });
      },
    );
  }
}
