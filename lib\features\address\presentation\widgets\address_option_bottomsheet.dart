import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_text.dart';
import '../../utils/address_utils.dart';

class EditAddressBottomSheet extends StatelessWidget {
  const EditAddressBottomSheet({super.key, this.address});
  final AddressModel? address;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        bottomSheet: AnimatedPadding(
          padding: EdgeInsets.only(bottom: 0
              // MediaQuery.of(context).viewInsets.bottom
              ),
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeIn,
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.neutral100,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 10, 20, 12),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          AddressUtils.getAddressTitle(address),
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.primary700,
                        ),
                        SizedBox(height: 2),
                        CustomText(
                          address?.fullAddress ?? '',
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral500,
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
                ColoredBox(
                  color: AppColors.neutral150,
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(16, 16, 16, 20),
                    decoration: BoxDecoration(
                      color: AppColors.neutral100,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AddressEditTile(
                          title: 'Edit',
                          icon: 'assets/new/icons/edit.png',
                          color: AppColors.neutral600,
                          onTap: () {
                            HapticFeedback.lightImpact();
                            context.pop();
                            getIt<AddressBloc>().add(
                                AddressEvent.selectFromMap(address: address));
                          },
                        ),
                        Divider(
                            thickness: 0.5,
                            color: AppColors.neutral150,
                            height: 0.5),
                        if (address != null && address?.isSelected == false)
                          AddressEditTile(
                            title: 'Delete',
                            icon: 'assets/new/icons/delete.png',
                            color: AppColors.red500,
                            onTap: () {
                              HapticFeedback.lightImpact();
                              context.pop();
                              getIt<AddressBloc>().add(
                                AddressEvent.deleteAddress(address?.id ?? ''),
                              );
                            },
                          ),

                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class AddressEditTile extends StatelessWidget {
  const AddressEditTile(
      {super.key,
      required this.title,
      required this.onTap,
      required this.icon,
      required this.color});
  final String title;
  final Function() onTap;
  final String icon;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.fromLTRB(12, 12, 12, 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: 36.sp,
              width: 36.sp,
              decoration: BoxDecoration(
                color: Color(0xFFF7F7F7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Image.asset(
                  icon,
                  height: 24,
                  width: 24,
                  color: color,
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: CustomText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
