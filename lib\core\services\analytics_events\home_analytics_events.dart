import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';


class HomeAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track Promotional Banner Selected
  Future<void> trackPromotionalBannerSelected({
    required String bannerId,
    required String bannerName,
    required String homepageSection,
    required String redirectionPath,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logPromotionalBannerSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          bannerId: bannerId,
          bannerLocation: homepageSection,
          redirectionPath: redirectionPath,
          homepageSection: homepageSection,
        );
      }
    } catch (e) {
      debugPrint('Error tracking promotional banner selected: $e');
    }
  }

  /// Track Category Selected
  Future<void> trackCategorySelected({
    required String categoryName,
    required String categoryPath,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logCategorySelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          categoryName: categoryName,
          categoryPath: categoryPath,
        );
      }
    } catch (e) {
      debugPrint('Error tracking category selected: $e');
    }
  }

  /// Track Sub Category Selected
  Future<void> trackSubCategorySelected({
    required String categoryName,
    required String subCategoryName,
    required String categoryPath,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logSubCategorySelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          categoryName: categoryName,
          subCategoryName: subCategoryName,
          categoryPath: categoryPath,
        );
      }
    } catch (e) {
      debugPrint('Error tracking sub category selected: $e');
    }
  }

  /// Track Sub Sub Category Selected
  Future<void> trackSubSubCategorySelected({
    required String categoryName,
    required String subCategoryName,
    required String subSubCategoryName,
    required String categoryPath,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logSubSubCategorySelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          categoryName: categoryName,
          subCategoryName: subCategoryName,
          subSubCategoryName: subSubCategoryName,
          categoryPath: categoryPath,
        );
      }
    } catch (e) {
      debugPrint('Error tracking sub sub category selected: $e');
    }
  }

  /// Track Landing Page Selected
  Future<void> trackLandingPageSelected({
    required String homepageSection,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logLandingPageSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          homepageSection: homepageSection,
        );
      }
    } catch (e) {
      debugPrint('Error tracking landing page selected: $e');
    }
  }

  /// Track Homepage Previous Order Product Selected
  Future<void> trackHomepagePreviousOrderProductSelected({
    required String homepageSection,
    required String tileName,
    required String label,
    String source = 'Home_page',
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logHomepagePreviousOrderProductSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          homepageSection: homepageSection,
          tileName: tileName,
          label: label,
          source: source,
        );
      }
    } catch (e) {
      debugPrint('Error tracking homepage previous order product selected: $e');
    }
  }

  /// Track Homepage Frequently Bought Product Selected
  Future<void> trackHomepageFrequentlyBoughtProductSelected({
    required String categoryName,
    required String categoryPath,
    String? subCategoryName,
    String? subSubCategoryName,
    String? productSkuId,
    String? productName,
    String screenName = 'home_page',
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logHomepageFrequentlyBoughtProductSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          categoryName: categoryName,
          categoryPath: categoryPath,
          subCategoryName: subCategoryName,
          subSubCategoryName: subSubCategoryName,
          productSkuId: productSkuId,
          productName: productName,
          screenName: screenName,
        );
      }
    } catch (e) {
      debugPrint('Error tracking homepage frequently bought product selected: $e');
    }
  }

  /// Track Homepage Frequently Bought CTA
  Future<void> trackHomepageFrequentlyBoughtCTA({
    String? subCategoryName,
    required String categoryName,
    required String categoryPath,
    String screenName = 'home_page',
    String? productSkuId,
    String? productName,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logHomepageFrequentlyBoughtCTA(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          subCategoryName: subCategoryName,
          categoryName: categoryName,
          categoryPath: categoryPath,
          screenName: screenName,
          productSkuId: productSkuId,
          productName: productName,
        );
      }
    } catch (e) {
      debugPrint('Error tracking homepage frequently bought CTA: $e');
    }
  }

}
