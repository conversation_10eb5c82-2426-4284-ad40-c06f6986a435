
package com.user.mobile.rozana

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.ResolvableApiException
// import com.google.android.gms.location.LocationRequest // No longer needed if you only use Builder
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.android.gms.location.LocationRequest // Keep this if you need the LocationRequest type declaration
import com.google.android.gms.location.Priority // <--- NEW: You MUST import this!

import android.app.Activity
import android.content.Intent
import android.content.IntentSender
import android.util.Log

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.app/location_settings" // Must match Dart code

    companion object {
        private const val REQUEST_CHECK_SETTINGS = 0x1 // Unique request code
        private const val TAG = "LocationSettings"
    }

    private var pendingResult: MethodChannel.Result? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
                call, result ->
            if (call.method == "showLocationServicesDialog") {
                pendingResult = result // Store the result to be used later
                checkLocationSettings()
            } else {
                result.notImplemented()
            }
        }
    }

    private fun checkLocationSettings() {
        // *** CRITICAL CHANGE: Use LocationRequest.Builder instead of LocationRequest.create() ***
        val locationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY, // <--- Use Priority.PRIORITY_HIGH_ACCURACY
            10000 // interval in milliseconds (e.g., 10 seconds)
        )
        .setMinUpdateIntervalMillis(5000) // Fastest interval
        .setWaitForAccurateLocation(false) // Optional: adjust as needed
        .build() // Don't forget to call .build()


        val builder = LocationSettingsRequest.Builder()
            .addLocationRequest(locationRequest)
            .setAlwaysShow(true) // This is crucial to make the dialog appear if settings are not met
            .setNeedBle(true) // Optional: If you specifically need Bluetooth Low Energy for scanning

        val client = LocationServices.getSettingsClient(this)
        val task = client.checkLocationSettings(builder.build())

        task.addOnSuccessListener { locationSettingsResponse ->
            // All location settings are satisfied, including high accuracy.
            Log.d(TAG, "Location settings are already satisfied (High Accuracy).")
            pendingResult?.success(true) // Indicate success (location is already on and accurate)
            pendingResult = null
        }

        task.addOnFailureListener { exception -> // Explicitly define exception as type Exception
            if (exception is ResolvableApiException) {
                try {
                    // Show the dialog by calling startResolutionForResult()
                    // This will display the "Location Accuracy" dialog
                    exception.startResolutionForResult(this, REQUEST_CHECK_SETTINGS)
                } catch (sendEx: IntentSender.SendIntentException) {
                    Log.e(TAG, "Error showing location settings dialog: ${sendEx.message}")
                    pendingResult?.error("DIALOG_ERROR", sendEx.message, null)
                    pendingResult = null
                }
            } else if (exception is ApiException && exception.statusCode == LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE) {
                // Location settings are not satisfied, but we cannot fix it.
                // This might happen on emulators or devices without Google Play Services
                Log.e(TAG, "Location settings change unavailable: ${exception.message}")
                pendingResult?.error("UNAVAILABLE", "Location settings change unavailable (e.g., no Play Services)", null)
                pendingResult = null
            } else {
                // Handle other types of exceptions
                Log.e(TAG, "Unknown exception during location settings check: ${exception.message}")
                pendingResult?.error("UNKNOWN_ERROR", exception.message, null)
                pendingResult = null
            }
        }
    }

    // Handle the result from the system dialog
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_CHECK_SETTINGS -> {
                when (resultCode) {
                    Activity.RESULT_OK -> {
                        Log.d(TAG, "User enabled location accuracy.")
                        pendingResult?.success(true) // Dialog shown and user enabled accuracy
                    }
                    Activity.RESULT_CANCELED -> {
                        Log.d(TAG, "User did not enable location accuracy.")
                        pendingResult?.success(false) // Dialog shown, but user cancelled
                    }
                }
                pendingResult = null // Always clear pendingResult after handling
            }
        }
    }
}