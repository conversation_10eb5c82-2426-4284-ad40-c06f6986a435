import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_event.freezed.dart';

@freezed
class AppEvent with _$AppEvent {
  const factory AppEvent.appStarted() = AppStarted;
  const factory AppEvent.loginRequested(String token,
      {Map<String, dynamic>? user}) = LoginRequested;
  const factory AppEvent.loginWithProfileCheck(String token,
      {Map<String, dynamic>? user}) = LoginWithProfileCheck;
  const factory AppEvent.logoutRequested() = LogoutRequested;
}
