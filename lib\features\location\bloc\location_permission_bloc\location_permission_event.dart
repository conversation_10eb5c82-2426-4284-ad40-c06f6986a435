import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_permission_event.freezed.dart';

@freezed
class LocationPermissionEvent with _$LocationPermissionEvent {
  const factory LocationPermissionEvent.checkPermissions(
      {@Default(false) bool showLoader}) = _CheckPermissions;
  const factory LocationPermissionEvent.requestPermissions() =
      _RequestPermissions;
  const factory LocationPermissionEvent.openAppSettings() = _OpenAppSettings;
  const factory LocationPermissionEvent.requestLocationServiceToggle() =
      _RequestLocationServiceToggle;
}
