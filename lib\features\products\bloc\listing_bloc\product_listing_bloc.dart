import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/domain/entities/category_entity.dart';


import 'product_listing_event.dart';
import 'product_listing_state.dart';



class ProductListingBloc extends Bloc<ProductListingEvent, ProductListingState> {

  ProductListingBloc({
  }) : super(const ProductListingState.loading()) {
    // Register event handlers using .map for Freezed events
    on<ProductListingEvent>((event, emit) async {
      await event.when(
        initial: (
          CategoryEntity? category,
        ) async {
          await _onInitialEvent(
            category,
            emit,
          );
        },
        selectSubcategory: (CategoryEntity? subcategory) {
          _onSelectSubcategoryEvent(subcategory, emit);
        },
 
        reset: () {
          _onResetEvent(emit);
        },
      );
    });
  }

  // Event handler for initial setup
  Future<void> _onInitialEvent(
    CategoryEntity? category,
    Emitter<ProductListingState> emit,
  ) async {
    emit(const ProductListingState.loading());
    emit(ProductListingState.loaded(category: category));
  }

  // Event handler for selecting a subcategory
  void _onSelectSubcategoryEvent(
    CategoryEntity? subCategory,
    Emitter<ProductListingState> emit,
  ) {
    if (subCategory != null) {
      emit(ProductListingState.loaded(
        category: subCategory,
      ));
    }
  }

  void _onResetEvent(Emitter<ProductListingState> emit) {
    emit(const ProductListingState.loading());
  }
}
