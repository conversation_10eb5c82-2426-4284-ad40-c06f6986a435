<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="rozana">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  
  <!-- Google Sign-In client ID -->
  <meta name="google-signin-client_id" content="184880023304-9k0lqv7bq9ld2ckbkdjkbvqk7vgn9qe0.apps.googleusercontent.com">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Rozana</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <!-- Google Maps API - Make sure this is BEFORE flutter_bootstrap.js -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDttwF2xEqWoxAtxQvvboWsiTwW6JQcb9U&libraries=places,geometry"></script>
  
  <!-- Google Places API interop -->
  <script src="google_places_interop.js"></script>
  
  <!-- Razorpay checkout script - load synchronously before Flutter -->
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  
  <!-- Razorpay web integration helper -->
  <script src="razorpay_integration.js"></script>
  
  <!-- Flutter app - load after Razorpay scripts -->
  <script src="flutter_bootstrap.js"></script>
</body>
</html>