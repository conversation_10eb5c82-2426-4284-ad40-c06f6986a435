import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';

class CartAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track My Bag Landing Page
  Future<void> trackMyBagLandingPage() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logMyBagLandingPage(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: 0,
          itemsTotal: '0',
          deliveryCharge: '0',
          tax: '0',
          discount: '0',
          grandTotal: '0',
          freeProductsAdded: 0,
          deliverySlots: 0,
        );
      }
    } catch (e) {
      debugPrint('Error tracking my bag landing page: $e');
    }
  }

  /// Track My Bag Product Added
  Future<void> trackMyBagProductAdded({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String productDiscount,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logMyBagProductAdded(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          productDiscount: productDiscount,
        );
      }
    } catch (e) {
      debugPrint('Error tracking my bag product added: $e');
    }
  }

  /// Track My Bag Product Removed
  Future<void> trackMyBagProductRemoved({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String productDiscount,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logMyBagProductRemoved(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          productDiscount: productDiscount,
        );
      }
    } catch (e) {
      debugPrint('Error tracking my bag product removed: $e');
    }
  }

  /// Track My Bag Product Deleted
  Future<void> trackMyBagProductDeleted({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String productDiscount,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logMyBagProductDeleted(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          productDiscount: productDiscount,
        );
      }
    } catch (e) {
      debugPrint('Error tracking my bag product deleted: $e');
    }
  }

}