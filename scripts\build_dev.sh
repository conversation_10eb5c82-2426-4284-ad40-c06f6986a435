#!/bin/bash

# Build script for development environment
# Note: Currently uses same configuration as other environments
echo "Building for DEVELOPMENT environment..."

flutter build apk \
  --dart-define=ENVIRONMENT=development \
  --dart-define=API_BASE_URL=https://oms-dev.rozana.tech/ \
  --dart-define=TYPESENSE_HOST=rozanats.headrun.com \
  --dart-define=TYPESENSE_API_KEY=WzQSB1f8gpXWmsQzRjvEO149v04bfaJa \
  --dart-define=FIREBASE_API_KEY=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI \
  --dart-define=AMPLITUDE_API_KEY=802688fb6093fa50671e018afbe1f7df \
  --dart-define=BRANCH_LOGGING_ENABLED=true \
  --dart-define=ENABLE_LOGGING=true \
  --dart-define=APP_NAME="Roz<PERSON>" \
  --debug

echo "Development build completed!"
