import 'package:rozana/data/models/order_model.dart';
import 'package:rozana/data/models/order_item_model.dart';
import 'package:rozana/data/models/order_timeline_model.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/domain/entities/order_item_entity.dart';
import 'package:rozana/domain/entities/order_timeline_entity.dart';

/// Mapper class to convert between order models and entities
class OrderMapper {
  /// Convert OrderModel to OrderEntity
  static OrderEntity toEntity(OrderModel model) {
    return OrderEntity(
        id: model.id ?? '',
        customerId: model.customerId ?? '',
        customerName: model.customerName ?? '',
        status: model.status ?? '',
        orderDate: model.orderDateTime ?? DateTime.now(),
        deliveryDate: model.deliveryDateTime,
        estimatedDeliveryTime: model.estimatedDeliveryTime,
        totalAmount: (model.totalAmount ?? 0).toDouble(),
        subtotal: (model.subtotal ?? 0).toDouble(),
        tax: (model.tax ?? 0).toDouble(),
        deliveryFee: (model.deliveryFee ?? 0).toDouble(),
        discount: (model.discount ?? 0).toDouble(),
        paymentMethod: model.paymentMethod ?? '',
        deliveryAddress: model.deliveryAddress,
        items: model.items
                ?.map((item) => OrderItemMapper.toEntity(item))
                .toList() ??
            [],
        orderTimeline: model.orderTimeline
                ?.map((timeline) => OrderTimelineMapper.toEntity(timeline))
                .toList() ??
            [],
        canCancel: model.canCancel ?? false,
        canReorder: model.canReorder ?? false,
        invoices: model.invoices ?? [],
        historyItems: model.historyItems ?? []);
  }

  /// Convert list of OrderModel to list of OrderEntity
  static List<OrderEntity> toEntityList(List<OrderModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  /// Convert OrderEntity to OrderModel
  static OrderModel toModel(OrderEntity entity) {
    return OrderModel(
      id: entity.id,
      customerId: entity.customerId,
      customerName: entity.customerName,
      status: entity.status,
      orderDate: entity.orderDate.toIso8601String(),
      deliveryDate: entity.deliveryDate?.toIso8601String(),
      estimatedDeliveryTime: entity.estimatedDeliveryTime,
      totalAmount: entity.totalAmount,
      subtotal: entity.subtotal,
      tax: entity.tax,
      deliveryFee: entity.deliveryFee,
      discount: entity.discount,
      paymentMethod: entity.paymentMethod,
      deliveryAddress: entity.deliveryAddress,
      items: entity.items.map((item) => OrderItemMapper.toModel(item)).toList(),
      orderTimeline: entity.orderTimeline
          .map((timeline) => OrderTimelineMapper.toModel(timeline))
          .toList(),
      canCancel: entity.canCancel,
      canReorder: entity.canReorder,
      objectType: 'order',
      invoices: entity.invoices,
      historyItems: entity.historyItems,
    );
  }
}

/// Mapper class for order items
class OrderItemMapper {
  /// Convert OrderItemModel to OrderItemEntity
  static OrderItemEntity toEntity(OrderItemModel model) {
    return OrderItemEntity(
      id: model.id ?? '',
      productId: model.productId ?? '',
      name: model.name ?? '',
      variantName: model.variantName,
      imageUrl: model.imageUrl,
      price: (model.price ?? 0).toDouble(),
      quantity: model.quantity ?? 0,
      unit: model.unit ?? '',
      discountedPrice: (model.discountedPrice ?? model.price ?? 0).toDouble(),
      facilityId: model.facilityId,
      facilityName: model.facilityName,
      skuID: model.skuID,
      tax: model.tax,
      cgst: model.cgst,
      sgst: model.sgst,
      taxable: model.taxable,
      isReturnable: model.isReturnable ?? false,
      returnType: model.returnType ?? '00',
      returnWindow: model.returnWindow ?? 0,
    );
  }

  /// Convert OrderItemEntity to OrderItemModel
  static OrderItemModel toModel(OrderItemEntity entity) {
    return OrderItemModel(
      id: entity.id,
      productId: entity.productId,
      name: entity.name,
      variantName: entity.variantName,
      imageUrl: entity.imageUrl,
      price: entity.price,
      quantity: entity.quantity,
      unit: entity.unit,
      discountedPrice: entity.discountedPrice,
      facilityId: entity.facilityId,
      facilityName: entity.facilityName,
      skuID: entity.skuID,
      tax: entity.tax,
      cgst: entity.cgst,
      sgst: entity.sgst,
      taxable: entity.taxable,
      isReturnable: entity.isReturnable,
      returnType: entity.returnType,
      returnWindow: entity.returnWindow,
    );
  }
}

/// Mapper class for order timeline
class OrderTimelineMapper {
  /// Convert OrderTimelineModel to OrderTimelineEntity
  static OrderTimelineEntity toEntity(OrderTimelineModel model) {
    return OrderTimelineEntity(
      status: model.status ?? '',
      timestamp: model.dateTime ?? DateTime.now(),
      title: model.title ?? '',
      description: model.description ?? '',
    );
  }

  /// Convert OrderTimelineEntity to OrderTimelineModel
  static OrderTimelineModel toModel(OrderTimelineEntity entity) {
    return OrderTimelineModel(
      status: entity.status,
      timestamp: entity.timestamp.toIso8601String(),
      title: entity.title,
      description: entity.description,
    );
  }
}
