import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/widgets/custom_image.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../bloc/free_product_bloc/free_product_bloc.dart';
import '../../bloc/free_product_bloc/free_product_event.dart';
import '../../bloc/free_product_bloc/free_product_state.dart';

class FreeProductCard extends StatelessWidget {
  final ProductEntity product;

  const FreeProductCard({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Free offer header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.success,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.card_giftcard,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'FREE PRODUCT',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Product content
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(
                    width: 80,
                    height: 80,
                    child: _buildImage(),
                  ),
                ),
                const SizedBox(width: 12),

                // Product details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product name
                      Text(
                        product.name,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral700,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),

                      // Category
                      if (product.category.isNotEmpty)
                        Text(
                          product.category,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.neutral400,
                          ),
                        ),
                      const SizedBox(height: 8),

                      // Price section
                      Row(
                        children: [
                          // Free price
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'FREE',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Original price (crossed out)
                          if (product.originalPrice != null &&
                              product.originalPrice! > 0)
                            Text(
                              '₹${product.originalPrice!.toStringAsFixed(0)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.neutral400,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Add to cart section
                _buildCartControls(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImage() {
    return CustomImage(imageUrl: product.imageUrl ?? '', width: 80, height: 80, fit: BoxFit.cover);

  }

  Widget _buildCartControls() {
    return BlocBuilder<FreeProductBloc, FreeProductState>(
      builder: (context, state) {
        // Check if this product is in the selected free products list
        final isSelected = state.selectedFreeProducts.any(
          (selectedProduct) =>
              selectedProduct.id == product.id &&
              selectedProduct.skuID == product.skuID,
        );

        if (isSelected) {
          return _buildDeleteButton(context);
        } else {
          return _buildAddButton(context);
        }
      },
    );
  }

  Widget _buildAddButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _addToCart(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.success,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'ADD',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _removeFromFreeCart(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'DELETE',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _addToCart(BuildContext context) {
    HapticFeedback.mediumImpact();

    getIt<FreeProductBloc>().add(
          FreeProductEvent.addFreeProduct(product: product),
        );
  }

  void _removeFromFreeCart(BuildContext context) {
    HapticFeedback.mediumImpact();

    getIt<FreeProductBloc>().add(
          FreeProductEvent.removeFreeProduct(
            productId: product.id,
            skuId: product.skuID ?? '',
          ),
        );
  }
}
