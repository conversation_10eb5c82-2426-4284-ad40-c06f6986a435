export 'profile_event.dart';
export 'profile_state.dart';

import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/services/user_profile_service.dart';

import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final UserProfileService _userProfileService;

  ProfileBloc({UserProfileService? userProfileService})
      : _userProfileService = userProfileService ?? UserProfileService(),
        super(const ProfileState.initial()) {
    on<ProfileEvent>((event, emit) async {
      await event.map(
        loadUserData: (e) => _mapLoadUserDataToState(emit),
        logout: (e) => _mapLogoutToState(emit),
      );
    });
  }

  Future<void> _mapLoadUserDataToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      // Try to get profile from Firestore first
      final profile = await _userProfileService.getUserProfile();

      if (profile != null) {
        emit(ProfileState.loaded(
          userName: profile.displayName ?? 'No Name',
          userEmail: profile.email ?? 'No Email',
          userGender: 'Not specified', // We don't collect gender anymore
          phoneNumber: profile.phoneNumber ?? '--',
        ));
      } else {
        // Fallback to SharedPreferences (enhanced user data)
        String? userJson = AppPreferences.getUserdata();
        if (userJson != null && userJson.isNotEmpty) {
          Map<String, dynamic> userData = jsonDecode(userJson);

          String userName;
          String userEmail;
          String phone;

          // Profile incomplete - show phone number and prompt
          userName = userData['displayName'] ?? 'Hello there  👋';
          userEmail = userData['email'] ?? 'Complete your profile';
          phone = userData['phoneNumber'] ?? '--';

          emit(ProfileState.loaded(
            userName: userName,
            userEmail: userEmail,
            userGender: 'Not specified',
            phoneNumber: phone,
          ));
        } else {
          emit(ProfileState.loaded(
            userName: 'Hello there  👋',
            userEmail: 'Please login',
            userGender: 'Not specified',
            phoneNumber: '--',
          ));
        }
      }
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to load user data: $e'));
    }
  }

  Future<void> _mapLogoutToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      getIt<AppBloc>().add(AppEvent.logoutRequested());
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to logout: $e'));
    }
  }
}
