import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/data/mappers/product_mapper.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../products/presentation/widgets/product_card.dart';

class CartItemCard extends StatelessWidget {
  final CartItemModel? item;
  final bool isEditable;

  const CartItemCard({
    super.key,
    required this.item,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Product image
          GestureDetector(
            onTap: () { 
              final sku = item?.skuID?.split('-').first;
                          context.pushNamed(
                            RouteNames.productDetail,
                            pathParameters: {'id': sku ?? ''},
                            extra: {
                              'sku': sku ?? '',
                              'validate_quantity': false,
                              'variant_name': item?.skuID?.split('-').last,
                            },
                          );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: 56.sp,
                height: 56.sp,
                child: CustomImage(
                  imageUrl: item?.imageUrl ?? '',
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),

          // Product details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Product name
                CustomText(
                  item?.name ?? '--',
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: AppColors.neutral600,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textHeight: 1.4,
                ),
                const SizedBox(height: 4),
                CustomText(
                  item?.variantName ?? '--',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.neutral500,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          SizedBox(width: 10),

          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Visibility(
                visible: isEditable,
                child: Padding(
                  padding: const EdgeInsets.only(top: 4, bottom: 6),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 80),
                    child: QuantityTogglerWidget(
                      height: 34,
                      product: ProductMapper.toEntity(
                          ProductModel.fromJson(item?.toJson() ?? {})),
                      showVariantsBottomSheet: false,
                      selectedProduct: ProductMapper.toEntity(
                          ProductModel.fromJson(item?.toJson() ?? {})),
                      checkAvailability: true,
                    ),
                  ),
                ),
              ),
              Row(
                children: [
                  IntrinsicWidth(
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        CustomText(
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          '₹${(item?.price ?? 0).toStringAsFixed(0)}',
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          textHeight: 1.4,
                          color: AppColors.neutral400,
                        ),
                        Transform.rotate(
                          angle: 2.9,
                          child: Container(
                            height: 1,
                            width: double.infinity,
                            color: AppColors.neutral400,
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(width: 6),
                  CustomText(
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    '₹${(item?.discountedPrice ?? 0).toStringAsFixed(0)}',
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    textHeight: 1.4,
                    color: AppColors.neutral700,
                  ),
                  SizedBox(width: 2),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

}