import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../widgets/custom_text.dart';
import '../../bloc/home bloc/home_bloc.dart';
import 'section_most_bought.dart';


class PreviouslyBoughtSection extends StatelessWidget {
  const PreviouslyBoughtSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.neutral150,
      child: BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (previous, current) {
          if (previous is! HomeLoaded) {
            return true;
          }
          if (current is HomeLoaded) {
            return previous.previouslyBought != current.previouslyBought;
          }
          return false; // Don’t rebuild for other transitions
        },
        builder: (context, state) {
          List<ProductEntity>? previouslyBought =
              state.mapOrNull(loaded: (value) => value.previouslyBought);
          // Use AnimatedSwitcher for smooth transitions between loading and loaded states
          return Visibility(
            visible: previouslyBought?.isNotEmpty ?? false,
            child: Column(
              children: [
                SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  child: Row(
                    children: [
                      Expanded(
                        child: CustomText(
                          'Your previous orders',
                          fontSize: 20,
                          fontWeight: FontWeight.w900,
                          color: AppColors.primary700,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.fromLTRB(AppDimensions.screenHzPadding, 10,
                      AppDimensions.screenHzPadding, 20),
                  padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.screenHzPadding),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    switchInCurve: Curves.easeInOut,
                    switchOutCurve: Curves.easeInOut,
                    child: previouslyBought == null
                        ? RepaintBoundary(
                            child: Semantics(
                              label: 'Loading top products',
                              hint:
                                  'Please wait while top products are loading',
                              child: Column(
                                key: const ValueKey('most_bought_skeleton'),
                                children: [
                                  // Use enhanced shimmer with staggered loading for better visual feedback
                                  ProductGrid2(
                                    productList: null,
                                    useEnhancedShimmer: true,
                                    sourceSection: 'previous_orders',
                                  ),
                                ],
                              ),
                            ),
                          )
                        : previouslyBought.isEmpty
                            ? const SizedBox
                                .shrink() // Don't show anything if list is empty
                            : RepaintBoundary(
                                child: Column(
                                  key: const ValueKey('most_bought_loaded'),
                                  children: [
                                    ProductGrid2(productList: previouslyBought, sourceSection: 'previous_orders'),
                                  ],
                                ),
                              ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
