import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../domain/entities/product_entity.dart';

part 'free_product_state.freezed.dart';

@freezed
abstract class FreeProductState with _$FreeProductState {
  const factory FreeProductState({
    @Default(false) bool isLoading,
    @Default(false) bool isCheckingOffer,
    @Default([]) List<ProductEntity> freeProducts,
    @Default([]) List<ProductEntity> selectedFreeProducts,
    String? error,
    String? offerProductId,
    String? offerVariantId,
    String? sku,
    @Default(false) bool hasActiveOffer,
  }) = _FreeProductState;

  factory FreeProductState.initial() => const FreeProductState();

  factory FreeProductState.loading() => const FreeProductState(
        isLoading: true,
      );

  factory FreeProductState.checkingOffer() => const FreeProductState(
        isCheckingOffer: true,
      );

  factory FreeProductState.offerFound({
    required String productId,
    required String variantId,
    required String sku,
  }) =>
      FreeProductState(
        hasActiveOffer: true,
        offerProductId: productId,
        offerVariantId: variantId,
        sku: sku,
      );

  factory FreeProductState.productsLoaded({
    required List<ProductEntity> products,
    required String productId,
    required String variantId,
    required String sku,
    List<ProductEntity>? selectedFreeProducts,
  }) =>
      FreeProductState(
        freeProducts: products,
        hasActiveOffer: true,
        offerProductId: productId,
        offerVariantId: variantId,
        sku: sku,
        selectedFreeProducts: selectedFreeProducts ?? [],
      );

  factory FreeProductState.error(String message) => FreeProductState(
        error: message,
      );

  factory FreeProductState.noOffer() => const FreeProductState(
        hasActiveOffer: false,
        freeProducts: [],
        selectedFreeProducts: [],
      );
}
