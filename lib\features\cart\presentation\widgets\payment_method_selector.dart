import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/services/analytics_events/payment_analytics_events.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_text.dart';

class PaymentMethodSelector extends StatelessWidget {
  final String selectedMethod;
  final Function(String) onMethodSelected;
  final Map<String, dynamic>? cartData; // Cart data for analytics
  final bool showWalletOption;
  final double walletBalance;
  final bool hasOndc;

  const PaymentMethodSelector({
    super.key,
    required this.selectedMethod,
    required this.onMethodSelected,
    this.cartData, // Optional cart data for analytics tracking
    this.showWalletOption = false,
    this.walletBalance = 0.0,
    required this.hasOndc,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(12.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cash on Delivery
          PaymentOptionTile(
            id: 'cod',
            title: 'Cash on delivery',
            subtitle: 'Pay with cash at delivery time',
            logo: 'assets/new/icons/account_balance_wallet.png',
            selectedMethod: selectedMethod,
            onMethodSelected: onMethodSelected,
            cartData: cartData,
          ),
          // Online Payment with Razorpay
          Visibility(
            visible: !hasOndc,
            child: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: PaymentOptionTile(
                id: 'razorpay',
                title: 'Online payment',
                subtitle: 'UPI / Credit, Debit card / Netbanking',
                logo: 'assets/new/icons/credit_card.png',
                selectedMethod: selectedMethod,
                onMethodSelected: onMethodSelected,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PaymentOptionTile extends StatelessWidget {
  const PaymentOptionTile(
      {super.key,
      required this.selectedMethod,
      required this.onMethodSelected,
      required this.id,
      required this.title,
      required this.subtitle,
      required this.logo,
      this.cartData});

  final String selectedMethod;
  final Function(String) onMethodSelected;

  final String id;
  final String title;
  final String subtitle;
  final String logo;
  final Map<String, dynamic>? cartData;

  @override
  Widget build(BuildContext context) {
    final isSelected = selectedMethod == id;
    return InkWell(
      onTap: () {
        onMethodSelected(id);
        // Track payment method selection
        PaymentAnalyticsEvents().trackPaymentMethodSelected(
          methodId: id,
          cartData: cartData ?? {},
        );
      },
      child: Row(
        children: [
          SizedBox.square(
            dimension: 20.sp,
            child: Image.asset(
              logo,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  title,
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: AppColors.neutral600,
                  textHeight: 1.4,
                ),
                const SizedBox(height: 2),
                CustomText(
                  subtitle,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.neutral500,
                  textHeight: 1.4,
                ),
              ],
            ),
          ),
          Container(
            width: 24,
            height: 24,
            padding: EdgeInsets.all(4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.primary200,
              ),
              color: isSelected ? AppColors.primary : Colors.transparent,
            ),
            child:
                isSelected ? Image.asset('assets/new/icons/check.png') : null,
          )
        ],
      ),
    );
  }

}
