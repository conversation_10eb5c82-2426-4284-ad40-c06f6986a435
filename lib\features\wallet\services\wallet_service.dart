import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:rozana/core/config/environment_config.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/data/models/wallet_model.dart';

class WalletService {
  final Dio _dio = Dio();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Fetches the wallet balance for the current user
  Future<WalletModel?> getWalletBalance() async {
    try {
      // Get current user ID from Firebase Auth
      final User? user = _auth.currentUser;
      if (user == null) {
        LogMessage.p('WalletService: User not authenticated');
        return null;
      }

      final String userId = user.uid;
      final String endpoint = 'wallet-entry/$userId/balance';
      final String baseUrl = EnvironmentConfig.walletBaseUrl;
      final String apiKey = EnvironmentConfig.walletAPIKey;

      // Configure Dio with base URL and headers
      _dio.options.baseUrl = baseUrl;
      _dio.options.headers = {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
      };

      // Make API request
      final response = await _dio.get(endpoint);
      
      if (response.statusCode == 200) {
        return WalletModel.fromJson(response.data);
      } else {
        LogMessage.p('WalletService: Failed to fetch wallet balance - ${response.statusCode}');
        return null;
      }
    } catch (e) {
      LogMessage.p('WalletService: Error fetching wallet balance - $e');
      return null;
    }
  }
}
