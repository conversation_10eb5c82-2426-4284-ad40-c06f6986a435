import 'package:flutter/foundation.dart' show kIsWeb;
import 'places_service_interface.dart';

// Import platform-specific implementations
import 'places_service_mobile.dart';
// Use conditional imports for web implementation
import 'places_service_web_stub.dart' if (dart.library.html) 'places_service_web.dart';

/// Factory class to create the appropriate platform-specific Places service
class PlacesServiceFactory {
  /// Creates and returns the appropriate Places service implementation
  /// based on the current platform
  static PlacesServiceInterface createPlacesService() {
    if (kIsWeb) {
      // Web implementation
      return PlacesServiceWeb();
    } else {
      // Mobile implementation
      return PlacesServiceMobile();
    }
  }
}
