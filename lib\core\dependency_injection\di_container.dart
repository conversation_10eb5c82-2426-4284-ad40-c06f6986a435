import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:rozana/data/repositories/order_repository_impl.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';

// Global instance of GetIt
final GetIt getIt = GetIt.instance;

// Global Keys
final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Setup dependency injection
void setupDependencies() {
  // Register repositories
  if (!getIt.isRegistered<OrderRepositoryInterface>()) {
    getIt.registerLazySingleton<OrderRepositoryInterface>(
      () => OrderRepositoryImpl(),
    );
  }
}
