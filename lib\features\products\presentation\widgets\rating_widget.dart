import 'package:flutter/material.dart';

import '../../../../core/themes/color_schemes.dart';

class RatingWidget extends StatelessWidget {
  final double rating;
  final int? ratingCount;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool showLabel;
  final TextStyle? labelStyle;
  final MainAxisAlignment alignment;

  /// Widget to display product rating with stars
  const RatingWidget({
    super.key,
    required this.rating,
    this.ratingCount,
    this.size = 16,
    this.activeColor,
    this.inactiveColor,
    this.showLabel = true,
    this.labelStyle,
    this.alignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: alignment,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(5, (index) {
            return Icon(
              index < rating.floor()
                  ? Icons.star
                  : index < rating
                      ? Icons.star_half
                      : Icons.star_border,
              color: index < rating
                  ? activeColor ?? AppColors.warning
                  : inactiveColor ?? Colors.grey.shade400,
              size: size,
            );
          }),
        ),
        if (showLabel) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: labelStyle ??
                TextStyle(
                  fontSize: size * 0.9,
                  fontWeight: FontWeight.bold,
                  color: AppColors.neutral600,
                ),
          ),
          if (ratingCount != null) ...[
            const SizedBox(width: 2),
            Text(
              '($ratingCount)',
              style: labelStyle?.copyWith(
                    fontWeight: FontWeight.normal,
                    color: AppColors.neutral400,
                  ) ??
                  TextStyle(
                    fontSize: size * 0.8,
                    color: AppColors.neutral400,
                  ),
            ),
          ],
        ],
      ],
    );
  }
}
