// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AppState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppState()';
  }
}

/// @nodoc
class $AppStateCopyWith<$Res> {
  $AppStateCopyWith(AppState _, $Res Function(AppState) __);
}

/// Adds pattern-matching-related methods to [AppState].
extension AppStatePatterns on AppState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AppInitial value)? initial,
    TResult Function(_AppLoading value)? loading,
    TResult Function(_AppLoaded value)? loaded,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppInitial() when initial != null:
        return initial(_that);
      case _AppLoading() when loading != null:
        return loading(_that);
      case _AppLoaded() when loaded != null:
        return loaded(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AppInitial value) initial,
    required TResult Function(_AppLoading value) loading,
    required TResult Function(_AppLoaded value) loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _AppInitial():
        return initial(_that);
      case _AppLoading():
        return loading(_that);
      case _AppLoaded():
        return loaded(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AppInitial value)? initial,
    TResult? Function(_AppLoading value)? loading,
    TResult? Function(_AppLoaded value)? loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _AppInitial() when initial != null:
        return initial(_that);
      case _AppLoading() when loading != null:
        return loading(_that);
      case _AppLoaded() when loaded != null:
        return loaded(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(bool isAuthenticated)? loaded,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AppInitial() when initial != null:
        return initial();
      case _AppLoading() when loading != null:
        return loading();
      case _AppLoaded() when loaded != null:
        return loaded(_that.isAuthenticated);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(bool isAuthenticated) loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _AppInitial():
        return initial();
      case _AppLoading():
        return loading();
      case _AppLoaded():
        return loaded(_that.isAuthenticated);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(bool isAuthenticated)? loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _AppInitial() when initial != null:
        return initial();
      case _AppLoading() when loading != null:
        return loading();
      case _AppLoaded() when loaded != null:
        return loaded(_that.isAuthenticated);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _AppInitial implements AppState {
  const _AppInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _AppInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppState.initial()';
  }
}

/// @nodoc

class _AppLoading implements AppState {
  const _AppLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _AppLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppState.loading()';
  }
}

/// @nodoc

class _AppLoaded implements AppState {
  const _AppLoaded({required this.isAuthenticated});

  final bool isAuthenticated;

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AppLoadedCopyWith<_AppLoaded> get copyWith =>
      __$AppLoadedCopyWithImpl<_AppLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AppLoaded &&
            (identical(other.isAuthenticated, isAuthenticated) ||
                other.isAuthenticated == isAuthenticated));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isAuthenticated);

  @override
  String toString() {
    return 'AppState.loaded(isAuthenticated: $isAuthenticated)';
  }
}

/// @nodoc
abstract mixin class _$AppLoadedCopyWith<$Res>
    implements $AppStateCopyWith<$Res> {
  factory _$AppLoadedCopyWith(
          _AppLoaded value, $Res Function(_AppLoaded) _then) =
      __$AppLoadedCopyWithImpl;
  @useResult
  $Res call({bool isAuthenticated});
}

/// @nodoc
class __$AppLoadedCopyWithImpl<$Res> implements _$AppLoadedCopyWith<$Res> {
  __$AppLoadedCopyWithImpl(this._self, this._then);

  final _AppLoaded _self;
  final $Res Function(_AppLoaded) _then;

  /// Create a copy of AppState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isAuthenticated = null,
  }) {
    return _then(_AppLoaded(
      isAuthenticated: null == isAuthenticated
          ? _self.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
