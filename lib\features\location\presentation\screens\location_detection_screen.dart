import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';

class LocationDetectionScreen extends StatefulWidget {
  final Function(AddressModel?) onAddressSelected;
  final bool showSkip;

  const LocationDetectionScreen({
    super.key,
    required this.onAddressSelected,
    this.showSkip = true,
  });

  @override
  State<LocationDetectionScreen> createState() =>
      _LocationDetectionScreenState();
}

class _LocationDetectionScreenState extends State<LocationDetectionScreen> {
  final AddressService _addressService = AddressService();
  final LocationService _locationService = LocationService();
  bool _isLoading = true;
  String _statusMessage = 'Detecting your location...';
  AddressModel? _detectedAddress;
  bool _permissionDenied = false;

  @override
  void initState() {
    super.initState();
    _detectLocation();
  }

  Future<void> _detectLocation() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Detecting your location...';
    });

    try {
      // Check if we have any saved addresses
      final addresses = await _addressService.getAllAddresses();

      if (addresses.isNotEmpty) {
        // Use smart address selection (Zepto/Blinkit style)
        final selectedAddress = await _addressService.getSelectedAddress();

        if (selectedAddress != null) {
          setState(() {
            _detectedAddress = selectedAddress;
            _isLoading = false;
            _statusMessage = 'Location detected!';
          });
          return;
        }
      }

      // No addresses or couldn't find nearest, try to auto-detect
      final hasPermission = await _locationService.checkLocationPermission();

      if (!hasPermission) {
        final permissionGranted =
            await _locationService.requestLocationPermission();

        if (!permissionGranted) {
          // Check if permission is permanently denied
          final shouldOpenSettings =
              await _locationService.shouldOpenAppSettings();
          setState(() {
            _permissionDenied = true;
            _isLoading = false;
            _statusMessage = shouldOpenSettings
                ? 'Location permission permanently denied. Please enable in Settings.'
                : 'Location permission denied';
          });
          return;
        }
      }

      // Get current location details (no auto-save)
      final currentPosition = await _addressService.getCurrentPosition();
      if (currentPosition != null) {
        final placemarks = await _addressService.getAddressFromCoordinates(
          currentPosition.latitude,
          currentPosition.longitude,
        );
        if (placemarks != null && placemarks.isNotEmpty) {
          final address = _addressService.placemarkToAddressModel(
            placemarks.first,
            currentPosition,
            isDefault: false, // Don't auto-save
          );

          setState(() {
            _detectedAddress = address;
            _isLoading = false;
            _statusMessage = 'Current location detected!';
          });
          return;
        }
      }

      // If location detection fails
      final address = null;

      setState(() {
        _detectedAddress = address;
        _isLoading = false;
        _statusMessage = address != null
            ? 'Location detected!'
            : 'Could not detect location';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Failed to detect location';
      });
    }
  }

  void _confirmAddress() {
    widget.onAddressSelected(_detectedAddress);
  }

  void _skipLocationDetection() {
    widget.onAddressSelected(null);
  }

  void _navigateToAddressSelection() {
    context.push(RouteNames.addresses, extra: {
      'selectMode': true,
      'onAddressSelected': widget.onAddressSelected,
    });
  }

  void _navigateToAddAddress() {
    context.push(RouteNames.addresses).then((_) {
      _detectLocation();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              _buildHeader(),
              const SizedBox(height: 40),
              _isLoading
                  ? _buildLoadingState()
                  : _permissionDenied
                      ? _buildPermissionDeniedState()
                      : _detectedAddress != null
                          ? _buildAddressDetectedState()
                          : _buildLocationNotDetectedState(),
              const Spacer(),
              _buildBottomButtons(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.location_on,
          size: 64,
          color: AppColors.primary,
        ),
        const SizedBox(height: 16),
        Text(
          'Location Services',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.neutral600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'We need your location to show nearby stores and delivery options',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color: AppColors.neutral400,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 24),
        Text(
          _statusMessage,
          style: TextStyle(
            fontSize: 16,
            color: AppColors.neutral400,
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionDeniedState() {
    return Column(
      children: [
        Icon(
          Icons.location_disabled,
          size: 64,
          color: Colors.grey,
        ),
        const SizedBox(height: 16),
        Text(
          'Location Permission Denied',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.neutral600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Please enable location permissions in your device settings to use location features.',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.neutral400,
          ),
        ),
        const SizedBox(height: 24),
        AppButton(
          text: 'Try Again',
          onPressed: _detectLocation,
          isOutlined: true,
        ),
      ],
    );
  }

  Widget _buildAddressDetectedState() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Address Detected',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
        const SizedBox(height: 16),
        AppButton(
          text: 'Use This Address',
          onPressed: _confirmAddress,
        ),
        const SizedBox(height: 12),
        AppButton(
          text: 'Choose Another Address',
          onPressed: _navigateToAddressSelection,
          isOutlined: true,
        ),
      ],
    );
  }

  Widget _buildLocationNotDetectedState() {
    return Column(
      children: [
        Icon(
          Icons.location_off,
          size: 64,
          color: Colors.grey,
        ),
        const SizedBox(height: 16),
        Text(
          'Could Not Detect Location',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.neutral600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'We were unable to detect your current location. Please try again or add a new address manually.',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.neutral400,
          ),
        ),
        const SizedBox(height: 24),
        AppButton(
          text: 'Try Again',
          onPressed: _detectLocation,
        ),
        const SizedBox(height: 12),
        AppButton(
          text: 'Add Address Manually',
          onPressed: _navigateToAddAddress,
          isOutlined: true,
        ),
      ],
    );
  }

  Widget _buildBottomButtons() {
    if (_isLoading || !widget.showSkip) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        if (widget.showSkip)
          TextButton(
            onPressed: _skipLocationDetection,
            child: Text(
              'Skip for now',
              style: TextStyle(
                color: AppColors.neutral400,
              ),
            ),
          ),
      ],
    );
  }
}
