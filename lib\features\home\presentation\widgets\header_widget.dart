import 'package:flutter/material.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_text.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({super.key, required this.title});
  final String title;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/images/header_line_left.png',
          height: 3,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: CustomText(
            title,
            fontWeight: FontWeight.w700,
            fontSize: 18,
            color: AppColors.primary,
            textAlign: TextAlign.center,
          ),
        ),
        Image.asset(
          'assets/images/header_line_right.png',
          height: 3,
        ),
      ],
    );
  }
}
