import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/domain/entities/product_entity.dart';

part 'product_detail_event.freezed.dart';

@freezed
abstract class ProductDetailEvent with _$ProductDetailEvent {
  const factory ProductDetailEvent.fetchProduct({
    required String sku,
    required String variantName,
    required bool validateQuantity,
  }) = _FetchProduct;

  const factory ProductDetailEvent.switchVariant({
    required ProductEntity variant,
  }) = _SwitchVariant;

  const factory ProductDetailEvent.fetchSimilarProducts({
    required String categoryId,
    required String excludeProductId,
  }) = _FetchSimilarProducts;

  const factory ProductDetailEvent.checkUomQuantity(
      {required List<num?> uoms,
      required String whSku,
      required String facilityId,
      required ProductEntity parentEntity,
      required ProductEntity selectedVariant}) = _ProductCheckUomAvailability;
}
