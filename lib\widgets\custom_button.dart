import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rozana/widgets/custom_border.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/themes/color_schemes.dart';

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double height;
  final double borderRadius;
  final EdgeInsets padding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color? borderColor;

  const AppButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height = 50,
    this.borderRadius = 8,
    this.padding = const EdgeInsets.symmetric(horizontal: 16),
    this.prefixIcon,
    this.suffixIcon,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: isOutlined
          ? OutlinedButton(
              onPressed: isLoading ? () {} : onPressed,
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: backgroundColor ??
                      (isLoading ? AppColors.primary100 : AppColors.primary),
                  width: 1.5,
                ),
                shape: ContinuousRectangleBorder(
                  borderRadius: BorderRadius.circular((borderRadius * 2.5).sp),
                ),
                padding: padding,
              ),
              child: _buildButtonContent(),
            )
          : ElevatedButton(
              onPressed: isLoading ? () {} : onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: backgroundColor ??
                    (isLoading ? AppColors.primary100 : AppColors.primary),
                foregroundColor: textColor ?? Colors.white,
                shape: ContinuousRectangleBorder(
                  borderRadius: BorderRadius.circular((borderRadius * 2.5).sp),
                ),
                padding: padding,
              ),
              child: _buildButtonContent(),
            ),
    );
  }

  Widget _buildButtonContent() {
    return isLoading
        ? const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (prefixIcon != null) ...[
                prefixIcon!,
                const SizedBox(width: 8),
              ],
              CustomText(
                text,
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: isOutlined
                    ? (textColor ?? AppColors.primary)
                    : (textColor ?? AppColors.neutral100),
              ),
              if (suffixIcon != null) ...[
                const SizedBox(width: 8),
                suffixIcon!,
              ],
            ],
          );
  }
}

// For backward compatibility
class CustomButton extends AppButton {
  const CustomButton({
    super.key,
    required super.text,
    required super.onPressed,
    super.isLoading,
    super.isOutlined,
    super.backgroundColor,
    super.textColor,
    super.width,
    super.height,
    super.borderRadius,
    super.padding,
    super.prefixIcon,
    super.suffixIcon,
  });
}

class AppIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final double iconSize;
  final bool isOutlined;
  final bool isDisabled;
  final String? tooltip;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final BorderSide? border;
  final double elevation;

  /// A customizable icon button with consistent styling
  const AppIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.size = 40,
    this.iconSize = 20,
    this.isOutlined = false,
    this.isDisabled = false,
    this.tooltip,
    this.padding = const EdgeInsets.all(8),
    this.borderRadius = 8,
    this.border,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = isDisabled
        ? Colors.grey.shade200
        : backgroundColor ??
            (isOutlined ? Colors.transparent : AppColors.primary);

    final effectiveIconColor = isDisabled
        ? Colors.grey.shade400
        : iconColor ?? (isOutlined ? AppColors.primary : Colors.white);

    final BorderSide? effectiveBorder = isOutlined
        ? border ?? BorderSide(color: AppColors.primary, width: 1.5)
        : border;

    final button = CustomBorder(
      color: effectiveBackgroundColor,
      radius: borderRadius,
      side: effectiveBorder,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
        ),
        child: Icon(
          icon,
          color: effectiveIconColor,
          size: iconSize,
        ),
      ),
    );

    if (isDisabled) {
      return button;
    }

    return tooltip != null
        ? Tooltip(
            message: tooltip!,
            child: _buildInkWell(button),
          )
        : _buildInkWell(button);
  }

  Widget _buildInkWell(Widget child) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(borderRadius),
      child: child,
    );
  }
}
