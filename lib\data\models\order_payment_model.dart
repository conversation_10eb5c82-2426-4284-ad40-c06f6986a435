/// Model representing a payment method in an order
class OrderPaymentModel {
  /// Payment mode (e.g., 'razorpay', 'cod', 'wallet')
  final String paymentMode;

  /// Whether to create a payment order (for online payments)
  final bool createPaymentOrder;

  /// Payment amount
  final double amount;

  /// Additional payment metadata (optional)
  final Map<String, dynamic>? metadata;

  /// Constructor
  OrderPaymentModel({
    required this.paymentMode,
    this.createPaymentOrder = false,
    required this.amount,
    this.metadata,
  });

  /// Create from JSON
  factory OrderPaymentModel.fromJson(Map<String, dynamic> json) {
    return OrderPaymentModel(
      paymentMode: json['payment_mode'] as String,
      createPaymentOrder: json['create_payment_order'] as bool? ?? false,
      amount: (json['amount'] is int)
          ? (json['amount'] as int).toDouble()
          : json['amount'] as double,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{
      'payment_mode': paymentMode,
      'create_payment_order': createPaymentOrder,
      'amount': amount,
    };
    
    if (metadata != null) {
      data['metadata'] = metadata;
    }
    
    return data;
  }
}
