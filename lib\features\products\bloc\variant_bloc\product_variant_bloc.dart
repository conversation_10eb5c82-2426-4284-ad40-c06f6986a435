export 'product_variant_event.dart';
export 'product_variant_state.dart';

import 'package:bloc/bloc.dart';
import 'package:rozana/core/utils/logger.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/network/api_endpoints.dart';
import '../../../../core/network/service_api_client.dart';

import 'product_variant_event.dart';
import 'product_variant_state.dart';

class ProductVariantBloc
    extends Bloc<ProductVariantEvent, ProductVariantState> {
  ProductVariantBloc() : super(ProductVariantState.loading()) {
    on<CartCheckUomAvailability>(
      (event, emit) => _onCheckUOMAvailability(
          whSku: event.whSku,
          uom: event.uoms,
          facilityId: event.facilityId,
          emit),
    );
  }

  Future<dynamic> _onCheckUOMAvailability(Emitter<ProductVariantState> emit,
      {required String whSku,
      required List<num?> uom,
      required String facilityId}) async {
    try {
      var response = await ServiceApiClient.sendImsRequest(
          endUrl: EndUrl.getUOMStockAvailability,
          method: HttpMethod.get,
          queryParameters: {
            "facility": facilityId,
            whSku: (uom).join(','),
          });

      Map dataList = response?.data['data']?[whSku] ?? {};

      final Map<String, num> data = dataList.map((key, value) {
        final calculatedQty = value["calculated_qty"] as num;
        return MapEntry(key, calculatedQty);
      });

      emit(ProductVariantState.loaded(variantQuantityData: data));
    } catch (e) {
      LogMessage.p("Failed to check uom availability => $e");
    }
  }
}
