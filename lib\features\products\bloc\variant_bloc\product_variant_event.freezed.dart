// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_variant_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductVariantEvent {
  List<num?> get uoms;
  String get whSku;
  String get facilityId;

  /// Create a copy of ProductVariantEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProductVariantEventCopyWith<ProductVariantEvent> get copyWith =>
      _$ProductVariantEventCopyWithImpl<ProductVariantEvent>(
          this as ProductVariantEvent, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProductVariantEvent &&
            const DeepCollectionEquality().equals(other.uoms, uoms) &&
            (identical(other.whSku, whSku) || other.whSku == whSku) &&
            (identical(other.facilityId, facilityId) ||
                other.facilityId == facilityId));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(uoms), whSku, facilityId);

  @override
  String toString() {
    return 'ProductVariantEvent(uoms: $uoms, whSku: $whSku, facilityId: $facilityId)';
  }
}

/// @nodoc
abstract mixin class $ProductVariantEventCopyWith<$Res> {
  factory $ProductVariantEventCopyWith(
          ProductVariantEvent value, $Res Function(ProductVariantEvent) _then) =
      _$ProductVariantEventCopyWithImpl;
  @useResult
  $Res call({List<num?> uoms, String whSku, String facilityId});
}

/// @nodoc
class _$ProductVariantEventCopyWithImpl<$Res>
    implements $ProductVariantEventCopyWith<$Res> {
  _$ProductVariantEventCopyWithImpl(this._self, this._then);

  final ProductVariantEvent _self;
  final $Res Function(ProductVariantEvent) _then;

  /// Create a copy of ProductVariantEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uoms = null,
    Object? whSku = null,
    Object? facilityId = null,
  }) {
    return _then(_self.copyWith(
      uoms: null == uoms
          ? _self.uoms
          : uoms // ignore: cast_nullable_to_non_nullable
              as List<num?>,
      whSku: null == whSku
          ? _self.whSku
          : whSku // ignore: cast_nullable_to_non_nullable
              as String,
      facilityId: null == facilityId
          ? _self.facilityId
          : facilityId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ProductVariantEvent].
extension ProductVariantEventPatterns on ProductVariantEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartCheckUomAvailability value)? checkUomQuantity,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CartCheckUomAvailability() when checkUomQuantity != null:
        return checkUomQuantity(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartCheckUomAvailability value) checkUomQuantity,
  }) {
    final _that = this;
    switch (_that) {
      case CartCheckUomAvailability():
        return checkUomQuantity(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartCheckUomAvailability value)? checkUomQuantity,
  }) {
    final _that = this;
    switch (_that) {
      case CartCheckUomAvailability() when checkUomQuantity != null:
        return checkUomQuantity(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<num?> uoms, String whSku, String facilityId)?
        checkUomQuantity,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CartCheckUomAvailability() when checkUomQuantity != null:
        return checkUomQuantity(_that.uoms, _that.whSku, _that.facilityId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<num?> uoms, String whSku, String facilityId)
        checkUomQuantity,
  }) {
    final _that = this;
    switch (_that) {
      case CartCheckUomAvailability():
        return checkUomQuantity(_that.uoms, _that.whSku, _that.facilityId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<num?> uoms, String whSku, String facilityId)?
        checkUomQuantity,
  }) {
    final _that = this;
    switch (_that) {
      case CartCheckUomAvailability() when checkUomQuantity != null:
        return checkUomQuantity(_that.uoms, _that.whSku, _that.facilityId);
      case _:
        return null;
    }
  }
}

/// @nodoc

class CartCheckUomAvailability implements ProductVariantEvent {
  const CartCheckUomAvailability(
      {required final List<num?> uoms,
      required this.whSku,
      required this.facilityId})
      : _uoms = uoms;

  final List<num?> _uoms;
  @override
  List<num?> get uoms {
    if (_uoms is EqualUnmodifiableListView) return _uoms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_uoms);
  }

  @override
  final String whSku;
  @override
  final String facilityId;

  /// Create a copy of ProductVariantEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CartCheckUomAvailabilityCopyWith<CartCheckUomAvailability> get copyWith =>
      _$CartCheckUomAvailabilityCopyWithImpl<CartCheckUomAvailability>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CartCheckUomAvailability &&
            const DeepCollectionEquality().equals(other._uoms, _uoms) &&
            (identical(other.whSku, whSku) || other.whSku == whSku) &&
            (identical(other.facilityId, facilityId) ||
                other.facilityId == facilityId));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_uoms), whSku, facilityId);

  @override
  String toString() {
    return 'ProductVariantEvent.checkUomQuantity(uoms: $uoms, whSku: $whSku, facilityId: $facilityId)';
  }
}

/// @nodoc
abstract mixin class $CartCheckUomAvailabilityCopyWith<$Res>
    implements $ProductVariantEventCopyWith<$Res> {
  factory $CartCheckUomAvailabilityCopyWith(CartCheckUomAvailability value,
          $Res Function(CartCheckUomAvailability) _then) =
      _$CartCheckUomAvailabilityCopyWithImpl;
  @override
  @useResult
  $Res call({List<num?> uoms, String whSku, String facilityId});
}

/// @nodoc
class _$CartCheckUomAvailabilityCopyWithImpl<$Res>
    implements $CartCheckUomAvailabilityCopyWith<$Res> {
  _$CartCheckUomAvailabilityCopyWithImpl(this._self, this._then);

  final CartCheckUomAvailability _self;
  final $Res Function(CartCheckUomAvailability) _then;

  /// Create a copy of ProductVariantEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? uoms = null,
    Object? whSku = null,
    Object? facilityId = null,
  }) {
    return _then(CartCheckUomAvailability(
      uoms: null == uoms
          ? _self._uoms
          : uoms // ignore: cast_nullable_to_non_nullable
              as List<num?>,
      whSku: null == whSku
          ? _self.whSku
          : whSku // ignore: cast_nullable_to_non_nullable
              as String,
      facilityId: null == facilityId
          ? _self.facilityId
          : facilityId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
