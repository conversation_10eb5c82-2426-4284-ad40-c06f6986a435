import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/data/models/wallet_model.dart';
import 'package:rozana/features/wallet/bloc/wallet_event.dart';
import 'package:rozana/features/wallet/bloc/wallet_state.dart';
import 'package:rozana/features/wallet/services/wallet_service.dart';

class WalletBloc extends Bloc<WalletEvent, WalletState> {
  final WalletService _walletService;

  WalletBloc({required WalletService walletService})
      : _walletService = walletService,
        super(WalletInitial()) {
    on<FetchWalletBalance>(_onFetchWalletBalance);
    on<UpdateWalletBalance>(_onUpdateWalletBalance);
  }

  Future<void> _onFetchWalletBalance(
      FetchWalletBalance event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    try {
      final wallet = await _walletService.getWalletBalance();
      if (wallet != null) {
        emit(WalletLoaded(wallet));
      } else {
        emit(const WalletError('Failed to fetch wallet balance'));
      }
    } catch (e) {
      emit(WalletError('Error: $e'));
    }
  }

  void _onUpdateWalletBalance(
      UpdateWalletBalance event, Emitter<WalletState> emit) {
    if (state is WalletLoaded) {
      final currentWallet = (state as WalletLoaded).wallet;
      final updatedWallet = WalletModel(
        userId: currentWallet.userId,
        currentBalance: event.newBalance,
      );
      emit(WalletLoaded(updatedWallet));
    }
  }
}
