import 'package:freezed_annotation/freezed_annotation.dart';
part 'login_state.freezed.dart';

@freezed
abstract class LoginState with _$LoginState {
  const factory LoginState.initial({
    @Default('') String mobile,
    @Default(false) bool showError,
    @Default(false) bool isLoading,
    @Default(false) bool isLoginValid,
    @Default(false) bool isOTPValid,
  }) = _LoginInitial;

  const factory LoginState.otp({
    @Default('') String mobile,
    @Default('') String otp,
    @Default('') String verificationId,
    @Default(false) bool showError,
    @Default(false) bool isLoading,
    @Default(false) bool canResend,
    @Default(30) int resendSeconds,
    @Default(false) bool isLoginValid,
    @Default(false) bool isOTPValid,
  }) = _LoginOTP;
}
