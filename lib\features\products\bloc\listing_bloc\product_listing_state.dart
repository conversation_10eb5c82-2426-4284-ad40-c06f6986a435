import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/category_entity.dart';
part 'product_listing_state.freezed.dart';

@freezed
class ProductListingState with _$ProductListingState {
  const factory ProductListingState.initial() = _Initial;
  const factory ProductListingState.loading() = _Loading;

  const factory ProductListingState.loaded({
    CategoryEntity? category,
    CategoryEntity? subCategory,
  }) = _Loaded;

  const factory ProductListingState.error(String message) = _Error;

  const factory ProductListingState.productBySkuError(String message) =
      _ProductBySkuError;
}
