import 'package:flutter/material.dart';

/// A custom overlay widget for the barcode scanner that provides
/// a modern and attractive scanning interface.
class ScannerOverlay extends StatelessWidget {
  /// Color for the overlay (semi-transparent area around the scan window)
  final Color overlayColor;

  /// Color for the border of the scan window
  final Color borderColor;

  /// Animation controller for scan effects
  final AnimationController scanAnimationController;

  /// Animation for scan effects
  final Animation<double> animation;

  const ScannerOverlay({
    super.key,
    required this.overlayColor,
    required this.borderColor,
    required this.scanAnimationController,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    // Get the screen size to calculate the scan window dimensions
    final size = MediaQuery.of(context).size;

    // Calculate the scan window size (70% of the smaller dimension)
    final scanWindowSize =
        size.width < size.height ? size.width * 0.7 : size.height * 0.7;

    return Stack(
      children: [
        // Full screen overlay with cutout for scan window
        CustomPaint(
          size: size,
          painter: OverlayPainter(
            overlayColor: overlayColor,
            borderColor: borderColor,
            scanWindowSize: scanWindowSize,
            animation: animation,
          ),
        ),

        // Scanning line animation
        Center(
          child: SizedBox(
            width: scanWindowSize,
            height: scanWindowSize,
            child: _buildScanningLine(scanWindowSize),
          ),
        ),

        // Corner indicators
        Center(
          child: AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return Transform.scale(
                scale: animation.value,
                child: SizedBox(
                  width: scanWindowSize,
                  height: scanWindowSize,
                  child: _buildCornerIndicators(),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildScanningLine(double scanWindowSize) {
    return AnimatedBuilder(
      animation: scanAnimationController,
      builder: (context, child) {
        return CustomPaint(
          size: Size(scanWindowSize, scanWindowSize),
          painter: ScanningLinePainter(
            scanAnimationController: scanAnimationController,
            borderColor: borderColor,
          ),
        );
      },
    );
  }

  Widget _buildCornerIndicators() {
    return Stack(
      children: [
        // Top left corner
        Positioned(
          left: 0,
          top: 0,
          child: _buildCorner(Alignment.topLeft),
        ),
        // Top right corner
        Positioned(
          right: 0,
          top: 0,
          child: _buildCorner(Alignment.topRight),
        ),
        // Bottom left corner
        Positioned(
          left: 0,
          bottom: 0,
          child: _buildCorner(Alignment.bottomLeft),
        ),
        // Bottom right corner
        Positioned(
          right: 0,
          bottom: 0,
          child: _buildCorner(Alignment.bottomRight),
        ),
      ],
    );
  }

  Widget _buildCorner(Alignment alignment) {
    // Determine rotation based on corner position
    double quarterTurns = 0;
    if (alignment == Alignment.topRight) quarterTurns = 1;
    if (alignment == Alignment.bottomRight) quarterTurns = 2;
    if (alignment == Alignment.bottomLeft) quarterTurns = 3;

    return RotatedBox(
      quarterTurns: quarterTurns.toInt(),
      child: SizedBox(
        width: 40,
        height: 40,
        child: CustomPaint(
          painter: CornerPainter(borderColor: borderColor),
        ),
      ),
    );
  }
}

/// Custom painter for the overlay with a cutout for the scan window
class OverlayPainter extends CustomPainter {
  final Color overlayColor;
  final Color borderColor;
  final double scanWindowSize;
  final Animation<double> animation;

  OverlayPainter({
    required this.overlayColor,
    required this.borderColor,
    required this.scanWindowSize,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    // Calculate the scan window position
    final scanWindowLeft = (size.width - scanWindowSize) / 2;
    final scanWindowTop = (size.height - scanWindowSize) / 2;

    // Draw the overlay with a cutout for the scan window
    final path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRect(Rect.fromLTWH(
        scanWindowLeft,
        scanWindowTop,
        scanWindowSize,
        scanWindowSize,
      ))
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant OverlayPainter oldDelegate) {
    return oldDelegate.overlayColor != overlayColor ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.scanWindowSize != scanWindowSize ||
        oldDelegate.animation != animation;
  }
}

/// Custom painter for the scanning line animation
class ScanningLinePainter extends CustomPainter {
  final AnimationController scanAnimationController;
  final Color borderColor;

  ScanningLinePainter({
    required this.scanAnimationController,
    required this.borderColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = borderColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // Calculate the y position for the scanning line based on the animation value
    final y = size.height * scanAnimationController.value;

    // Create a gradient for the scanning line
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        borderColor.withValues(alpha: 0.0),
        borderColor.withValues(alpha: 0.8),
        borderColor.withValues(alpha: 0.0),
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    // Apply the gradient to the paint
    paint.shader = gradient.createShader(
      Rect.fromLTWH(0, y - 10, size.width, 20),
    );

    // Draw the scanning line
    canvas.drawLine(
      Offset(0, y),
      Offset(size.width, y),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant ScanningLinePainter oldDelegate) {
    return oldDelegate.scanAnimationController != scanAnimationController ||
        oldDelegate.borderColor != borderColor;
  }
}

/// Custom painter for the corner indicators
class CornerPainter extends CustomPainter {
  final Color borderColor;

  CornerPainter({required this.borderColor});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    // Draw the L-shaped corner
    final path = Path()
      ..moveTo(0, size.height * 0.4)
      ..lineTo(0, 0)
      ..lineTo(size.width * 0.4, 0);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CornerPainter oldDelegate) {
    return oldDelegate.borderColor != borderColor;
  }
}
