import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomText extends Text {
  const CustomText(
    this.text, {
    this.fontSize,
    this.fontWeight,
    this.color,
    this.textHeight,
    super.key,
    super.style,
    super.strutStyle,
    super.textAlign,
    super.textDirection,
    super.locale,
    super.softWrap,
    super.overflow = TextOverflow.ellipsis,
    super.textScaleFactor,
    super.maxLines,
    super.semanticsLabel,
    super.textWidthBasis,
    super.textHeightBehavior,
    super.selectionColor,
    this.decoration,
  }) : super(text);

  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final double? textHeight;
  final TextDecoration? decoration;

  @override
  TextStyle? get style {
    final inheritedStyle = super.style;
    return (inheritedStyle ?? const TextStyle()).copyWith(
      fontSize: fontSize != null ? (fontSize!).sp : null,
      fontWeight: fontWeight,
      color: color,
      height: textHeight,
      decoration: decoration,
      fontFamily: 'Mukta',
    );
  }
}

class MainHeaderText extends CustomText {
  const MainHeaderText(
    this.headerText, {
    super.key,
    super.color,
    super.fontSize = 24,
    super.fontWeight = FontWeight.w600,
    super.textHeight = 1.4,
    super.textAlign,
    super.overflow = TextOverflow.visible,
    super.maxLines = 2,
  }) : super(headerText);

  final String headerText;
}

class SubHeaderText extends CustomText {
  const SubHeaderText(
    this.headerText, {
    super.key,
    super.color,
    super.fontSize = 14,
    super.fontWeight = FontWeight.w500,
    super.textHeight,
    super.textAlign,
    super.overflow = TextOverflow.ellipsis,
    super.maxLines = 3,
  }) : super(headerText);

  final String headerText;
}

class ButtonText extends CustomText {
  const ButtonText(
    this.headerText, {
    super.key,
    super.color,
    super.fontSize = 14,
    super.fontWeight = FontWeight.w600,
    super.textHeight,
    super.textAlign,
    super.overflow = TextOverflow.clip,
    super.maxLines = 1,
  }) : super(headerText);

  final String headerText;
}
