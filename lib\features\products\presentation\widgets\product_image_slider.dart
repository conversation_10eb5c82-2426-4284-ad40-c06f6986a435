import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';
import 'package:rozana/core/services/analytics_events/product_analytics_events.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/widgets/image_page_view.dart';
import 'package:rozana/routes/app_router.dart';

class ProductImageSlider extends StatefulWidget {
  final List<String> images;
  final double aspectRatio;
  final bool showIndicators;
  final Map<String, dynamic>? productData; // Product data for analytics
  final double horizontalPadding;

  const ProductImageSlider({
    super.key,
    required this.images,
    this.aspectRatio = 1.0,
    this.showIndicators = true,
    this.horizontalPadding = 0.0,
    this.productData, // Optional product data for analytics tracking
  });

  @override
  State<ProductImageSlider> createState() => _ProductImageSliderState();
}

class _ProductImageSliderState extends State<ProductImageSlider> {
  late PageController _pageController;
  int _currentIndex = 0;
  static const double _borderRadius = 8.0;
  static const double _indicatorSize = 8.0;
  static const double _indicatorSpacing = 3.0;
  static const String _placeholderAsset = 'assets/images/image-placeholder.jpg';

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Test analytics service initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _testAnalyticsService();
    });
  }

  void _testAnalyticsService() {
    try {
      final analyticsService = getIt<AnalyticsService>();
      debugPrint(
          'Analytics: Service test - isInitialized: ${analyticsService.isInitialized}');
    } catch (e) {
      debugPrint('Analytics: Service test failed - $e');
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If no images or empty list, show placeholder
    if (widget.images.isEmpty) {
      return _buildPlaceholderImage();
    }

    // If only one image, show it without PageView
    if (widget.images.length == 1) {
      return _buildSingleImage(widget.images.first);
    }

    // Multiple images - show slider
    return _buildImageSlider();
  }

  /// Common method to build ImagePageView with consistent styling
  Widget _buildImagePageView({
    required List<String> images,
    required Function(int) onImageTap,
    PageController? controller,
    Function(int)? onPageChanged,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.horizontalPadding),
      child: AspectRatio(
        aspectRatio: widget.aspectRatio,
        child: ImagePageView(
          images: images,
          controller: controller,
          onPageChanged: onPageChanged,
          onImageTap: onImageTap,
          fit: BoxFit.contain,
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return _buildImagePageView(
      images: const [], // Empty list will show placeholder
      onImageTap: (index) => _openFullScreenViewer(0),
    );
  }

  Widget _buildSingleImage(String imageUrl) {
    return _buildImagePageView(
      images: [imageUrl],
      onImageTap: (index) => _openFullScreenViewer(0),
    );
  }

  Widget _buildImageSlider() {
    return Stack(
      children: [
        _buildImagePageView(
          images: widget.images,
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          onImageTap: (index) => _openFullScreenViewer(index),
        ),

        // Page indicators
        if (widget.showIndicators && widget.images.length > 1)
          Positioned(
            bottom: 10,
            right: 20,
            child: _buildPageIndicators(),
          ),
      ],
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.images.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: _indicatorSpacing),
          width: _indicatorSize,
          height: _indicatorSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentIndex == index
                ? AppColors.primary
                : AppColors.primary.withAlpha(80),
          ),
        ),
      ),
    );
  }

  void _openFullScreenViewer(int initialIndex) {
    // If no images, use placeholder
    final imagesToShow =
        widget.images.isEmpty ? [_placeholderAsset] : widget.images;

    context.push(RouteNames.fullScreenImageViewer, extra: {
      'images': imagesToShow,
      'initialIndex': initialIndex,
    });
  }

  void _trackImageScroll(String direction) {
    if (widget.productData != null) {
      ProductAnalyticsEvents().trackPDPImageScroll(
        productSkuId: widget.productData!['sku'] ?? '',
        productName: widget.productData!['name'] ?? '',
        mrp: (widget.productData!['originalPrice'] ?? 0).toString(),
        sellingPrice: (widget.productData!['price'] ?? 0).toString(),
        discount: ((widget.productData!['originalPrice'] ?? 0) -
                (widget.productData!['price'] ?? 0))
            .toString(),
        categoryName: 'Unknown',
        subCategoryName: 'Unknown',
        imageScrollDirection: direction,
        screenName: 'Product Detail Screen',
      );
    }
  }
}
