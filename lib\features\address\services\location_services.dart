import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:rozana/core/utils/logger.dart';

class AddressLocationService {
  Future<Position?> getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        LogMessage.p('Location services are disabled');
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          LogMessage.p('Location permission denied by user');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        LogMessage.p('Location permission permanently denied');
        return null;
      }

      try {
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 10),
        );
      } catch (e) {
        LogMessage.p('High accuracy failed, trying medium accuracy: $e');
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 15),
        );
      }
    } catch (e) {
      LogMessage.p('Error getting current position: $e');
      return null;
    }
  }

  Future<List<Placemark>> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      return await placemarkFromCoordinates(latitude, longitude);
    } catch (e) {
      LogMessage.p('Error getting address from coordinates: $e');
      return [];
    }
  }

  Future<List<Location>> searchAddresses(String query) async {
    try {
      return await locationFromAddress(query);
    } catch (e) {
      LogMessage.p('Error searching addresses: $e');
      return [];
    }
  }
}
