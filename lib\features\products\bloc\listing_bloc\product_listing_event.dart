import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/category_entity.dart';

part 'product_listing_event.freezed.dart';

@freezed
class ProductListingEvent with _$ProductListingEvent {
  const factory ProductListingEvent.initial({
    CategoryEntity? category,
  }) = _Initial;

  const factory ProductListingEvent.selectSubcategory({
    CategoryEntity? subCategory,
  }) = _SelectSubcategory;

  const factory ProductListingEvent.loadProductsByCategory({
    required String categoryId,
    required String excludeProductId,
    required int page,
    required int pageSize,
    required bool refresh,
  }) = _LoadProductsByCategory;

  const factory ProductListingEvent.loadProductsByBrand({
    required String brandId,
    required String excludeProductId,
    required int page,
    required int pageSize,
    required bool refresh,
  }) = _LoadProductsByBrand;
   const factory ProductListingEvent.reset() = _Reset;
}
