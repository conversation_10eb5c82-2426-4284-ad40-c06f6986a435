import 'dart:async';
import 'package:flutter/material.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';

import '../themes/color_schemes.dart';

/// Abstract notification service for handling in-app notifications
abstract class AppNotifier {
  /// Show a snackbar notification
  void showSnackBar(String message,
      {bool isError = false, EdgeInsetsGeometry? margin});

  /// Show a toast notification
  void showToast(String message, {bool isError = false});

  /// Show a dialog notification
  Future<void> showDialog({
    required String title,
    required String message,
    String? positiveButtonText,
    String? negativeButtonText,
    VoidCallback? onPositivePressed,
    VoidCallback? onNegativePressed,
  });

  /// Show a loading dialog
  Future<void> showLoading({String? message});

  /// Hide the current loading dialog
  void hideLoading();
}

/// Implementation of NotificationService
class NotificationServiceImpl implements AppNotifier {
  final GlobalKey<ScaffoldMessengerState> _scaffoldKey;
  bool _isLoading = false;

  NotificationServiceImpl(this._scaffoldKey);

  @override
  void showSnackBar(String message,
      {bool isError = false, EdgeInsetsGeometry? margin}) {
    if (_scaffoldKey.currentState != null) {
      _scaffoldKey.currentState!.clearSnackBars();
      _scaffoldKey.currentState!.showSnackBar(
        SnackBar(
            margin: margin,
            content: Text(message),
            backgroundColor: isError ? Colors.red : AppColors.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2)),
      );
    }
  }

  @override
  void showToast(String message, {bool isError = false}) {
    // For now, we'll use snackbar as toast
    // In a real app, you might want to use a dedicated toast package
    showSnackBar(message, isError: isError);
  }

  @override
  Future<void> showDialog({
    required String title,
    required String message,
    String? positiveButtonText,
    String? negativeButtonText,
    VoidCallback? onPositivePressed,
    VoidCallback? onNegativePressed,
  }) async {
    final context = _scaffoldKey.currentContext;
    if (context == null) return;

    await showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      pageBuilder: (BuildContext buildContext, _, __) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            if (negativeButtonText != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (onNegativePressed != null) {
                    onNegativePressed();
                  }
                },
                child: Text(negativeButtonText),
              ),
            if (positiveButtonText != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (onPositivePressed != null) {
                    onPositivePressed();
                  }
                },
                child: Text(positiveButtonText),
              ),
          ],
        );
      },
    );
  }

  @override
  Future<void> showLoading({String? message}) async {
    if (_isLoading) return;

    final context = navigatorKey.currentContext;
    if (context == null) return;

    _isLoading = true;

    await showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      pageBuilder: (BuildContext buildContext, _, __) {
        return PopScope(
          canPop: false,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  if (message != null) ...[
                    const SizedBox(height: 16),
                    Text(message),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void hideLoading() {
    if (!_isLoading) return;

    final context = navigatorKey.currentContext;
    if (context == null) return;

    _isLoading = false;
    Navigator.of(context, rootNavigator: true).pop();
  }
}
