import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/widgets/custom_text.dart';

class MapBottomSheet extends StatelessWidget {
  final AddressModel? temporaryAddress;
  final VoidCallback onConfirmLocation;
  final bool isAuthenticated;

  const MapBottomSheet({
    super.key,
    required this.temporaryAddress,
    required this.onConfirmLocation,
    required this.isAuthenticated,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.neutral100,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomText(
            'Delivering your order to',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColors.primary700,
          ),
          const SizedBox(height: 12),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Color(0xFFF7F7F7),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.neutral200,
                width: 0.5,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        ((temporaryAddress?.fullAddress?.split(',').first) ??
                                temporaryAddress?.addressLine1) ??
                            'Select a location on the map',
                        fontSize: 14,
                        color: AppColors.primary700,
                        fontWeight: FontWeight.w700,
                        maxLines: 1,
                      ),
                      SizedBox(height: 4),
                      CustomText(
                        ((temporaryAddress?.fullAddress
                                    ?.split(',')
                                    .sublist(
                                        1,
                                        temporaryAddress?.fullAddress
                                            ?.split(',')
                                            .length)
                                    .join(',')) ??
                                temporaryAddress?.fullAddress) ??
                            '...',
                        fontSize: 14,
                        color: AppColors.neutral500,
                        fontWeight: FontWeight.w500,
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          GestureDetector(
            onTap: onConfirmLocation,
            child: Container(
              height: 48.sp,
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
              decoration: BoxDecoration(
                color: temporaryAddress != null
                    ? AppColors.primary
                    : AppColors.primary100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomText(
                    isAuthenticated
                        ? 'Add more address details'
                        : 'Select address',
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: AppColors.neutral100,
                  ),
                  Visibility(
                    visible: isAuthenticated,
                    child: Icon(
                      Icons.arrow_right,
                      color: AppColors.neutral150,
                    ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
