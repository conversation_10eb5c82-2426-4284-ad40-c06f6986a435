import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'dart:convert';
class AnalyticsDataService {
  static final AnalyticsDataService _instance = AnalyticsDataService._internal();
  factory AnalyticsDataService() => _instance;
  AnalyticsDataService._internal();

  Future<String?> getMobileNumber() async {
    try {
      // Get user data from SharedPreferences
      final userDataString = AppPreferences.getUserdata();

      if (userDataString != null && userDataString.isNotEmpty) {
        final userData = jsonDecode(userDataString);
        final phoneNumber = userData['phoneNumber'] ?? userData['phone'] ?? userData['mobile'];

        if (phoneNumber != null && phoneNumber.toString().isNotEmpty) {
          return phoneNumber.toString();
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<String> getGeolocation() async {
      try {
        final typesenseService = getIt<TypesenseService>();
        final storeName = typesenseService.currentStoreName;

        if (storeName != null && storeName.isNotEmpty) {
          return storeName;
        }
      } catch (e) {
        debugPrint('Error getting store name from TypesenseService: $e');
      }
      return 'Amethi_DS';
  }


  Future<String> getDeliverySLA() async {
      try {
        final addressBloc = getIt<AddressBloc>();
        final state = addressBloc.state;
        final duration = state.delliveryDuration;
        if (duration != null && duration.isNotEmpty) {
          return 'Delivery in $duration';
        }
      } catch (_) {}

      return 'Delivery time not available';
  }
  Future<Map<String, String?>> getAllAnalyticsData() async {
    try {
      final mobileNumber = await getMobileNumber();
      final geolocation = await getGeolocation();
      final deliverySLA = await getDeliverySLA();

      return {
        'mobileNumber': mobileNumber,
        'geolocation': geolocation,
        'durationText': deliverySLA,
      };
    } catch (e) {
      return {
        'mobileNumber': null,
        'geolocation': 'Unknown',
        'durationText': 'Delivery time not available',
      };
    }
  }


  Future<Map<String, String>> getAnalyticsDataWithFallbacks() async {
    try {
      final mobileNumber = await getMobileNumber();
      final geolocation = await getGeolocation();
      final deliverySLA = await getDeliverySLA();

      return {
        'mobileNumber': mobileNumber ?? '',
        'geolocation': geolocation,
        'durationText': deliverySLA,
      };
    } catch (e) {
      return {
        'mobileNumber': '',
        'geolocation': 'Unknown',
        'durationText': 'Delivery time not available',
      };
    }
  }
}