// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'address_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AddressEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AddressEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddressEvent()';
  }
}

/// @nodoc
class $AddressEventCopyWith<$Res> {
  $AddressEventCopyWith(AddressEvent _, $Res Function(AddressEvent) __);
}

/// Adds pattern-matching-related methods to [AddressEvent].
extension AddressEventPatterns on AddressEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Started value)? started,
    TResult Function(_InitMap value)? initMap,
    TResult Function(_SelectFromMap value)? selectFromMap,
    TResult Function(_InitSearch value)? initSearch,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_DeleteAddress value)? deleteAddress,
    TResult Function(_FetchAddresses value)? fetchAddresses,
    TResult Function(_SelectAddress value)? selectAddress,
    TResult Function(_MapCameraIdle value)? mapCameraIdle,
    TResult Function(_SearchPlaces value)? searchPlaces,
    TResult Function(_SelectPlace value)? selectPlace,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_EmitAsyncState value)? emitAsyncState,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that);
      case _Started() when started != null:
        return started(_that);
      case _InitMap() when initMap != null:
        return initMap(_that);
      case _SelectFromMap() when selectFromMap != null:
        return selectFromMap(_that);
      case _InitSearch() when initSearch != null:
        return initSearch(_that);
      case _SaveAddress() when saveAddress != null:
        return saveAddress(_that);
      case _DeleteAddress() when deleteAddress != null:
        return deleteAddress(_that);
      case _FetchAddresses() when fetchAddresses != null:
        return fetchAddresses(_that);
      case _SelectAddress() when selectAddress != null:
        return selectAddress(_that);
      case _MapCameraIdle() when mapCameraIdle != null:
        return mapCameraIdle(_that);
      case _SearchPlaces() when searchPlaces != null:
        return searchPlaces(_that);
      case _SelectPlace() when selectPlace != null:
        return selectPlace(_that);
      case _GetCurrentLocation() when getCurrentLocation != null:
        return getCurrentLocation(_that);
      case _EmitAsyncState() when emitAsyncState != null:
        return emitAsyncState(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Started value) started,
    required TResult Function(_InitMap value) initMap,
    required TResult Function(_SelectFromMap value) selectFromMap,
    required TResult Function(_InitSearch value) initSearch,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_DeleteAddress value) deleteAddress,
    required TResult Function(_FetchAddresses value) fetchAddresses,
    required TResult Function(_SelectAddress value) selectAddress,
    required TResult Function(_MapCameraIdle value) mapCameraIdle,
    required TResult Function(_SearchPlaces value) searchPlaces,
    required TResult Function(_SelectPlace value) selectPlace,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_EmitAsyncState value) emitAsyncState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init():
        return init(_that);
      case _Started():
        return started(_that);
      case _InitMap():
        return initMap(_that);
      case _SelectFromMap():
        return selectFromMap(_that);
      case _InitSearch():
        return initSearch(_that);
      case _SaveAddress():
        return saveAddress(_that);
      case _DeleteAddress():
        return deleteAddress(_that);
      case _FetchAddresses():
        return fetchAddresses(_that);
      case _SelectAddress():
        return selectAddress(_that);
      case _MapCameraIdle():
        return mapCameraIdle(_that);
      case _SearchPlaces():
        return searchPlaces(_that);
      case _SelectPlace():
        return selectPlace(_that);
      case _GetCurrentLocation():
        return getCurrentLocation(_that);
      case _EmitAsyncState():
        return emitAsyncState(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Started value)? started,
    TResult? Function(_InitMap value)? initMap,
    TResult? Function(_SelectFromMap value)? selectFromMap,
    TResult? Function(_InitSearch value)? initSearch,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_DeleteAddress value)? deleteAddress,
    TResult? Function(_FetchAddresses value)? fetchAddresses,
    TResult? Function(_SelectAddress value)? selectAddress,
    TResult? Function(_MapCameraIdle value)? mapCameraIdle,
    TResult? Function(_SearchPlaces value)? searchPlaces,
    TResult? Function(_SelectPlace value)? selectPlace,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_EmitAsyncState value)? emitAsyncState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init(_that);
      case _Started() when started != null:
        return started(_that);
      case _InitMap() when initMap != null:
        return initMap(_that);
      case _SelectFromMap() when selectFromMap != null:
        return selectFromMap(_that);
      case _InitSearch() when initSearch != null:
        return initSearch(_that);
      case _SaveAddress() when saveAddress != null:
        return saveAddress(_that);
      case _DeleteAddress() when deleteAddress != null:
        return deleteAddress(_that);
      case _FetchAddresses() when fetchAddresses != null:
        return fetchAddresses(_that);
      case _SelectAddress() when selectAddress != null:
        return selectAddress(_that);
      case _MapCameraIdle() when mapCameraIdle != null:
        return mapCameraIdle(_that);
      case _SearchPlaces() when searchPlaces != null:
        return searchPlaces(_that);
      case _SelectPlace() when selectPlace != null:
        return selectPlace(_that);
      case _GetCurrentLocation() when getCurrentLocation != null:
        return getCurrentLocation(_that);
      case _EmitAsyncState() when emitAsyncState != null:
        return emitAsyncState(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(bool startMap, bool applyFilter)? started,
    TResult Function()? initMap,
    TResult Function(AddressModel? address)? selectFromMap,
    TResult Function()? initSearch,
    TResult Function()? saveAddress,
    TResult Function(String addressId)? deleteAddress,
    TResult Function(bool filterServiceStatus)? fetchAddresses,
    TResult Function(String addressId)? selectAddress,
    TResult Function(double latitude, double longitude)? mapCameraIdle,
    TResult Function(String query)? searchPlaces,
    TResult Function(String placeId)? selectPlace,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressState state)? emitAsyncState,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init();
      case _Started() when started != null:
        return started(_that.startMap, _that.applyFilter);
      case _InitMap() when initMap != null:
        return initMap();
      case _SelectFromMap() when selectFromMap != null:
        return selectFromMap(_that.address);
      case _InitSearch() when initSearch != null:
        return initSearch();
      case _SaveAddress() when saveAddress != null:
        return saveAddress();
      case _DeleteAddress() when deleteAddress != null:
        return deleteAddress(_that.addressId);
      case _FetchAddresses() when fetchAddresses != null:
        return fetchAddresses(_that.filterServiceStatus);
      case _SelectAddress() when selectAddress != null:
        return selectAddress(_that.addressId);
      case _MapCameraIdle() when mapCameraIdle != null:
        return mapCameraIdle(_that.latitude, _that.longitude);
      case _SearchPlaces() when searchPlaces != null:
        return searchPlaces(_that.query);
      case _SelectPlace() when selectPlace != null:
        return selectPlace(_that.placeId);
      case _GetCurrentLocation() when getCurrentLocation != null:
        return getCurrentLocation();
      case _EmitAsyncState() when emitAsyncState != null:
        return emitAsyncState(_that.state);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(bool startMap, bool applyFilter) started,
    required TResult Function() initMap,
    required TResult Function(AddressModel? address) selectFromMap,
    required TResult Function() initSearch,
    required TResult Function() saveAddress,
    required TResult Function(String addressId) deleteAddress,
    required TResult Function(bool filterServiceStatus) fetchAddresses,
    required TResult Function(String addressId) selectAddress,
    required TResult Function(double latitude, double longitude) mapCameraIdle,
    required TResult Function(String query) searchPlaces,
    required TResult Function(String placeId) selectPlace,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressState state) emitAsyncState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init():
        return init();
      case _Started():
        return started(_that.startMap, _that.applyFilter);
      case _InitMap():
        return initMap();
      case _SelectFromMap():
        return selectFromMap(_that.address);
      case _InitSearch():
        return initSearch();
      case _SaveAddress():
        return saveAddress();
      case _DeleteAddress():
        return deleteAddress(_that.addressId);
      case _FetchAddresses():
        return fetchAddresses(_that.filterServiceStatus);
      case _SelectAddress():
        return selectAddress(_that.addressId);
      case _MapCameraIdle():
        return mapCameraIdle(_that.latitude, _that.longitude);
      case _SearchPlaces():
        return searchPlaces(_that.query);
      case _SelectPlace():
        return selectPlace(_that.placeId);
      case _GetCurrentLocation():
        return getCurrentLocation();
      case _EmitAsyncState():
        return emitAsyncState(_that.state);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(bool startMap, bool applyFilter)? started,
    TResult? Function()? initMap,
    TResult? Function(AddressModel? address)? selectFromMap,
    TResult? Function()? initSearch,
    TResult? Function()? saveAddress,
    TResult? Function(String addressId)? deleteAddress,
    TResult? Function(bool filterServiceStatus)? fetchAddresses,
    TResult? Function(String addressId)? selectAddress,
    TResult? Function(double latitude, double longitude)? mapCameraIdle,
    TResult? Function(String query)? searchPlaces,
    TResult? Function(String placeId)? selectPlace,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressState state)? emitAsyncState,
  }) {
    final _that = this;
    switch (_that) {
      case _Init() when init != null:
        return init();
      case _Started() when started != null:
        return started(_that.startMap, _that.applyFilter);
      case _InitMap() when initMap != null:
        return initMap();
      case _SelectFromMap() when selectFromMap != null:
        return selectFromMap(_that.address);
      case _InitSearch() when initSearch != null:
        return initSearch();
      case _SaveAddress() when saveAddress != null:
        return saveAddress();
      case _DeleteAddress() when deleteAddress != null:
        return deleteAddress(_that.addressId);
      case _FetchAddresses() when fetchAddresses != null:
        return fetchAddresses(_that.filterServiceStatus);
      case _SelectAddress() when selectAddress != null:
        return selectAddress(_that.addressId);
      case _MapCameraIdle() when mapCameraIdle != null:
        return mapCameraIdle(_that.latitude, _that.longitude);
      case _SearchPlaces() when searchPlaces != null:
        return searchPlaces(_that.query);
      case _SelectPlace() when selectPlace != null:
        return selectPlace(_that.placeId);
      case _GetCurrentLocation() when getCurrentLocation != null:
        return getCurrentLocation();
      case _EmitAsyncState() when emitAsyncState != null:
        return emitAsyncState(_that.state);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Init implements AddressEvent {
  const _Init();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Init);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddressEvent.init()';
  }
}

/// @nodoc

class _Started implements AddressEvent {
  const _Started({this.startMap = false, this.applyFilter = false});

  @JsonKey()
  final bool startMap;
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$StartedCopyWith<_Started> get copyWith =>
      __$StartedCopyWithImpl<_Started>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Started &&
            (identical(other.startMap, startMap) ||
                other.startMap == startMap) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startMap, applyFilter);

  @override
  String toString() {
    return 'AddressEvent.started(startMap: $startMap, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$StartedCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$StartedCopyWith(_Started value, $Res Function(_Started) _then) =
      __$StartedCopyWithImpl;
  @useResult
  $Res call({bool startMap, bool applyFilter});
}

/// @nodoc
class __$StartedCopyWithImpl<$Res> implements _$StartedCopyWith<$Res> {
  __$StartedCopyWithImpl(this._self, this._then);

  final _Started _self;
  final $Res Function(_Started) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? startMap = null,
    Object? applyFilter = null,
  }) {
    return _then(_Started(
      startMap: null == startMap
          ? _self.startMap
          : startMap // ignore: cast_nullable_to_non_nullable
              as bool,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _InitMap implements AddressEvent {
  const _InitMap();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InitMap);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddressEvent.initMap()';
  }
}

/// @nodoc

class _SelectFromMap implements AddressEvent {
  const _SelectFromMap({this.address});

  final AddressModel? address;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectFromMapCopyWith<_SelectFromMap> get copyWith =>
      __$SelectFromMapCopyWithImpl<_SelectFromMap>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectFromMap &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  @override
  String toString() {
    return 'AddressEvent.selectFromMap(address: $address)';
  }
}

/// @nodoc
abstract mixin class _$SelectFromMapCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$SelectFromMapCopyWith(
          _SelectFromMap value, $Res Function(_SelectFromMap) _then) =
      __$SelectFromMapCopyWithImpl;
  @useResult
  $Res call({AddressModel? address});
}

/// @nodoc
class __$SelectFromMapCopyWithImpl<$Res>
    implements _$SelectFromMapCopyWith<$Res> {
  __$SelectFromMapCopyWithImpl(this._self, this._then);

  final _SelectFromMap _self;
  final $Res Function(_SelectFromMap) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? address = freezed,
  }) {
    return _then(_SelectFromMap(
      address: freezed == address
          ? _self.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
    ));
  }
}

/// @nodoc

class _InitSearch implements AddressEvent {
  const _InitSearch();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InitSearch);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddressEvent.initSearch()';
  }
}

/// @nodoc

class _SaveAddress implements AddressEvent {
  const _SaveAddress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SaveAddress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddressEvent.saveAddress()';
  }
}

/// @nodoc

class _DeleteAddress implements AddressEvent {
  const _DeleteAddress(this.addressId);

  final String addressId;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeleteAddressCopyWith<_DeleteAddress> get copyWith =>
      __$DeleteAddressCopyWithImpl<_DeleteAddress>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeleteAddress &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressId);

  @override
  String toString() {
    return 'AddressEvent.deleteAddress(addressId: $addressId)';
  }
}

/// @nodoc
abstract mixin class _$DeleteAddressCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$DeleteAddressCopyWith(
          _DeleteAddress value, $Res Function(_DeleteAddress) _then) =
      __$DeleteAddressCopyWithImpl;
  @useResult
  $Res call({String addressId});
}

/// @nodoc
class __$DeleteAddressCopyWithImpl<$Res>
    implements _$DeleteAddressCopyWith<$Res> {
  __$DeleteAddressCopyWithImpl(this._self, this._then);

  final _DeleteAddress _self;
  final $Res Function(_DeleteAddress) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addressId = null,
  }) {
    return _then(_DeleteAddress(
      null == addressId
          ? _self.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _FetchAddresses implements AddressEvent {
  const _FetchAddresses({this.filterServiceStatus = false});

  @JsonKey()
  final bool filterServiceStatus;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FetchAddressesCopyWith<_FetchAddresses> get copyWith =>
      __$FetchAddressesCopyWithImpl<_FetchAddresses>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FetchAddresses &&
            (identical(other.filterServiceStatus, filterServiceStatus) ||
                other.filterServiceStatus == filterServiceStatus));
  }

  @override
  int get hashCode => Object.hash(runtimeType, filterServiceStatus);

  @override
  String toString() {
    return 'AddressEvent.fetchAddresses(filterServiceStatus: $filterServiceStatus)';
  }
}

/// @nodoc
abstract mixin class _$FetchAddressesCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$FetchAddressesCopyWith(
          _FetchAddresses value, $Res Function(_FetchAddresses) _then) =
      __$FetchAddressesCopyWithImpl;
  @useResult
  $Res call({bool filterServiceStatus});
}

/// @nodoc
class __$FetchAddressesCopyWithImpl<$Res>
    implements _$FetchAddressesCopyWith<$Res> {
  __$FetchAddressesCopyWithImpl(this._self, this._then);

  final _FetchAddresses _self;
  final $Res Function(_FetchAddresses) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? filterServiceStatus = null,
  }) {
    return _then(_FetchAddresses(
      filterServiceStatus: null == filterServiceStatus
          ? _self.filterServiceStatus
          : filterServiceStatus // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _SelectAddress implements AddressEvent {
  const _SelectAddress(this.addressId);

  final String addressId;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectAddressCopyWith<_SelectAddress> get copyWith =>
      __$SelectAddressCopyWithImpl<_SelectAddress>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectAddress &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, addressId);

  @override
  String toString() {
    return 'AddressEvent.selectAddress(addressId: $addressId)';
  }
}

/// @nodoc
abstract mixin class _$SelectAddressCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$SelectAddressCopyWith(
          _SelectAddress value, $Res Function(_SelectAddress) _then) =
      __$SelectAddressCopyWithImpl;
  @useResult
  $Res call({String addressId});
}

/// @nodoc
class __$SelectAddressCopyWithImpl<$Res>
    implements _$SelectAddressCopyWith<$Res> {
  __$SelectAddressCopyWithImpl(this._self, this._then);

  final _SelectAddress _self;
  final $Res Function(_SelectAddress) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addressId = null,
  }) {
    return _then(_SelectAddress(
      null == addressId
          ? _self.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _MapCameraIdle implements AddressEvent {
  const _MapCameraIdle({required this.latitude, required this.longitude});

  final double latitude;
  final double longitude;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MapCameraIdleCopyWith<_MapCameraIdle> get copyWith =>
      __$MapCameraIdleCopyWithImpl<_MapCameraIdle>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MapCameraIdle &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude);

  @override
  String toString() {
    return 'AddressEvent.mapCameraIdle(latitude: $latitude, longitude: $longitude)';
  }
}

/// @nodoc
abstract mixin class _$MapCameraIdleCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$MapCameraIdleCopyWith(
          _MapCameraIdle value, $Res Function(_MapCameraIdle) _then) =
      __$MapCameraIdleCopyWithImpl;
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class __$MapCameraIdleCopyWithImpl<$Res>
    implements _$MapCameraIdleCopyWith<$Res> {
  __$MapCameraIdleCopyWithImpl(this._self, this._then);

  final _MapCameraIdle _self;
  final $Res Function(_MapCameraIdle) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_MapCameraIdle(
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _SearchPlaces implements AddressEvent {
  const _SearchPlaces(this.query);

  final String query;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchPlacesCopyWith<_SearchPlaces> get copyWith =>
      __$SearchPlacesCopyWithImpl<_SearchPlaces>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchPlaces &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  @override
  String toString() {
    return 'AddressEvent.searchPlaces(query: $query)';
  }
}

/// @nodoc
abstract mixin class _$SearchPlacesCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$SearchPlacesCopyWith(
          _SearchPlaces value, $Res Function(_SearchPlaces) _then) =
      __$SearchPlacesCopyWithImpl;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$SearchPlacesCopyWithImpl<$Res>
    implements _$SearchPlacesCopyWith<$Res> {
  __$SearchPlacesCopyWithImpl(this._self, this._then);

  final _SearchPlaces _self;
  final $Res Function(_SearchPlaces) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
  }) {
    return _then(_SearchPlaces(
      null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SelectPlace implements AddressEvent {
  const _SelectPlace(this.placeId);

  final String placeId;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectPlaceCopyWith<_SelectPlace> get copyWith =>
      __$SelectPlaceCopyWithImpl<_SelectPlace>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectPlace &&
            (identical(other.placeId, placeId) || other.placeId == placeId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, placeId);

  @override
  String toString() {
    return 'AddressEvent.selectPlace(placeId: $placeId)';
  }
}

/// @nodoc
abstract mixin class _$SelectPlaceCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$SelectPlaceCopyWith(
          _SelectPlace value, $Res Function(_SelectPlace) _then) =
      __$SelectPlaceCopyWithImpl;
  @useResult
  $Res call({String placeId});
}

/// @nodoc
class __$SelectPlaceCopyWithImpl<$Res> implements _$SelectPlaceCopyWith<$Res> {
  __$SelectPlaceCopyWithImpl(this._self, this._then);

  final _SelectPlace _self;
  final $Res Function(_SelectPlace) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? placeId = null,
  }) {
    return _then(_SelectPlace(
      null == placeId
          ? _self.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _GetCurrentLocation implements AddressEvent {
  const _GetCurrentLocation();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetCurrentLocation);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AddressEvent.getCurrentLocation()';
  }
}

/// @nodoc

class _EmitAsyncState implements AddressEvent {
  const _EmitAsyncState(this.state);

  final AddressState state;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EmitAsyncStateCopyWith<_EmitAsyncState> get copyWith =>
      __$EmitAsyncStateCopyWithImpl<_EmitAsyncState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EmitAsyncState &&
            (identical(other.state, state) || other.state == state));
  }

  @override
  int get hashCode => Object.hash(runtimeType, state);

  @override
  String toString() {
    return 'AddressEvent.emitAsyncState(state: $state)';
  }
}

/// @nodoc
abstract mixin class _$EmitAsyncStateCopyWith<$Res>
    implements $AddressEventCopyWith<$Res> {
  factory _$EmitAsyncStateCopyWith(
          _EmitAsyncState value, $Res Function(_EmitAsyncState) _then) =
      __$EmitAsyncStateCopyWithImpl;
  @useResult
  $Res call({AddressState state});

  $AddressStateCopyWith<$Res> get state;
}

/// @nodoc
class __$EmitAsyncStateCopyWithImpl<$Res>
    implements _$EmitAsyncStateCopyWith<$Res> {
  __$EmitAsyncStateCopyWithImpl(this._self, this._then);

  final _EmitAsyncState _self;
  final $Res Function(_EmitAsyncState) _then;

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? state = null,
  }) {
    return _then(_EmitAsyncState(
      null == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as AddressState,
    ));
  }

  /// Create a copy of AddressEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AddressStateCopyWith<$Res> get state {
    return $AddressStateCopyWith<$Res>(_self.state, (value) {
      return _then(_self.copyWith(state: value));
    });
  }
}

// dart format on
