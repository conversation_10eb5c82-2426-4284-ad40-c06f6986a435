import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'skeleton_colors.dart';

class EnhancedShimmer extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Color? baseColor;
  final Color? highlightColor;
  final ShimmerDirection direction;
  final bool enableAnimation;
  final Curve curve;
  final double opacity;

  const EnhancedShimmer({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1000),
    this.baseColor,
    this.highlightColor,
    this.direction = ShimmerDirection.ltr,
    this.enableAnimation = true,
    this.curve = Curves.linear,
    this.opacity = 1.0,
  });

  @override
  State<EnhancedShimmer> createState() => _EnhancedShimmerState();
}

class _EnhancedShimmerState extends State<EnhancedShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    // Start animation by default, will check for reduced motion in didChangeDependencies
    if (widget.enableAnimation) {
      _controller.repeat();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check for reduced motion preference
    final bool reducedMotion = MediaQuery.of(context).disableAnimations;

    // Update animation state based on reduced motion preference
    if (reducedMotion && _controller.isAnimating) {
      _controller.stop();
    } else if (!reducedMotion &&
        widget.enableAnimation &&
        !_controller.isAnimating) {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableAnimation) {
      return widget.child;
    }

    final theme = Theme.of(context);
    final baseColor = widget.baseColor ??
        theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.4);
    final highlightColor = widget.highlightColor ?? theme.colorScheme.surface;

    return Opacity(
      opacity: widget.opacity,
      child: Shimmer.fromColors(
        period: widget.duration,
        direction: widget.direction,
        baseColor: baseColor,
        highlightColor: highlightColor,
        child: widget.child,
      ),
    );
  }
}

/// A staggered shimmer effect that animates children with a slight delay between each
class StaggeredShimmerEffect extends StatelessWidget {
  final List<Widget> children;
  final Duration staggerDuration;
  final Color? baseColor;
  final Color? highlightColor;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const StaggeredShimmerEffect({
    super.key,
    required this.children,
    this.staggerDuration = const Duration(milliseconds: 150),
    this.baseColor,
    this.highlightColor,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisSize = MainAxisSize.min,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: List.generate(
        children.length,
        (index) {
          // Calculate staggered delay based on index
          final delay = Duration(
              milliseconds: (index * staggerDuration.inMilliseconds).round());

          return AnimatedBuilder(
            animation: const AlwaysStoppedAnimation(0),
            builder: (context, child) {
              return FutureBuilder(
                future: Future.delayed(delay),
                builder: (context, snapshot) {
                  final opacity =
                      snapshot.connectionState == ConnectionState.done
                          ? 1.0
                          : 0.0;

                  return AnimatedOpacity(
                    opacity: opacity,
                    duration: const Duration(milliseconds: 150),
                    curve: Curves.easeInOut,
                    child: EnhancedShimmer(
                      baseColor: baseColor,
                      highlightColor: highlightColor,
                      child: children[index],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}

/// Enhanced shimmer box with better animation and customization options
class EnhancedShimmerBox extends StatelessWidget {
  final double? height;
  final double? width;
  final double radius;
  final EdgeInsetsGeometry margin;
  final EdgeInsetsGeometry? padding;
  final Color? baseColor;
  final Color? highlightColor;
  final Color? backgroundColor;
  final BoxBorder? border;
  final List<BoxShadow>? boxShadow;
  final bool staggered;
  final int staggerIndex;

  const EnhancedShimmerBox({
    super.key,
    this.height = 100,
    this.width = double.infinity,
    this.radius = 8,
    this.margin = EdgeInsets.zero,
    this.padding,
    this.baseColor,
    this.highlightColor,
    this.backgroundColor,
    this.border,
    this.boxShadow,
    this.staggered = false,
    this.staggerIndex = 0,
  });

  @override
  Widget build(BuildContext context) {
    final shimmerChild = Container(
      height: height,
      width: width,
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? SkeletonColors.backgroundColor(),
        borderRadius: BorderRadius.circular(radius),
        border: border,
        boxShadow: boxShadow,
      ),
    );

    // Always use non-staggered shimmer effect as per user preference
    return EnhancedShimmer(
      baseColor: baseColor ?? SkeletonColors.baseColor(),
      highlightColor: highlightColor ?? SkeletonColors.highlightColor(),
      child: shimmerChild,
    );
  }
}
