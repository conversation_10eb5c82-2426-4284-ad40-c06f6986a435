// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_variant_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductVariantState {
  Map<String, num>? get variantQuantityData;

  /// Create a copy of ProductVariantState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProductVariantStateCopyWith<ProductVariantState> get copyWith =>
      _$ProductVariantStateCopyWithImpl<ProductVariantState>(
          this as ProductVariantState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProductVariantState &&
            const DeepCollectionEquality()
                .equals(other.variantQuantityData, variantQuantityData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(variantQuantityData));

  @override
  String toString() {
    return 'ProductVariantState(variantQuantityData: $variantQuantityData)';
  }
}

/// @nodoc
abstract mixin class $ProductVariantStateCopyWith<$Res> {
  factory $ProductVariantStateCopyWith(
          ProductVariantState value, $Res Function(ProductVariantState) _then) =
      _$ProductVariantStateCopyWithImpl;
  @useResult
  $Res call({Map<String, num>? variantQuantityData});
}

/// @nodoc
class _$ProductVariantStateCopyWithImpl<$Res>
    implements $ProductVariantStateCopyWith<$Res> {
  _$ProductVariantStateCopyWithImpl(this._self, this._then);

  final ProductVariantState _self;
  final $Res Function(ProductVariantState) _then;

  /// Create a copy of ProductVariantState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? variantQuantityData = freezed,
  }) {
    return _then(_self.copyWith(
      variantQuantityData: freezed == variantQuantityData
          ? _self.variantQuantityData
          : variantQuantityData // ignore: cast_nullable_to_non_nullable
              as Map<String, num>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ProductVariantState].
extension ProductVariantStatePatterns on ProductVariantState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading():
        return loading(_that);
      case _Loaded():
        return loaded(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Map<String, num>? variantQuantityData)? loading,
    TResult Function(Map<String, num>? variantQuantityData)? loaded,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading(_that.variantQuantityData);
      case _Loaded() when loaded != null:
        return loaded(_that.variantQuantityData);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Map<String, num>? variantQuantityData) loading,
    required TResult Function(Map<String, num>? variantQuantityData) loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading():
        return loading(_that.variantQuantityData);
      case _Loaded():
        return loaded(_that.variantQuantityData);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Map<String, num>? variantQuantityData)? loading,
    TResult? Function(Map<String, num>? variantQuantityData)? loaded,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading(_that.variantQuantityData);
      case _Loaded() when loaded != null:
        return loaded(_that.variantQuantityData);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Loading implements ProductVariantState {
  const _Loading({final Map<String, num>? variantQuantityData})
      : _variantQuantityData = variantQuantityData;

  final Map<String, num>? _variantQuantityData;
  @override
  Map<String, num>? get variantQuantityData {
    final value = _variantQuantityData;
    if (value == null) return null;
    if (_variantQuantityData is EqualUnmodifiableMapView)
      return _variantQuantityData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of ProductVariantState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadingCopyWith<_Loading> get copyWith =>
      __$LoadingCopyWithImpl<_Loading>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loading &&
            const DeepCollectionEquality()
                .equals(other._variantQuantityData, _variantQuantityData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_variantQuantityData));

  @override
  String toString() {
    return 'ProductVariantState.loading(variantQuantityData: $variantQuantityData)';
  }
}

/// @nodoc
abstract mixin class _$LoadingCopyWith<$Res>
    implements $ProductVariantStateCopyWith<$Res> {
  factory _$LoadingCopyWith(_Loading value, $Res Function(_Loading) _then) =
      __$LoadingCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, num>? variantQuantityData});
}

/// @nodoc
class __$LoadingCopyWithImpl<$Res> implements _$LoadingCopyWith<$Res> {
  __$LoadingCopyWithImpl(this._self, this._then);

  final _Loading _self;
  final $Res Function(_Loading) _then;

  /// Create a copy of ProductVariantState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? variantQuantityData = freezed,
  }) {
    return _then(_Loading(
      variantQuantityData: freezed == variantQuantityData
          ? _self._variantQuantityData
          : variantQuantityData // ignore: cast_nullable_to_non_nullable
              as Map<String, num>?,
    ));
  }
}

/// @nodoc

class _Loaded implements ProductVariantState {
  const _Loaded({final Map<String, num>? variantQuantityData})
      : _variantQuantityData = variantQuantityData;

  final Map<String, num>? _variantQuantityData;
  @override
  Map<String, num>? get variantQuantityData {
    final value = _variantQuantityData;
    if (value == null) return null;
    if (_variantQuantityData is EqualUnmodifiableMapView)
      return _variantQuantityData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of ProductVariantState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadedCopyWith<_Loaded> get copyWith =>
      __$LoadedCopyWithImpl<_Loaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loaded &&
            const DeepCollectionEquality()
                .equals(other._variantQuantityData, _variantQuantityData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_variantQuantityData));

  @override
  String toString() {
    return 'ProductVariantState.loaded(variantQuantityData: $variantQuantityData)';
  }
}

/// @nodoc
abstract mixin class _$LoadedCopyWith<$Res>
    implements $ProductVariantStateCopyWith<$Res> {
  factory _$LoadedCopyWith(_Loaded value, $Res Function(_Loaded) _then) =
      __$LoadedCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, num>? variantQuantityData});
}

/// @nodoc
class __$LoadedCopyWithImpl<$Res> implements _$LoadedCopyWith<$Res> {
  __$LoadedCopyWithImpl(this._self, this._then);

  final _Loaded _self;
  final $Res Function(_Loaded) _then;

  /// Create a copy of ProductVariantState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? variantQuantityData = freezed,
  }) {
    return _then(_Loaded(
      variantQuantityData: freezed == variantQuantityData
          ? _self._variantQuantityData
          : variantQuantityData // ignore: cast_nullable_to_non_nullable
              as Map<String, num>?,
    ));
  }
}

// dart format on
