import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/data/models/place_suggestion_model.dart';

part 'address_state.freezed.dart';

@freezed
abstract class AddressState with _$AddressState {
  const factory AddressState.initial({
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    @Default(false) bool relocateMap,
    @Default(true) bool isServicable,
    @Default('...') String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _Initial;
  const factory AddressState.loading({
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    @Default(false) bool relocateMap,
    @Default(true) bool isServicable,
    @Default('...') String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _Loading;
  const factory AddressState.addressForm({
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    @Default(false) bool relocateMap,
    @Default(false) bool isServicable,
    @Default(false) bool isEdit,
    String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _AddressForm;
  const factory AddressState.addressList({
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    required bool isLoading,
    required bool hasError,
    @Default(false) bool relocateMap,
    @Default(false) bool isServicable,
    String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _AddressList;

  // Updated State for Map UI
  const factory AddressState.mapSelection({
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    @Default(false) bool relocateMap,
    @Default(false) bool isServicable,
    String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _MapSelection;

  const factory AddressState.searchScreen({
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    List<PlaceSuggestionModel>? searchResults,
    @Default(false) bool isSearching,
    @Default(false) bool relocateMap,
    @Default(false) bool isServicable,
    String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _MapSearch;

  const factory AddressState.error(
    String message, {
    List<AddressModel>? addresses,
    AddressModel? temporaryAddress,
    AddressModel? selectedAddress,
    AddressModel? currentLocation,
    @Default(false) bool relocateMap,
    @Default(false) bool isServicable,
    String? delliveryDuration,
    @Default(false) bool applyFilter,
  }) = _Error;
}
