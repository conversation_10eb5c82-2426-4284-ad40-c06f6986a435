import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/domain/usecases/get_order_history_usecase.dart';
import 'package:rozana/features/order/bloc/order_tracking/order_tracking_event.dart';
import 'package:rozana/features/order/bloc/order_tracking/order_tracking_state.dart';

class OrderTrackingBloc extends Bloc<OrderTrackingEvent, OrderTrackingState> {
  final GetOrderHistoryUseCase _getOrderHistoryUseCase;

  OrderTrackingBloc({required GetOrderHistoryUseCase getOrderHistoryUseCase})
    : _getOrderHistoryUseCase = getOrderHistoryUseCase,
      super(const OrderTrackingInitial()) {
    on<InitializeOrderTracking>(_onInitialize);
    on<FetchPendingOrders>(_onFetchPendingOrders);
    on<HideOrderTrackingWidget>(_onHideWidget);
  }

  void _onInitialize(
    InitializeOrderTracking event,
    Emitter<OrderTrackingState> emit,
  ) {
    add(FetchPendingOrders());
  }

  void _onFetchPendingOrders(
    FetchPendingOrders event,
    Emitter<OrderTrackingState> emit,
  ) async {
    try {
      // Show loading state
      emit(const OrderTrackingLoading());

      // Fetch pending orders
      final pendingOrders = await _getOrderHistoryUseCase.getPendingOrders(
        refresh: true,
      );

      // If no pending orders, emit empty state
      if (pendingOrders.isEmpty) {
        emit(const OrderTrackingEmpty());
        return;
      }

      // Emit loaded state with pending orders
      emit(OrderTrackingLoaded(pendingOrders: pendingOrders));
    } catch (e) {
      // Emit error state
      emit(
        OrderTrackingError(
          message: 'Failed to fetch pending orders: ${e.toString()}',
        ),
      );
    }
  }

  void _onHideWidget(
    HideOrderTrackingWidget event,
    Emitter<OrderTrackingState> emit,
  ) {
    emit(const OrderTrackingEmpty());
  }

  @override
  Future<void> close() {
    return super.close();
  }
}
