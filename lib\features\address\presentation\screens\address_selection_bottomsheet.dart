import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../data/models/adress_model.dart';
import '../../bloc/address_bloc.dart';
import 'address_list_view.dart';

class AddressSelectionBottomSheet extends StatelessWidget {
  const AddressSelectionBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddressBloc, AddressState>(
      builder: (context, state) {
        return state.map(
          initial: (_) => _buildLoadingState(),
          loading: (_) => _buildLoadingState(),
          addressList: (value) {
            List<AddressModel> addresses = value.addresses ?? [];
            AddressModel? selectedAddress = value.selectedAddress;
            bool isLoading = value.isLoading;

            if (isLoading) {
              return _buildLoadingState();
            }
            return DraggableScrollableSheet(
              initialChildSize: 0.9,
              minChildSize: 0.3,
              maxChildSize: 0.9,
              expand: false,
              builder: (context, scrollController) {
                return ClipRRect(
                  borderRadius: BorderRadiusGeometry.circular(16),
                  child: ColoredBox(
                    color: AppColors.neutral150,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ColoredBox(
                          color: AppColors.neutral100,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const SizedBox(height: 12),
                              Container(
                                width: 40,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                  vertical: 12,
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: CustomText(
                                        'Select delivery location',
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700,
                                        color: AppColors.neutral800,
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    GestureDetector(
                                      onTap: () => Navigator.pop(context),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 6, vertical: 4),
                                        child: Icon(Icons.close,
                                            color: AppColors.neutral600),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (addresses.isEmpty)
                          const Expanded(
                            child: Center(
                                child: CustomText('No addresses found.')),
                          ),
                        if (addresses.isNotEmpty)
                          Expanded(
                            child: AddressListView(
                              addresses: addresses,
                              selectedAddress: selectedAddress,
                              scrollController: scrollController,
                              filterServiceStatus: true,
                            ),
                          ),
                        InkWell(
                          onTap: () {
                            HapticFeedback.lightImpact();
                            context.pop();
                            context.push(RouteNames.addresses,
                                extra: {'startMap': true, 'applyFilter' : true});
                          },
                          child: Container(
                              color: AppColors.neutral100,
                              padding:
                                  const EdgeInsets.fromLTRB(16, 20, 16, 20),
                              child: Container(
                                height: 48.sp,
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 5),
                                decoration: BoxDecoration(
                                  color: AppColors.neutral100,
                                  border: Border.all(
                                      color: AppColors.primary500, width: 1.5),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset(
                                      'assets/new/icons/add.png',
                                      color: AppColors.primary500,
                                      width: 24,
                                      height: 24,
                                    ),
                                    SizedBox(width: 10),
                                    CustomText(
                                      'Add new address',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                      color: AppColors.primary500,
                                    ),
                                  ],
                                ),
                              )),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
          // Map and Form views should not be shown in the bottom sheet.
          mapSelection: (_) => const SizedBox.shrink(),
          searchScreen: (_) => const SizedBox.shrink(),
          addressForm: (address) => const SizedBox.shrink(),
          error: (value) => _buildErrorState(value.message),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return const SizedBox(
      height: 200,
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorState(String message) {
    return SizedBox(
      height: 200,
      child: Center(child: Text('Error: $message')),
    );
  }
}
