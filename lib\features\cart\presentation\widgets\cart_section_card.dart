import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rozana/widgets/custom_border.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_text.dart';

class CartSectionCard extends StatelessWidget {
  const CartSectionCard({
    super.key,
    this.cardColor,
    this.logo,
    this.iconColor,
    this.textPrimaryColor,
    this.textSecondaryColor,
    this.dividerColor,
    this.title,
    this.titleText,
    this.subTitle,
    this.iconBackgroundColor,
    required this.children,
    this.trailing,
  });

  final Color? cardColor;
  final Color? iconColor;
  final Color? textPrimaryColor;
  final Color? textSecondaryColor;
  final Color? dividerColor;
  final Widget? title;
  final String? titleText;
  final String? subTitle;
  final String? logo;
  final Widget? trailing;
  final Color? iconBackgroundColor;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return CustomBorder(
      color: cardColor ?? AppColors.neutral100,
      radius: 16,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(12.sp, 10.sp, 12.sp, 8.sp),
            child: Row(
              children: [
                if (logo != null)
                  Container(
                    height: 40.sp,
                    width: 40.sp,
                    decoration: BoxDecoration(
                      color: iconBackgroundColor ?? Color(0xFFF7F7F7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Image.asset(
                        logo!,
                        height: 24.sp,
                        width: 24.sp,
                        color: iconColor ?? AppColors.primary600,
                      ),
                    ),
                  ),
                SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      title ??
                          CustomText(
                            titleText ?? '',
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: textPrimaryColor ?? AppColors.primary700,
                          ),
                      if ((subTitle ?? '').isNotEmpty)
                        CustomText(
                          subTitle ?? '',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: textSecondaryColor ?? AppColors.neutral400,
                        ),
                    ],
                  ),
                ),
                if (trailing != null) trailing!,
              ],
            ),
          ),
          Divider(
            thickness: 0.5,
            color: dividerColor ?? AppColors.neutral150,
            height: 0.5,
          ),
          ...children,
        ],
      ),
    );
  }
}
