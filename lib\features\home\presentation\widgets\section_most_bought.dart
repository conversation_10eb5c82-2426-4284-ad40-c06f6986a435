import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../domain/entities/product_entity.dart';
import '../../../products/presentation/widgets/product_card.dart';
import '../../bloc/home bloc/home_bloc.dart';

class MostBoughtSection extends StatelessWidget {
  const MostBoughtSection({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (previous, current) {
          if (previous is! HomeLoaded) {
            return true;
          }
          if (current is HomeLoaded) {
            return previous.mostBought != current.mostBought;
          }
          return false; // Don’t rebuild for other transitions
        },
        builder: (context, state) {
          List<ProductEntity>? mostBought =
              state.mapOrNull(loaded: (value) => value.mostBought);
          // Use AnimatedSwitcher for smooth transitions between loading and loaded states
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            switchInCurve: Curves.easeInOut,
            switchOutCurve: Curves.easeInOut,
            child: mostBought == null
                ? RepaintBoundary(
                    child: Semantics(
                      label: 'Loading top products',
                      hint: 'Please wait while top products are loading',
                      child: Column(
                        key: const ValueKey('most_bought_skeleton'),
                        children: [
                          GridView.builder(
                            shrinkWrap: true,
                            primary: false,
                            padding: EdgeInsets.symmetric(
                                horizontal: AppDimensions.screenHzPadding),
                            itemCount: 6,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount:
                                        3, // 👈 Set the number of columns here
                                    mainAxisSpacing:
                                        constraints.maxWidth * 0.025,
                                    crossAxisSpacing:
                                        constraints.maxWidth * 0.025,
                                    mainAxisExtent: (constraints.maxWidth /
                                            (constraints.maxWidth * (0.005)))
                                        .sp),
                            itemBuilder: (context, index) {
                              return ProductCardShimmer();
                            },
                          ),
                        ],
                      ),
                    ),
                  )
                : mostBought.isEmpty
                    ? const SizedBox
                        .shrink() // Don't show anything if list is empty
                    : RepaintBoundary(
                        child: Column(
                          key: const ValueKey('most_bought_loaded'),
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppDimensions.screenHzPadding),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: CustomText(
                                      'Frequently bought',
                                      fontSize: 20,
                                      fontWeight: FontWeight.w900,
                                      color: AppColors.primary700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8),
                            // ProductGrid2(productList: mostBought),
                            GridView.builder(
                              shrinkWrap: true,
                              primary: false,
                              padding: EdgeInsets.symmetric(
                                  horizontal: AppDimensions.screenHzPadding),
                              itemCount: mostBought.length,
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount:
                                          3, // 👈 Set the number of columns here
                                      mainAxisSpacing:
                                          constraints.maxWidth * 0.025,
                                      crossAxisSpacing:
                                          constraints.maxWidth * 0.025,
                                      mainAxisExtent: (constraints.maxWidth /
                                              (constraints.maxWidth * (0.005)))
                                          .sp),
                              itemBuilder: (context, index) {
                                return DiscountedProductCard(
                                  product: mostBought[index],
                                  isLoading: false,
                                  sourceSection: 'frequently_bought',
                                );
                              },
                            ),
                          ],
                        ),
                      ),
          );
        },
      );
    });
  }
}

class ProductGrid2 extends StatelessWidget {
  const ProductGrid2({
    super.key,
    required this.productList,
    this.useEnhancedShimmer = true,
    this.sourceSection,
  });

  final List<ProductEntity>? productList;
  final bool useEnhancedShimmer;
  final String? sourceSection;

  @override
  Widget build(BuildContext context) {
    final double itemWidth =
        (MediaQuery.of(context).size.width / 3) - AppDimensions.screenHzPadding;
    // Show shimmer loading state when productList is null
    if (productList == null) {
      return RepaintBoundary(
        child: Semantics(
          label: 'Loading products',
          hint: 'Please wait while products are loading',
          child: SizedBox(
            height: (210.sp * 2) + 8,
            child: GridView.builder(
              shrinkWrap: true,
              primary: false,
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.screenHzPadding),
              scrollDirection: Axis.horizontal,
              itemCount: 6,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                childAspectRatio: 180.sp / itemWidth,
              ),
              itemBuilder: (ctx, index) {
                return ProductCardShimmer();
              },
            ),
          ),
        ),
      );
    }

    int totalLength = productList?.length ?? 0;

    final int crossAxisCount = (totalLength < 6) ? 1 : 2;

    return RepaintBoundary(
      child: Semantics(
        label: 'Product grid',
        hint: '${productList!.length} products available',
        child: SizedBox(
          height: (crossAxisCount == 1) ? 210.sp : (210.sp * 2) + 8,
          child: GridView.builder(
            padding:
                EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
            scrollDirection: Axis.horizontal,
            itemCount: totalLength,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              childAspectRatio: 180.sp / itemWidth,
            ),
            itemBuilder: (context, index) {
              return DiscountedProductCard(
                product: productList![index],
                isLoading: false,
                sourceSection: sourceSection,
                imagePadding: EdgeInsets.all(0),
              );
            },
          ),
        ),
      ),
    );
  }
}
