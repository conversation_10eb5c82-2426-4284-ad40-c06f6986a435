import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/features/address/bloc/address_state.dart';

part 'address_event.freezed.dart';

@freezed
abstract class AddressEvent with _$AddressEvent {
  const factory AddressEvent.init() = _Init;
  const factory AddressEvent.started(
      {@Default(false) bool startMap,
      @Default(false) bool applyFilter}) = _Started;
  const factory AddressEvent.initMap() = _InitMap;
  const factory AddressEvent.selectFromMap({AddressModel? address}) =
      _SelectFromMap;
  const factory AddressEvent.initSearch() = _InitSearch;
  const factory AddressEvent.saveAddress() = _SaveAddress;
  const factory AddressEvent.deleteAddress(String addressId) = _DeleteAddress;
  const factory AddressEvent.fetchAddresses(
      {@Default(false) bool filterServiceStatus}) = _FetchAddresses;
  const factory AddressEvent.selectAddress(String addressId) = _SelectAddress;

  // New Events for Map UI
  const factory AddressEvent.mapCameraIdle({
    required double latitude,
    required double longitude,
  }) = _MapCameraIdle;
  const factory AddressEvent.searchPlaces(String query) = _SearchPlaces;
  const factory AddressEvent.selectPlace(String placeId) = _SelectPlace;
  const factory AddressEvent.getCurrentLocation() = _GetCurrentLocation;
  const factory AddressEvent.emitAsyncState(AddressState state) =
      _EmitAsyncState;
}
