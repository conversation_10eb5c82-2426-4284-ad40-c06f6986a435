import 'package:flutter/material.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import '../../../../widgets/app_card.dart';
import '../../../../widgets/custom_text.dart';

class ExpansionCard extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const ExpansionCard({
    super.key,
    required this.title,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.symmetric(horizontal: 16,),
      borderRadius: 16,
      padding: EdgeInsets.zero,
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          expansionTileTheme: const ExpansionTileThemeData(
            tilePadding: EdgeInsets.symmetric(horizontal: 16,),
            childrenPadding: EdgeInsets.only(left: 16, right: 16, bottom: 8),
          ),
        ),
        child: ExpansionTile(
          title: CustomText(
            title,
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColors.neutral700,
          ),
          children: [
            Divider(
              thickness: 0.5,
              color: AppColors.neutral150,
              height: 0.5,
              indent: 0,
              endIndent: 0,
            ),
            ...children
          ],
        ),
      ),
    );
  }
}