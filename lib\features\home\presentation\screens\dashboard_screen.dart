import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/color_utils.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/data/mappers/category_mapper.dart';
import 'package:rozana/data/models/category_model.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/widgets/scrollable_lotties_animation.dart';
import 'package:rozana/features/home/<USER>/widgets/animated_search_placeholder.dart';
import 'package:rozana/features/home/<USER>/widgets/header_widget.dart';
import 'package:rozana/features/location/presentation/widgets/location_state_handler.dart';
import 'package:rozana/features/products/presentation/widgets/product_section.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/services/app_preferences_service.dart';
import '../../../../core/services/remote_config_service.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/helpers.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_text.dart';
import '../../../../widgets/viewall_category_title.dart';
import '../../../categories/bloc/categories_bloc.dart';
import '../../../categories/presentation/widgets/category_skeleton_loader.dart';
import '../../../categories/presentation/widgets/categoy_card.dart';

import '../widgets/custom_search_appbar.dart';
import '../widgets/glossy_container.dart';
import '../widgets/ondc_list_products.dart';
import '../widgets/section_banner.dart';
import '../widgets/section_most_bought.dart';
import '../widgets/section_previously_bought.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _wasAuthenticated = false;
  // String? _lastAddressId;
  ScrollController categoryScrollController = ScrollController();

  List<dynamic> themeSettings = [
    {
      "color": "#FEE3AA",
      "light_color": "#FDC762",
      "icon": "assets/new/icons/loyalty.png",
      "background_image": "assets/new/lotties/Ganesh-Chaturthi-V09.json",
      "image_height": 440,
      "image_scaler": 20,
      "transform": -0.08,
      "scrollOffset": 240,
      "scroll": true,
      "topbar_primary_color": "#000000",
      "topbar_secondary_color": "#383838",
      "icon_primary_color": "#900A09",
      "icon_secondary_color": "#383838",
      "border_color": "#1A000000",
      'category_icon': "assets/new/icons/discounts.png",
      "top_padding": 0,
      "appbar_padding": 0,
      "search_top_padding": 0,
      "categories_top_padding": 0,
      "banner_config": [
        {
          'colors': [
            '#900A09',
            '#E06313',
            '#E06313',
            '#900A09',
          ],
          'stops': [0.0, 0.3, 0.6, 1.0],
          'begin': {'x': -1, 'y': 0.5},
          'end': {'x': 1, 'y': 0.5},
          'text_color': '#FFFFFF',
          'bg_color': '#FFFFFF',
          'width': 162,
          'height': 198,
          'radius': 32,
          'category': {
            'id': '973',
            'category_id': '973',
            'name': 'Home&Kitchen',
            'collectionId': '24',
          },
          'imageUrl': 'assets/new/images/for_kitchen.png',
          'title': 'For Kitchen',
          'border_colors': ['#900A09', '#00000000']
        },
        {
          'colors': [
            '#900A09',
            '#E06313',
            '#E06313',
            '#900A09',
          ],
          'stops': [0.0, 0.3, 0.6, 1.0],
          'begin': {'x': -1, 'y': 0.5},
          'end': {'x': 1, 'y': 0.5},
          'text_color': '#FFFFFF',
          'bg_color': '#FFFFFF',
          'width': 162,
          'height': 198,
          'radius': 32,
          'category': {
            'id': '46',
            'category_id': '46',
            'name': 'Grocery&Staple',
            'collectionId': '11',
          },
          'imageUrl': 'assets/new/images/for_pooja.png',
          'title': 'For Pooja',
          'border_colors': ['#900A09', '#00000000']
        },
        {
          'colors': [
            '#900A09',
            '#E06313',
            '#E06313',
            '#900A09',
          ],
          'stops': [0.0, 0.3, 0.6, 1.0],
          'begin': {'x': -1, 'y': 0.5},
          'end': {'x': 1, 'y': 0.5},
          "text_color": "#FFFFFF",
          "bg_color": "#FFFFFF",
          'width': 162,
          'height': 198,
          "radius": 32,
          "category": {
            "id": "524",
            "category_id": "524",
            "name": "Snacks&Bakery",
            "collectionId": "17"
          },
          "imageUrl": "assets/new/images/snacks_and_sweets.png",
          "title": "Snacks & Sweet",
          'border_colors': ['#900A09', '#00000000']
        }
      ]
    },
    {
      "color": "#F0D8F8",
      "light_color": "#F0D8F8",
      "icon": "assets/new/icons/apparel.png",
      "background_image": "",
      "image_height": 0,
      "image_scaler": 500,
      "topbar_primary_color": "#000000",
      "topbar_secondary_color": "#6D6D6D",
      "icon_primary_color": "#000000",
      "icon_secondary_color": "#6D6D6D",
      "border_color": "#494949",
    },
    {
      "color": "#167019",
      "light_color": "#93EA96",
      "icon": "assets/new/icons/local_cafe.png",
      "background_image": "",
      "image_height": 0,
      "image_scaler": 500,
      "topbar_primary_color": "#000000",
      "topbar_secondary_color": "#FFFFFF",
      "icon_primary_color": "#000000",
      "icon_secondary_color": "#FFFFFF",
      "border_color": "#494949",
    },
    {
      "color": "#871108",
      "light_color": "#EC867F",
      "icon": "assets/new/icons/add.png",
      "background_image": "",
      "image_height": 0,
      "image_scaler": 500,
      "topbar_primary_color": "#000000",
      "topbar_secondary_color": "#FFFFFF",
      "icon_primary_color": "#000000",
      "icon_secondary_color": "#FFFFFF",
      "border_color": "#494949",
    },
    {
      "color": "#FDC762",
      "light_color": "#FEE3AA",
      "icon": "assets/new/icons/add.png",
      "background_image": "",
      "image_height": 0,
      "image_scaler": 500,
      "topbar_primary_color": "#000000",
      "topbar_secondary_color": "#383838",
      "icon_primary_color": "#900A09",
      "icon_secondary_color": "#383838",
      "border_color": "#1A000000",
    }
  ];

  @override
  void initState() {
    super.initState();
    // Initialize authentication state
    themeSettings =
        ((RemoteConfigService().getThemeConfig['dashboard']?.isNotEmpty ??
                    false) &&
                (RemoteConfigService().getThemeConfig['dashboard'][0]
                        ?['banner_config']?[0]?['border_colors'] !=
                    null))
            ? RemoteConfigService().getThemeConfig['dashboard']
            : themeSettings;

    final appState = context.read<AppBloc>().state;

    _wasAuthenticated = appState.maybeMap(
      loaded: (loaded) => loaded.isAuthenticated,
      orElse: () => false,
    );
  }

  @override
  void dispose() {
    categoryScrollController.dispose();
    super.dispose();
  }

  static Color iconPrimaryColor = AppColors.neutral100;
  static Color iconSecondaryColor = AppColors.neutral300;
  static String myDealIcon = 'assets/new/icons/loyalty.png';

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to AppBloc changes (authentication)
        BlocListener<AppBloc, AppState>(
          listener: (context, state) {
            state.maybeMap(
              loaded: (loaded) {
                // Check if user just became authenticated
                if (loaded.isAuthenticated && !_wasAuthenticated) {
                  // User just logged in, reload location to show default address
                  context.read<AddressBloc>().add(AddressEvent.init());
                  getIt<HomeBloc>().add(const HomeEvent.loadHomeData());
                }
                _wasAuthenticated = loaded.isAuthenticated;
              },
              orElse: () {},
            );
          },
        ),
      ],
      child: Scaffold(
          backgroundColor: AppColors.neutral100,
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              BlocBuilder<AddressBloc, AddressState>(
                builder: (context, locationState) {
                  bool isSearviceable = locationState.isServicable;
                  return BlocBuilder<HomeBloc, HomeState>(
                    buildWhen: (previous, current) =>
                        (previous.scrollOffset != current.scrollOffset) ||
                        (previous.selectedIndex != current.selectedIndex),
                    builder: (context, state) {
                      int selectedIndex = state.selectedIndex;
                      if (selectedIndex > themeSettings.length - 1) {
                        selectedIndex = 0;
                      }
                      String imageUrl =
                          themeSettings[selectedIndex]['background_image'];

                      int imageHeight = int.tryParse(
                              (themeSettings[selectedIndex]['image_height']
                                      ?.toString()) ??
                                  '0') ??
                          0;

                      int imagePadding = int.tryParse(
                              (themeSettings[selectedIndex]['image_scaler']
                                      ?.toString()) ??
                                  '0') ??
                          0;

                      double topHeight = (double.tryParse(
                              themeSettings[selectedIndex]['top_padding']
                                  .toString()) ??
                          0);
                      double appBarHeight = (double.tryParse(
                              themeSettings[selectedIndex]['appbar_padding']
                                  .toString()) ??
                          0);
                      double searchHeight = (double.tryParse(
                              themeSettings[selectedIndex]['search_top_padding']
                                  .toString()) ??
                          0);
                      double categorieHeight = (double.tryParse(
                              themeSettings[selectedIndex]
                                      ['categories_top_padding']
                                  .toString()) ??
                          0);

                      double transformValue = (double.tryParse(
                              themeSettings[selectedIndex]['transform']
                                  .toString()) ??
                          0);

                      double scrollOffset = (double.tryParse(
                              themeSettings[selectedIndex]['scrollOffset']
                                  .toString()) ??
                          0);

                      bool scroll =
                          (themeSettings[selectedIndex]['scroll']) ?? false;

                      bool isScrolled = state.scrollOffset >
                          (imageHeight -
                              (topHeight +
                                  appBarHeight +
                                  searchHeight +
                                  categorieHeight +
                                  scrollOffset));

                      return Stack(
                        children: [
                          SizedBox(child: ColoredBox(color: AppColors.white)),
                          LayoutBuilder(builder: (context, constraints) {
                            return Stack(
                              children: [
                                Container(
                                  height:
                                      MediaQuery.of(context).size.height - 100,
                                  decoration: BoxDecoration(
                                    color: isScrolled
                                        ? ColorUtils.hexToColor(
                                            themeSettings[selectedIndex]
                                                ['color'])
                                        : null,
                                    gradient: !isScrolled
                                        ? RadialGradient(
                                            colors: [
                                              ColorUtils.hexToColor(
                                                      themeSettings[
                                                              selectedIndex]
                                                          ['light_color']) ??
                                                  AppColors.primary100,
                                              ColorUtils.hexToColor(
                                                      themeSettings[
                                                              selectedIndex]
                                                          ['color']) ??
                                                  AppColors.primary,
                                            ],
                                            center: Alignment.topCenter,
                                            radius: 1,
                                          )
                                        : null,
                                  ),
                                ),
                                Visibility(
                                  visible: (isSearviceable &&
                                      (themeSettings[selectedIndex]
                                                  ['background_image'] ??
                                              '')
                                          .toString()
                                          .isNotEmpty),
                                  child: LayoutBuilder(
                                    builder: (context, constraints) {
                                      return ScrollableLottieImage(
                                        imageUrl: imageUrl,
                                        imageHeight: imageHeight,
                                        imagePadding: imagePadding.toDouble(),
                                        scroll: scroll,
                                        transformValue: transformValue,
                                        constraints: constraints,
                                        isScrolled: isScrolled,
                                        scrollOffset: state.scrollOffset,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            );
                          }),
                        ],
                      );
                    },
                  );
                },
              ),
              LayoutBuilder(builder: (context, constraints) {
                return SafeArea(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      getIt<HomeBloc>().add(const HomeEvent.loadHomeData());
                    },
                    edgeOffset: 200,
                    child: CustomScrollView(
                      controller: HomeBloc.scrollController,
                      slivers: [
                        // Top app bar with categories
                        BlocBuilder<HomeBloc, HomeState>(
                          builder: (context, state) {
                            int selectedIndex = state.selectedIndex;
                            if (selectedIndex > themeSettings.length - 1) {
                              selectedIndex = 0;
                            }
                            return SliverToBoxAdapter(
                              child: SizedBox(
                                  height: (double.tryParse(
                                          themeSettings[selectedIndex]
                                                  ['top_padding']
                                              .toString()) ??
                                      0)),
                            );
                          },
                        ),
                        BlocBuilder<HomeBloc, HomeState>(
                          buildWhen: (previous, current) =>
                              previous.selectedIndex != current.selectedIndex,
                          builder: (context, state) {
                            int selectedIndex = state.selectedIndex;
                            if (selectedIndex > themeSettings.length - 1) {
                              selectedIndex = 0;
                            }
                            return SliverAppBar(
                              titleSpacing: 0,
                              collapsedHeight: 60 +
                                  (double.tryParse(themeSettings[selectedIndex]
                                              ['appbar_padding']
                                          .toString()) ??
                                      0),
                              toolbarHeight: 60 +
                                  (double.tryParse(themeSettings[selectedIndex]
                                              ['appbar_padding']
                                          .toString()) ??
                                      0),
                              expandedHeight: 60 +
                                  (double.tryParse(themeSettings[selectedIndex]
                                              ['appbar_padding']
                                          .toString()) ??
                                      0),
                              floating: false,
                              pinned: false,
                              stretch: false,
                              snap: false,
                              backgroundColor: Colors.transparent,
                              title: CustomSliverAppBarContent(
                                iconPrimaryColor: ColorUtils.hexToColor(
                                        themeSettings[selectedIndex]
                                                ['topbar_primary_color'] ??
                                            '') ??
                                    iconPrimaryColor,
                                iconSecondaryColor: ColorUtils.hexToColor(
                                        themeSettings[selectedIndex]
                                                ['topbar_secondary_color'] ??
                                            '') ??
                                    iconSecondaryColor,
                              ),
                            );
                          },
                        ),
                        BlocBuilder<HomeBloc, HomeState>(
                          builder: (context, state) {
                            int selectedIndex = state.selectedIndex;
                            if (selectedIndex > themeSettings.length - 1) {
                              selectedIndex = 0;
                            }
                            return SliverToBoxAdapter(
                              child: SizedBox(
                                  height: (double.tryParse(
                                          themeSettings[selectedIndex]
                                                  ['search_top_padding']
                                              .toString()) ??
                                      0)),
                            );
                          },
                        ),

                        BlocConsumer<AddressBloc, AddressState>(
                          buildWhen: (previous, current) {
                            return current.maybeMap(
                                loading: (value) => false, orElse: () => true);
                          },
                          listener: (context, state) {
                            state.maybeMap(
                                loading: (_) async {},
                                orElse: () {
                                  getIt<HomeBloc>()
                                      .add(const HomeEvent.loadHomeData());
                                  getIt<HomeBloc>().add(
                                      HomeEvent.switchCategory(
                                          CategoryEntity(
                                              id: 'My Deals',
                                              categoryId: 'My Deals',
                                              name: 'My Deals',
                                              collectionId: 'My Deals'),
                                          0));
                                });
                          },
                          builder: (context, state) {
                            if (!state.isServicable) {
                              return SliverToBoxAdapter(child: SizedBox());
                            } else {
                              return BlocBuilder<HomeBloc, HomeState>(
                                buildWhen: (previous, current) =>
                                    (previous.scrollOffset !=
                                        current.scrollOffset) ||
                                    (previous.selectedIndex !=
                                        current.selectedIndex),
                                builder: (context, state) {
                                  int selectedIndex = state.selectedIndex;
                                  if (selectedIndex >
                                      themeSettings.length - 1) {
                                    selectedIndex = 0;
                                  }
                                  bool isScrolled = selectedIndex == 0
                                      ? (state.scrollOffset >
                                          ((themeSettings[selectedIndex]
                                                      ['image_height'] ??
                                                  0) -
                                              200))
                                      : (state.scrollOffset >
                                          (themeSettings[selectedIndex]
                                                  ['image_height'] ??
                                              0));

                                  double topPadding = (double.tryParse(
                                          themeSettings[selectedIndex]
                                                  ['categories_top_padding']
                                              .toString()) ??
                                      0);

                                  return SliverPersistentHeader(
                                    pinned: true,
                                    delegate: SliverAppBarDelegate(
                                      minHeight: (constraints.maxWidth /
                                              (constraints.maxWidth * 0.0076)) +
                                          topPadding,
                                      maxHeight: (constraints.maxWidth /
                                              (constraints.maxWidth * 0.0076)) +
                                          topPadding,
                                      child: Container(
                                        decoration: BoxDecoration(
                                            color: isScrolled
                                                ? (ColorUtils.hexToColor(
                                                        themeSettings[
                                                                selectedIndex]
                                                            ['color']) ??
                                                    AppColors.primary)
                                                : Colors.transparent,
                                            border: Border(
                                              bottom: BorderSide(
                                                color: ColorUtils.hexToColor(
                                                        themeSettings[
                                                                    selectedIndex]
                                                                [
                                                                'border_color'] ??
                                                            '') ??
                                                    AppColors.neutral600,
                                              ),
                                            )),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // SEARCH BAR
                                            GestureDetector(
                                              onTap: () {
                                                context.push(
                                                    '${RouteNames.search}?initialQuery=');
                                              },
                                              child: Container(
                                                margin: EdgeInsets.symmetric(
                                                    vertical: 10,
                                                    horizontal: AppDimensions
                                                        .screenHzPadding),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 6,
                                                        vertical: 6),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  border: Border.all(
                                                      color:
                                                          AppColors.primary100,
                                                      width: 0.5),
                                                ),
                                                child: Row(
                                                  children: [
                                                    SizedBox.square(
                                                      dimension: 36,
                                                      child: Center(
                                                        child: Image.asset(
                                                          'assets/new/icons/search.png',
                                                          width: 24,
                                                          height: 24,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 10),
                                                    Expanded(
                                                      child: SizedBox(
                                                        height: 36,
                                                        child: Row(
                                                          children: [
                                                            CustomText(
                                                              'Search ',
                                                              color: AppColors
                                                                  .primary300,
                                                              fontSize: 16,
                                                              maxLines: 1,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              textHeight: 0.9,
                                                            ),
                                                            AnimatedSearchPlaceholder(),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: topPadding),
                                            Container(
                                              color: isScrolled
                                                  ? (ColorUtils.hexToColor(
                                                          themeSettings[
                                                                  selectedIndex]
                                                              ['color']) ??
                                                      AppColors.primary)
                                                  : Colors.transparent,
                                              child: BlocBuilder<HomeBloc,
                                                  HomeState>(
                                                buildWhen: (previous, current) {
                                                  List<CategoryEntity>
                                                      prevCategories =
                                                      previous.mapOrNull(
                                                              loaded: (value) =>
                                                                  value
                                                                      .categorySections) ??
                                                          [];

                                                  List<CategoryEntity>
                                                      currCategories =
                                                      current.mapOrNull(
                                                              loaded: (value) =>
                                                                  value
                                                                      .categorySections) ??
                                                          [];

                                                  return (prevCategories !=
                                                          currCategories) ||
                                                      (previous
                                                              .selectedCategory !=
                                                          current
                                                              .selectedCategory);
                                                },
                                                builder: (context, state) {
                                                  Map<String, dynamic>
                                                      userData =
                                                      jsonDecode((AppPreferences
                                                                      .getUserdata()
                                                                  ?.isNotEmpty ??
                                                              false)
                                                          ? AppPreferences
                                                              .getUserdata()!
                                                          : '{}');
                                                  bool isDistributor =
                                                      userData['userType'] ==
                                                          'distributor';
                                                  if (state is HomeInitial ||
                                                      (state is HomeLoaded &&
                                                          state.categorySections ==
                                                              null)) {
                                                    return SingleChildScrollView(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: AppDimensions
                                                              .screenHzPadding),
                                                      scrollDirection:
                                                          Axis.horizontal,
                                                      child: Row(
                                                        children: List.generate(
                                                          10,
                                                          (i) => SizedBox(
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.06,
                                                            width: 60,
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right:
                                                                          10),
                                                              child: Column(
                                                                children: [
                                                                  Expanded(
                                                                      child:
                                                                          Padding(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        horizontal:
                                                                            5),
                                                                    child:
                                                                        ShimmerBox(),
                                                                  )),
                                                                  SizedBox(
                                                                      height:
                                                                          4),
                                                                  ShimmerText(
                                                                      height:
                                                                          12)
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  }

                                                  List<CategoryEntity>?
                                                      fetchedCategories =
                                                      state.mapOrNull(
                                                          loaded: (value) => value
                                                              .categorySections);

                                                  // Create a mutable list
                                                  List<CategoryEntity>
                                                      categories = [
                                                    ...fetchedCategories ?? []
                                                  ];

                                                  // Add the static SAMU category
                                                  final samuhCategory =
                                                      CategoryEntity(
                                                    id: 'samuh_static',
                                                    categoryId: 'samuh_static',
                                                    name: 'SAMUH',
                                                    collectionId:
                                                        'samuh_static',
                                                  );
                                                  if (isDistributor) {
                                                    categories
                                                        .add(samuhCategory);
                                                  }

                                                  myDealIcon = themeSettings[
                                                              selectedIndex]
                                                          ?['category_icon'] ??
                                                      myDealIcon;

                                                  return Column(
                                                    children: [
                                                      SizedBox(
                                                          // height: 89.4,
                                                          child:
                                                              SingleChildScrollView(
                                                        scrollDirection:
                                                            Axis.horizontal,
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                horizontal:
                                                                    AppDimensions
                                                                        .screenHzPadding),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          children:
                                                              List.generate(
                                                                  categories
                                                                      .length,
                                                                  (index) {
                                                            CategoryEntity
                                                                category =
                                                                categories[
                                                                    index];
                                                            bool isSelected = (category
                                                                        .id ==
                                                                    state
                                                                        .selectedCategory
                                                                        .id) ||
                                                                (category
                                                                        .name ==
                                                                    state
                                                                        .selectedCategory
                                                                        .name);

                                                            bool
                                                                isSamuhCategory =
                                                                category.id ==
                                                                    'samuh_static';

                                                            String imagePath;
                                                            if (isSamuhCategory) {
                                                              imagePath =
                                                                  'assets/new/icons/add.png';
                                                            } else if (index ==
                                                                0) {
                                                              imagePath =
                                                                  myDealIcon;
                                                            } else {
                                                              imagePath = category
                                                                      .imageUrl ??
                                                                  ((index >=
                                                                          themeSettings
                                                                              .length)
                                                                      ? ''
                                                                      : themeSettings[
                                                                              index]
                                                                          [
                                                                          'icon']);
                                                            }

                                                            return Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right:
                                                                          20),
                                                              child:
                                                                  AppBarCategoryBox(
                                                                imagePath:
                                                                    imagePath,
                                                                isSelected:
                                                                    isSelected,
                                                                categoryName:
                                                                    TextFormatter
                                                                        .getFormattedCategoryText(
                                                                  category.name,
                                                                ),
                                                                iconPrimaryColor:
                                                                    (ColorUtils.hexToColor(themeSettings[selectedIndex]
                                                                            [
                                                                            'icon_primary_color']) ??
                                                                        iconPrimaryColor),
                                                                iconSecondaryColor:
                                                                    (ColorUtils.hexToColor(themeSettings[selectedIndex]
                                                                            [
                                                                            'icon_secondary_color']) ??
                                                                        iconSecondaryColor),
                                                                onTap: () {
                                                                  HapticFeedback
                                                                      .lightImpact();
                                                                  HomeBloc.scrollController?.animateTo(
                                                                      0.0,
                                                                      duration: Duration(
                                                                          milliseconds:
                                                                              200),
                                                                      curve: Curves
                                                                          .easeIn);
                                                                  getIt<HomeBloc>().add(
                                                                      HomeEvent.switchCategory(
                                                                          category,
                                                                          index));
                                                                  // }
                                                                },
                                                              ),
                                                            );
                                                          }),
                                                        ),
                                                      )),
                                                    ],
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            }
                          },
                        ),
                        SliverToBoxAdapter(
                          child: BlocBuilder<AddressBloc, AddressState>(
                            builder: (context, locationState) {
                              bool isSearviceable = locationState.isServicable;
                              return BlocBuilder<HomeBloc, HomeState>(
                                buildWhen: (previous, current) =>
                                    (previous.selectedIndex !=
                                        current.selectedIndex),
                                builder: (context, state) {
                                  int selectedIndex = state.selectedIndex;
                                  if (selectedIndex >
                                      themeSettings.length - 1) {
                                    selectedIndex = 0;
                                  }
                                  int imageHeight = int.tryParse(
                                          (themeSettings[selectedIndex]
                                                      ['image_height']
                                                  ?.toString()) ??
                                              '0') ??
                                      0;
                                  return Container(
                                    height: isSearviceable
                                        ? (imageHeight > 0
                                            ? imageHeight.toDouble()
                                            : 50)
                                        : 0,
                                    color: Colors.transparent,
                                    alignment: Alignment.bottomCenter,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Visibility(
                                          visible: selectedIndex == 0,
                                          child: SingleChildScrollView(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10),
                                            scrollDirection: Axis.horizontal,
                                            child: Row(
                                              children: ((themeSettings[
                                                              selectedIndex]
                                                          ['banner_config'] ??
                                                      []) as List)
                                                  .map((e) => Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                horizontal: 6),
                                                        child: TopCategoryBox(
                                                          bannerConfig: e,
                                                        ),
                                                      ))
                                                  .toList(),
                                            ),
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        CustomPaint(
                                          size: Size(
                                              MediaQuery.of(context).size.width,
                                              30.0),
                                          painter: CurvedTopPainter(
                                            color: selectedIndex == 0
                                                ? AppColors.neutral150
                                                : AppColors.white,
                                          ),
                                          child: SizedBox(
                                            width: MediaQuery.of(context)
                                                .size
                                                .width, // Ensure child matches CustomPaint size if needed
                                            height: 30.0,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),

                        // Main content list
                        SliverList(
                          delegate: SliverChildListDelegate([
                            BlocBuilder<HomeBloc, HomeState>(
                              buildWhen: (previous, current) =>
                                  (previous is! HomeError) ||
                                  (current is! HomeError),
                              builder: (context, state) {
                                return Container(
                                  color: AppColors.white,
                                  child: state.maybeMap(
                                    error: (value) => _buildErrorState(
                                        value.message, context),
                                    orElse: () => LocationStateHandler(
                                      builderHeight:
                                          MediaQuery.of(context).size.height -
                                              100,
                                      child: HomeBody(),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ]),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ],
          )),
    );
  }
}

class TopCategoryBox extends StatelessWidget {
  const TopCategoryBox({
    super.key,
    this.bannerConfig = const {
      'colors': [
        '#4D000000',
        '#111111',
      ],
      'stops': [-1.0, 1.0],
      'begin': {'x': 0, 'y': 0},
      'end': {'x': 1, 'y': 1},
      'text_color': '#FFFFFF',
      'bg_color': '#FFFFFF',
      'width': 170,
      'height': 220,
      'radius': 32,
      'category': {
        'id': '973',
        'category_id': '973',
        'name': 'Home&Kitchen',
        'collectionId': '24',
      },
      'imageUrl': 'assets/new/images/for_kitchen.png',
      'title': 'For Kitchen',
      'border_colors': ['#00000000', '#00000000']
    },
  });
  final Map<String, dynamic> bannerConfig;

  @override
  Widget build(BuildContext context) {
    final List<Color> colors = ((bannerConfig['colors'] as List?)
            ?.map((e) => ColorUtils.hexToColor(e))
            .whereType<Color>()
            .toList() ??
        [Color(0xFFFFFFFF), Color(0xFF000000)]);

    // Correctly get and default the stops list.
    final List<double> stops = ((bannerConfig['stops'] as List?)
            ?.map((e) => double.tryParse(e.toString()) ?? 0.0)
            .toList() ??
        [0.0, 1.0]);

    // Correctly get and default begin and end alignments.
    final AlignmentGeometry begin = bannerConfig['begin'] != null
        ? Alignment(
            double.tryParse(bannerConfig['begin']['x'].toString()) ?? 0,
            double.tryParse(bannerConfig['begin']['y'].toString()) ?? 0,
          )
        : Alignment.centerLeft;

    final AlignmentGeometry end = bannerConfig['end'] != null
        ? Alignment(
            double.tryParse(bannerConfig['end']['x'].toString()) ?? 0,
            double.tryParse(bannerConfig['end']['y'].toString()) ?? 0,
          )
        : Alignment.centerRight;
    final Color textColor =
        ColorUtils.hexToColor(bannerConfig['text_color'] ?? '') ??
            AppColors.neutral100;
    final Color bgColor =
        ColorUtils.hexToColor(bannerConfig['bg_color'] ?? '') ??
            AppColors.neutral100;

    final double width =
        double.tryParse(bannerConfig['width'].toString()) ?? 170;
    final double height =
        double.tryParse(bannerConfig['height'].toString()) ?? 220;
    final double radius =
        double.tryParse(bannerConfig['radius'].toString()) ?? 32;

    final CategoryEntity entity = CategoryMapper.toEntity(
        CategoryModel.fromJson(bannerConfig['category']));
    final image = bannerConfig['imageUrl'] ?? '';
    final title = bannerConfig['title'] ?? '';

    final List<Color> borderColors = ((bannerConfig['border_colors'] as List?)
            ?.map((e) => ColorUtils.hexToColor(e))
            .whereType<Color>()
            .toList() ??
        [Colors.transparent, Colors.transparent]);

    return GestureDetector(
      onTap: () {
        CategoriesBloc.loadedSubcategories.clear();
        context.push(RouteNames.categories, extra: {
          'category': entity,
        });
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                width: width,
                height: height,
                padding: EdgeInsets.only(top: 40, bottom: 48),
                decoration: BoxDecoration(
                  color: bgColor,
                  borderRadius: BorderRadius.circular(radius),
                ),
                child: CustomImage(imageUrl: image),
              ),
              Container(
                width: width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(radius),
                      bottomRight: Radius.circular(radius)),
                  gradient: LinearGradient(
                    colors: colors,
                    stops: stops,
                    begin: begin,
                    end: end,
                  ),
                ),
                alignment: Alignment.center,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  child: CustomText(
                    title,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: textColor,
                  ),
                ),
              )
            ],
          ),
          SizedBox(
            width: width,
            height: height,
            child: CustomPaint(
              painter: GradientBorderPainter(
                borderWidth: 3,
                borderRadius: BorderRadius.circular(radius),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: borderColors,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HomeBody extends StatelessWidget {
  const HomeBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) =>
          previous.selectedCategory != current.selectedCategory,
      builder: (context, state) {
        CategoryEntity? category = state.selectedCategory;
        return Column(
          children: [
            (category.name.toLowerCase() == 'my deals')
                ? HomeAllData()
                : (category.name.toLowerCase() == 'samuh')
                    ? ONDCProductSection()
                    : HomeCategoriesSection(
                        preloadData: true,
                        showTitleBar: false,
                        parentCategory: category,
                        level: 'sub_category',
                      ),
          ],
        );
      },
    );
  }
}

class HomeAllData extends StatelessWidget {
  const HomeAllData({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //Previously Bought

        BlocBuilder<AppBloc, AppState>(
          builder: (context, state) {
            return state.maybeMap(
              orElse: () => SizedBox(),
              loaded: (value) {
                if (value.isAuthenticated) {
                  return PreviouslyBoughtSection();
                } else {
                  return SizedBox();
                }
              },
            );
          },
        ),
        SizedBox(height: 20),
        BannerWidget(),
        // Most Bought
        MostBoughtSection(),
        SizedBox(height: 150),
      ],
    );
  }
}

Widget _buildErrorState(String message, BuildContext context) {
  return SizedBox(
    height: MediaQuery.of(context).size.height - 200,
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 50),
          const SizedBox(height: 10),
          Text(message, textAlign: TextAlign.center),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              getIt<HomeBloc>().add(const HomeEvent.loadHomeData());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    ),
  );
}

class CurvedTopPainter extends CustomPainter {
  final Color color;

  CurvedTopPainter({this.color = Colors.green});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill; // Fill the path

    final path = Path();

    // Start from bottom left
    path.moveTo(0, size.height);
    // Draw line to top left
    path.lineTo(0, size.height * 0.8); // Start curve a bit down from top left
    path.cubicTo(
      size.width * 0.25,
      0, // Control point 1 (influences the left side of the curve)
      size.width * 0.75,
      0, // Control point 2 (influences the right side of the curve)
      size.width, size.height * 0.8, // End point of the curve
    );

    // Draw line to bottom right
    path.lineTo(size.width, size.height);
    // Close the path to form a shape
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // Only repaint if properties change
  }
}

class BannerWidget extends StatelessWidget {
  const BannerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddressBloc, AddressState>(
      builder: (context, state) {
        bool isServicable = state.isServicable;
        return isServicable
            ? BlocBuilder<HomeBloc, HomeState>(
                buildWhen: (previous, current) {
                  if (previous is! HomeLoaded) {
                    return true;
                  }
                  if (current is HomeLoaded) {
                    return previous.banners != current.banners;
                  }
                  return false; // Don’t rebuild for other transitions
                },
                builder: (context, state) {
                  CategoryEntity? selectedCategory = state.selectedCategory;
                  List<BannerEntity>? banners =
                      state.mapOrNull(loaded: (value) => value.banners);
                  return (banners?.isNotEmpty ?? false) &&
                          ((selectedCategory.isAvailable ?? false) ||
                              selectedCategory.name == 'My Deals')
                      ? Padding(
                          padding: const EdgeInsets.only(bottom: 30),
                          child: BannerSection(
                            banners: banners,
                            topPadding: 10,
                            height: 200,
                            showIndicator: false,
                            parentCategory: selectedCategory,
                          ),
                        )
                      : SizedBox();
                },
              )
            : SizedBox();
      },
    );
  }
}

class HomeCategoriesSection extends StatefulWidget {
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  const HomeCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
  });

  @override
  State<HomeCategoriesSection> createState() => _HomeCategoriesSectionState();
}

class _HomeCategoriesSectionState extends State<HomeCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  @override
  void didUpdateWidget(covariant HomeCategoriesSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the parentCategory has changed
    if (widget.parentCategory != oldWidget.parentCategory ||
        widget.level != oldWidget.level) {
      // Also check if level changed if it impacts data loading
      LogMessage.l(
          "Parent category changed from ${oldWidget.parentCategory?.name} to ${widget.parentCategory?.name}");
      _loadCategories(); // Reload categories when parentCategory changes
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(pageSize: 20)
          : await _dataManager.loadSubCategories(
              category: widget.parentCategory,
              pageSize: 20,
              level: widget.level);

      setState(() {
        _subCategories.clear();
        _subCategories.addAll(categories);
        _isLoading = false;
      });
      if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onSubcategorySelected!(_subCategories.first);
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: (widget.parentCategory?.isAvailable ?? false) || _isLoading
          ? _isLoading
              ? SizedBox(
                  height: MediaQuery.of(context).size.height,
                )
              : ConstrainedBox(
                  constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height - 300),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HomeCategorySectionWidget(
                        parentCategory: widget.parentCategory,
                        subCategories: _subCategories,
                      ),
                      BannerWidget(),
                      SizedBox(height: 30),
                      if (widget.parentCategory != null) ...[
                        HeaderWidget(title: "Top Products"),
                        const SizedBox(height: 12),
                        BlocConsumer<HomeBloc, HomeState>(
                          listener: (context, state) {
                            if ((state.scrollOffset) >=
                                ((HomeBloc.scrollController?.position
                                            .maxScrollExtent ??
                                        0) -
                                    200)) {
                              productSectionKey.currentState
                                  ?.loadMoreProducts();
                            }
                          },
                          buildWhen: (previous, current) => false,
                          builder: (context, state) {
                            return ProductsSection(
                              key: productSectionKey,
                              title:
                                  widget.parentCategory?.name ?? 'All Products',
                              showSeeAll: false,
                              category: widget.parentCategory,
                              useGridView: true,
                              shrinkWrap: true,
                              primary: true,
                              physics: NeverScrollableScrollPhysics(),
                              bottomPadding: 100,
                              useCollectionId: true,
                              cardWidth: MediaQuery.of(context).size.width / 2,
                              gridChildAspectRatio: 0.004,
                              onSeeAllTap: () async {},
                            );
                          },
                        ),
                      ]
                    ],
                  ),
                )
          : Padding(
              padding: const EdgeInsets.only(top: 100),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Image.asset('assets/images/curve_background.png'),
                  ),
                  SizedBox(
                    height: 150,
                    width: MediaQuery.of(context).size.width,
                    child: Image.asset('assets/images/coming-soon.png'),
                  ),
                ],
              ),
            ),
    );
  }
}

class HomeSubCategoriesSection extends StatefulWidget {
  final CategoryEntity? mainCategory;
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  const HomeSubCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
    this.mainCategory,
  });

  @override
  State<HomeSubCategoriesSection> createState() =>
      _HomeSubCategoriesSectionState();
}

class _HomeSubCategoriesSectionState extends State<HomeSubCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categories = widget.parentCategory == null
          ? await _dataManager.loadCategories(pageSize: 20)
          : await _dataManager.loadSubCategories(
              category: widget.parentCategory,
              pageSize: 20,
              level: widget.level);

      setState(() {
        _subCategories.clear();
        _subCategories.addAll(categories);
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitleBar)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 8),
            child: ViewAllCategoryTitle(
              title: widget.parentCategory?.name ?? '',
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap?.call();
              },
              showViewAll: false,
            ),
          ),
        const SizedBox(height: 8),
        _isLoading
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: CategorySkeletonLoader(
                    useGridView: widget.useGridView,
                    showAsRow: widget.showAsRow,
                    gridCrossAxisCount: widget.gridCrossAxisCount,
                    gridChildAspectRatio: 1,
                    itemHeight: 70,
                  ),
                ),
              )
            : GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.screenHzPadding),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: widget.gridCrossAxisCount,
                  childAspectRatio: widget.gridChildAspectRatio,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 12,
                ),
                itemCount: _subCategories.length,
                itemBuilder: (context, index) {
                  CategoryEntity subCategory = _subCategories[index];
                  return CategoryCard(
                    onTap: () async {
                      HapticFeedback.lightImpact();

                      Map<String, dynamic> extras = {};
                      if (widget.parentCategory != null) {
                        extras['category'] = widget.parentCategory;
                        extras['sub_category'] = subCategory;
                        extras['parent-category'] = widget.mainCategory;
                      }
                      context.push(RouteNames.products, extra: extras);
                    },
                    category: widget.parentCategory,
                    subCategory: subCategory,
                    radius: 10,
                    fontSize: 10,
                  );
                },
              ),
      ],
    );
  }
}

class HomeCategorySectionWidget extends StatelessWidget {
  const HomeCategorySectionWidget({
    super.key,
    this.parentCategory,
    this.subCategories = const [],
  });
  final CategoryEntity? parentCategory;
  final List<CategoryEntity> subCategories;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display parent category name as title (like CategoriesSection)
                Padding(
                  padding:
                      const EdgeInsets.only(left: 16, right: 8, bottom: 15),
                  child: HeaderWidget(title: parentCategory?.name ?? ''),
                ),
                // Display subcategories in grid format (like CategoriesSection)
                GridView.builder(
                  shrinkWrap: true,
                  primary: false,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 10,
                    childAspectRatio: 0.65,
                  ),
                  itemCount: subCategories.length,
                  itemBuilder: (ctx, index) {
                    CategoryEntity subCategory = subCategories[index];
                    return CategoryCard(
                      radius: 12,
                      fontSize: 14,
                      textColor: AppColors.neutral500,
                      subCategory: subCategory,
                      category: CategoryEntity(
                          id: '',
                          categoryId: subCategory.categoryId,
                          name: '',
                          collectionId: subCategory.parentID ?? ''),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Custom SliverPersistentHeaderDelegate
// This is boilerplate code needed for SliverPersistentHeader
class SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

class GradientBorderPainter extends CustomPainter {
  final double borderWidth;
  final BorderRadius borderRadius;
  final Gradient gradient;

  GradientBorderPainter({
    required this.borderWidth,
    required this.borderRadius,
    required this.gradient,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 1. Create the rounded rectangle for the gradient border
    final Rect outerRect = Offset.zero & size;
    final RRect outerRRect = borderRadius.toRRect(outerRect);

    // 2. Create the inner rounded rectangle for the content area
    final RRect innerRRect = outerRRect.deflate(borderWidth);

    // 3. Create the border path by subtracting the inner rect from the outer rect
    final Path borderPath = Path.combine(
      PathOperation.difference,
      Path()..addRRect(outerRRect),
      Path()..addRRect(innerRRect),
    );

    // 4. Create a Paint object with the gradient shader
    final Paint paint = Paint()
      ..shader = gradient.createShader(outerRect)
      ..style = PaintingStyle.fill;

    // 5. Draw the border path on the canvas
    canvas.drawPath(borderPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
