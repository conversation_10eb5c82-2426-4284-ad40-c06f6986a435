import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';


class AuthenticationAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track mobile number screen viewed
  Future<void> trackMobileNumberScreenViewed() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logMobileNumberScreenViewed(
          analyticsData['mobileNumber'],
        );
      }
    } catch (e) {
      debugPrint('Error tracking mobile number screen viewed: $e');
    }
  }

  /// Track mobile number continue button clicked
  Future<void> trackMobileNumberContinueClicked() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logMobileNumberContinueClicked(
          analyticsData['mobileNumber'],
        );
      }
    } catch (e) {
      debugPrint('Error tracking mobile number continue clicked: $e');
    }
  }

  /// Track OTP verification screen viewed
  Future<void> trackOTPVerificationScreenViewed() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOTPVerificationScreenViewed(
          analyticsData['mobileNumber'],
        );
      }
    } catch (e) {
      debugPrint('Error tracking OTP verification screen viewed: $e');
    }
  }

  /// Track OTP resend requested
  Future<void> trackOTPResendRequested() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOTPResendRequested(
          analyticsData['mobileNumber'],
        );
      }
    } catch (e) {
      debugPrint('Error tracking OTP resend requested: $e');
    }
  }

  /// Track OTP success
  Future<void> trackOTPSuccess() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOTPSuccess(
          mobileNumber: analyticsData['mobileNumber'],
          authMethod: 'phone',
          signupFlowDuration: 0,
          userType: 'new',
        );
      }
    } catch (e) {
      debugPrint('Error tracking OTP success: $e');
    }
  }

  /// Track OTP failure
  Future<void> trackOTPFailure() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOTPFailureIncorrect(
          mobileNumber: analyticsData['mobileNumber'],
          attemptNumber: 1,
          errorType: 'otp_failure',
          retryAttempted: false,
        );
      }
    } catch (e) {
      debugPrint('Error tracking OTP failure: $e');
    }
  }

  /// Track OTP incorrect
  Future<void> trackOTPIncorrect() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logOTPIncorrect(
          mobileNumber: analyticsData['mobileNumber'],
          authMethod: 'phone',
          signupFlowDuration: 0,
          userType: 'new',
        );
      }
    } catch (e) {
      debugPrint('Error tracking OTP incorrect: $e');
    }
  }


}