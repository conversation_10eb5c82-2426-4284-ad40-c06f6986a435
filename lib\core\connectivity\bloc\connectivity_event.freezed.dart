// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'connectivity_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ConnectivityEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ConnectivityEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ConnectivityEvent()';
  }
}

/// @nodoc
class $ConnectivityEventCopyWith<$Res> {
  $ConnectivityEventCopyWith(
      ConnectivityEvent _, $Res Function(ConnectivityEvent) __);
}

/// Adds pattern-matching-related methods to [ConnectivityEvent].
extension ConnectivityEventPatterns on ConnectivityEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckConnectivity value)? checkConnectivity,
    TResult Function(_ConnectivityChanged value)? connectivityChanged,
    TResult Function(_RetryConnection value)? retryConnection,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CheckConnectivity() when checkConnectivity != null:
        return checkConnectivity(_that);
      case _ConnectivityChanged() when connectivityChanged != null:
        return connectivityChanged(_that);
      case _RetryConnection() when retryConnection != null:
        return retryConnection(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckConnectivity value) checkConnectivity,
    required TResult Function(_ConnectivityChanged value) connectivityChanged,
    required TResult Function(_RetryConnection value) retryConnection,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckConnectivity():
        return checkConnectivity(_that);
      case _ConnectivityChanged():
        return connectivityChanged(_that);
      case _RetryConnection():
        return retryConnection(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckConnectivity value)? checkConnectivity,
    TResult? Function(_ConnectivityChanged value)? connectivityChanged,
    TResult? Function(_RetryConnection value)? retryConnection,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckConnectivity() when checkConnectivity != null:
        return checkConnectivity(_that);
      case _ConnectivityChanged() when connectivityChanged != null:
        return connectivityChanged(_that);
      case _RetryConnection() when retryConnection != null:
        return retryConnection(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkConnectivity,
    TResult Function(ConnectivityResult result)? connectivityChanged,
    TResult Function()? retryConnection,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CheckConnectivity() when checkConnectivity != null:
        return checkConnectivity();
      case _ConnectivityChanged() when connectivityChanged != null:
        return connectivityChanged(_that.result);
      case _RetryConnection() when retryConnection != null:
        return retryConnection();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkConnectivity,
    required TResult Function(ConnectivityResult result) connectivityChanged,
    required TResult Function() retryConnection,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckConnectivity():
        return checkConnectivity();
      case _ConnectivityChanged():
        return connectivityChanged(_that.result);
      case _RetryConnection():
        return retryConnection();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkConnectivity,
    TResult? Function(ConnectivityResult result)? connectivityChanged,
    TResult? Function()? retryConnection,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckConnectivity() when checkConnectivity != null:
        return checkConnectivity();
      case _ConnectivityChanged() when connectivityChanged != null:
        return connectivityChanged(_that.result);
      case _RetryConnection() when retryConnection != null:
        return retryConnection();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _CheckConnectivity implements ConnectivityEvent {
  const _CheckConnectivity();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CheckConnectivity);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ConnectivityEvent.checkConnectivity()';
  }
}

/// @nodoc

class _ConnectivityChanged implements ConnectivityEvent {
  const _ConnectivityChanged(this.result);

  final ConnectivityResult result;

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConnectivityChangedCopyWith<_ConnectivityChanged> get copyWith =>
      __$ConnectivityChangedCopyWithImpl<_ConnectivityChanged>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ConnectivityChanged &&
            (identical(other.result, result) || other.result == result));
  }

  @override
  int get hashCode => Object.hash(runtimeType, result);

  @override
  String toString() {
    return 'ConnectivityEvent.connectivityChanged(result: $result)';
  }
}

/// @nodoc
abstract mixin class _$ConnectivityChangedCopyWith<$Res>
    implements $ConnectivityEventCopyWith<$Res> {
  factory _$ConnectivityChangedCopyWith(_ConnectivityChanged value,
          $Res Function(_ConnectivityChanged) _then) =
      __$ConnectivityChangedCopyWithImpl;
  @useResult
  $Res call({ConnectivityResult result});
}

/// @nodoc
class __$ConnectivityChangedCopyWithImpl<$Res>
    implements _$ConnectivityChangedCopyWith<$Res> {
  __$ConnectivityChangedCopyWithImpl(this._self, this._then);

  final _ConnectivityChanged _self;
  final $Res Function(_ConnectivityChanged) _then;

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? result = null,
  }) {
    return _then(_ConnectivityChanged(
      null == result
          ? _self.result
          : result // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
    ));
  }
}

/// @nodoc

class _RetryConnection implements ConnectivityEvent {
  const _RetryConnection();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _RetryConnection);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ConnectivityEvent.retryConnection()';
  }
}

// dart format on
