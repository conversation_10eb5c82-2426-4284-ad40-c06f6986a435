// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'free_product_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FreeProductEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is FreeProductEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'FreeProductEvent()';
  }
}

/// @nodoc
class $FreeProductEventCopyWith<$Res> {
  $FreeProductEventCopyWith(
      FreeProductEvent _, $Res Function(FreeProductEvent) __);
}

/// Adds pattern-matching-related methods to [FreeProductEvent].
extension FreeProductEventPatterns on FreeProductEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckOfferEvent value)? checkOffer,
    TResult Function(GetProductsEvent value)? getProducts,
    TResult Function(AddFreeProductEvent value)? addFreeProduct,
    TResult Function(RemoveFreeProductEvent value)? removeFreeProduct,
    TResult Function(ClearFreeProductsEvent value)? clearFreeProducts,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CheckOfferEvent() when checkOffer != null:
        return checkOffer(_that);
      case GetProductsEvent() when getProducts != null:
        return getProducts(_that);
      case AddFreeProductEvent() when addFreeProduct != null:
        return addFreeProduct(_that);
      case RemoveFreeProductEvent() when removeFreeProduct != null:
        return removeFreeProduct(_that);
      case ClearFreeProductsEvent() when clearFreeProducts != null:
        return clearFreeProducts(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckOfferEvent value) checkOffer,
    required TResult Function(GetProductsEvent value) getProducts,
    required TResult Function(AddFreeProductEvent value) addFreeProduct,
    required TResult Function(RemoveFreeProductEvent value) removeFreeProduct,
    required TResult Function(ClearFreeProductsEvent value) clearFreeProducts,
  }) {
    final _that = this;
    switch (_that) {
      case CheckOfferEvent():
        return checkOffer(_that);
      case GetProductsEvent():
        return getProducts(_that);
      case AddFreeProductEvent():
        return addFreeProduct(_that);
      case RemoveFreeProductEvent():
        return removeFreeProduct(_that);
      case ClearFreeProductsEvent():
        return clearFreeProducts(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckOfferEvent value)? checkOffer,
    TResult? Function(GetProductsEvent value)? getProducts,
    TResult? Function(AddFreeProductEvent value)? addFreeProduct,
    TResult? Function(RemoveFreeProductEvent value)? removeFreeProduct,
    TResult? Function(ClearFreeProductsEvent value)? clearFreeProducts,
  }) {
    final _that = this;
    switch (_that) {
      case CheckOfferEvent() when checkOffer != null:
        return checkOffer(_that);
      case GetProductsEvent() when getProducts != null:
        return getProducts(_that);
      case AddFreeProductEvent() when addFreeProduct != null:
        return addFreeProduct(_that);
      case RemoveFreeProductEvent() when removeFreeProduct != null:
        return removeFreeProduct(_that);
      case ClearFreeProductsEvent() when clearFreeProducts != null:
        return clearFreeProducts(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(double cartAmount)? checkOffer,
    TResult Function(String productId, String variantId, String sku)?
        getProducts,
    TResult Function(ProductEntity product)? addFreeProduct,
    TResult Function(String productId, String skuId)? removeFreeProduct,
    TResult Function()? clearFreeProducts,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CheckOfferEvent() when checkOffer != null:
        return checkOffer(_that.cartAmount);
      case GetProductsEvent() when getProducts != null:
        return getProducts(_that.productId, _that.variantId, _that.sku);
      case AddFreeProductEvent() when addFreeProduct != null:
        return addFreeProduct(_that.product);
      case RemoveFreeProductEvent() when removeFreeProduct != null:
        return removeFreeProduct(_that.productId, _that.skuId);
      case ClearFreeProductsEvent() when clearFreeProducts != null:
        return clearFreeProducts();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(double cartAmount) checkOffer,
    required TResult Function(String productId, String variantId, String sku)
        getProducts,
    required TResult Function(ProductEntity product) addFreeProduct,
    required TResult Function(String productId, String skuId) removeFreeProduct,
    required TResult Function() clearFreeProducts,
  }) {
    final _that = this;
    switch (_that) {
      case CheckOfferEvent():
        return checkOffer(_that.cartAmount);
      case GetProductsEvent():
        return getProducts(_that.productId, _that.variantId, _that.sku);
      case AddFreeProductEvent():
        return addFreeProduct(_that.product);
      case RemoveFreeProductEvent():
        return removeFreeProduct(_that.productId, _that.skuId);
      case ClearFreeProductsEvent():
        return clearFreeProducts();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(double cartAmount)? checkOffer,
    TResult? Function(String productId, String variantId, String sku)?
        getProducts,
    TResult? Function(ProductEntity product)? addFreeProduct,
    TResult? Function(String productId, String skuId)? removeFreeProduct,
    TResult? Function()? clearFreeProducts,
  }) {
    final _that = this;
    switch (_that) {
      case CheckOfferEvent() when checkOffer != null:
        return checkOffer(_that.cartAmount);
      case GetProductsEvent() when getProducts != null:
        return getProducts(_that.productId, _that.variantId, _that.sku);
      case AddFreeProductEvent() when addFreeProduct != null:
        return addFreeProduct(_that.product);
      case RemoveFreeProductEvent() when removeFreeProduct != null:
        return removeFreeProduct(_that.productId, _that.skuId);
      case ClearFreeProductsEvent() when clearFreeProducts != null:
        return clearFreeProducts();
      case _:
        return null;
    }
  }
}

/// @nodoc

class CheckOfferEvent implements FreeProductEvent {
  const CheckOfferEvent({required this.cartAmount});

  final double cartAmount;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CheckOfferEventCopyWith<CheckOfferEvent> get copyWith =>
      _$CheckOfferEventCopyWithImpl<CheckOfferEvent>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CheckOfferEvent &&
            (identical(other.cartAmount, cartAmount) ||
                other.cartAmount == cartAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cartAmount);

  @override
  String toString() {
    return 'FreeProductEvent.checkOffer(cartAmount: $cartAmount)';
  }
}

/// @nodoc
abstract mixin class $CheckOfferEventCopyWith<$Res>
    implements $FreeProductEventCopyWith<$Res> {
  factory $CheckOfferEventCopyWith(
          CheckOfferEvent value, $Res Function(CheckOfferEvent) _then) =
      _$CheckOfferEventCopyWithImpl;
  @useResult
  $Res call({double cartAmount});
}

/// @nodoc
class _$CheckOfferEventCopyWithImpl<$Res>
    implements $CheckOfferEventCopyWith<$Res> {
  _$CheckOfferEventCopyWithImpl(this._self, this._then);

  final CheckOfferEvent _self;
  final $Res Function(CheckOfferEvent) _then;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? cartAmount = null,
  }) {
    return _then(CheckOfferEvent(
      cartAmount: null == cartAmount
          ? _self.cartAmount
          : cartAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class GetProductsEvent implements FreeProductEvent {
  const GetProductsEvent(
      {required this.productId, required this.variantId, required this.sku});

  final String productId;
  final String variantId;
  final String sku;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GetProductsEventCopyWith<GetProductsEvent> get copyWith =>
      _$GetProductsEventCopyWithImpl<GetProductsEvent>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetProductsEvent &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.variantId, variantId) ||
                other.variantId == variantId) &&
            (identical(other.sku, sku) || other.sku == sku));
  }

  @override
  int get hashCode => Object.hash(runtimeType, productId, variantId, sku);

  @override
  String toString() {
    return 'FreeProductEvent.getProducts(productId: $productId, variantId: $variantId, sku: $sku)';
  }
}

/// @nodoc
abstract mixin class $GetProductsEventCopyWith<$Res>
    implements $FreeProductEventCopyWith<$Res> {
  factory $GetProductsEventCopyWith(
          GetProductsEvent value, $Res Function(GetProductsEvent) _then) =
      _$GetProductsEventCopyWithImpl;
  @useResult
  $Res call({String productId, String variantId, String sku});
}

/// @nodoc
class _$GetProductsEventCopyWithImpl<$Res>
    implements $GetProductsEventCopyWith<$Res> {
  _$GetProductsEventCopyWithImpl(this._self, this._then);

  final GetProductsEvent _self;
  final $Res Function(GetProductsEvent) _then;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? productId = null,
    Object? variantId = null,
    Object? sku = null,
  }) {
    return _then(GetProductsEvent(
      productId: null == productId
          ? _self.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      variantId: null == variantId
          ? _self.variantId
          : variantId // ignore: cast_nullable_to_non_nullable
              as String,
      sku: null == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class AddFreeProductEvent implements FreeProductEvent {
  const AddFreeProductEvent({required this.product});

  final ProductEntity product;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AddFreeProductEventCopyWith<AddFreeProductEvent> get copyWith =>
      _$AddFreeProductEventCopyWithImpl<AddFreeProductEvent>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AddFreeProductEvent &&
            (identical(other.product, product) || other.product == product));
  }

  @override
  int get hashCode => Object.hash(runtimeType, product);

  @override
  String toString() {
    return 'FreeProductEvent.addFreeProduct(product: $product)';
  }
}

/// @nodoc
abstract mixin class $AddFreeProductEventCopyWith<$Res>
    implements $FreeProductEventCopyWith<$Res> {
  factory $AddFreeProductEventCopyWith(
          AddFreeProductEvent value, $Res Function(AddFreeProductEvent) _then) =
      _$AddFreeProductEventCopyWithImpl;
  @useResult
  $Res call({ProductEntity product});
}

/// @nodoc
class _$AddFreeProductEventCopyWithImpl<$Res>
    implements $AddFreeProductEventCopyWith<$Res> {
  _$AddFreeProductEventCopyWithImpl(this._self, this._then);

  final AddFreeProductEvent _self;
  final $Res Function(AddFreeProductEvent) _then;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? product = null,
  }) {
    return _then(AddFreeProductEvent(
      product: null == product
          ? _self.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductEntity,
    ));
  }
}

/// @nodoc

class RemoveFreeProductEvent implements FreeProductEvent {
  const RemoveFreeProductEvent({required this.productId, required this.skuId});

  final String productId;
  final String skuId;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RemoveFreeProductEventCopyWith<RemoveFreeProductEvent> get copyWith =>
      _$RemoveFreeProductEventCopyWithImpl<RemoveFreeProductEvent>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RemoveFreeProductEvent &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.skuId, skuId) || other.skuId == skuId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, productId, skuId);

  @override
  String toString() {
    return 'FreeProductEvent.removeFreeProduct(productId: $productId, skuId: $skuId)';
  }
}

/// @nodoc
abstract mixin class $RemoveFreeProductEventCopyWith<$Res>
    implements $FreeProductEventCopyWith<$Res> {
  factory $RemoveFreeProductEventCopyWith(RemoveFreeProductEvent value,
          $Res Function(RemoveFreeProductEvent) _then) =
      _$RemoveFreeProductEventCopyWithImpl;
  @useResult
  $Res call({String productId, String skuId});
}

/// @nodoc
class _$RemoveFreeProductEventCopyWithImpl<$Res>
    implements $RemoveFreeProductEventCopyWith<$Res> {
  _$RemoveFreeProductEventCopyWithImpl(this._self, this._then);

  final RemoveFreeProductEvent _self;
  final $Res Function(RemoveFreeProductEvent) _then;

  /// Create a copy of FreeProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? productId = null,
    Object? skuId = null,
  }) {
    return _then(RemoveFreeProductEvent(
      productId: null == productId
          ? _self.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      skuId: null == skuId
          ? _self.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class ClearFreeProductsEvent implements FreeProductEvent {
  const ClearFreeProductsEvent();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ClearFreeProductsEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'FreeProductEvent.clearFreeProducts()';
  }
}

// dart format on
