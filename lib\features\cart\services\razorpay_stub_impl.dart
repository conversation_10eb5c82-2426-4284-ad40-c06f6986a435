// Stub implementation for non-web platforms
import 'package:razorpay_flutter/razorpay_flutter.dart';

/// Stub implementation of Razorpay web checkout for non-web platforms
class RazorpayWebImpl {
  final Function(PaymentSuccessResponse) onPaymentSuccess;
  final Function(PaymentFailureResponse) onPaymentError;

  RazorpayWebImpl({
    required this.onPaymentSuccess,
    required this.onPaymentError,
  });

  /// This method does nothing on non-web platforms
  void processPayment(Map<String, dynamic> options) {
    // This should never be called on non-web platforms
    onPaymentError(PaymentFailureResponse(
      0,
      'Razorpay web implementation not available on this platform',
      null,
    ));
  }
}
