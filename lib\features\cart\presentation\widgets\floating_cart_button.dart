import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/presentation/screens/cart_screen.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';


class FloatingCartButton extends StatefulWidget {
  const FloatingCartButton({super.key});

  @override
  State<FloatingCartButton> createState() => _FloatingCartButtonState();
}

class _FloatingCartButtonState extends State<FloatingCartButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      buildWhen: (previous, current) =>
          previous.cart.totalItems != current.cart.totalItems ||
          previous.cart.total != current.cart.total,
      builder: (context, state) {
        // Don't show the button if cart is empty
        if (state.cart.totalItems == 0) {
          return const SizedBox.shrink();
        }

        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            );
          },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Material(
            elevation: 8,
            shadowColor: AppColors.primary100,
            borderRadius: BorderRadius.circular(50),
            child: InkWell(
              onTap: () {
                HapticFeedback.mediumImpact();

                if ((GoRouterState.of(context).uri.toString() == RouteNames.products) ||
                    (GoRouterState.of(context)
                        .uri
                        .toString()
                        .contains(RouteNames.search))) {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const CartScreen(),
                    ),
                  );
                } else {
                  context.push(RouteNames.cart);
                }
              },
              borderRadius: BorderRadius.circular(50),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    gradient: state.cart.totalItems > 0
                        ? RadialGradient(
                            colors: const [
                              Color(0xFFBEABD3), // lighter
                              Color(0xFF7D56A6), // darker
                            ],
                            center: const Alignment(0, -1.5), // slight top highlight
                            radius: 1,
                            transform: EllipticalGradientTransform(
                              widthFactor: 0.9,  // stretch across pill
                              heightFactor: 0.5, // compress vertically
                            ),
                          )
                        : null,
                    color: state.cart.totalItems > 0 ? null : AppColors.white,
                    border: state.cart.totalItems > 0
                        ? null
                        : Border.all(
                            color: AppColors.primary500,
                            width: 1.5,
                          ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary100,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),

                   
                child: Row(
                  mainAxisSize: MainAxisSize.min, 
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CircleAvatar(
                    backgroundColor: AppColors.primary500,
                    radius: 24,
                      child: const Icon(
                        Icons.shopping_cart_outlined,
                        color: AppColors.white,
                        size: 26,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Text + item count
                    Column(
                      mainAxisSize: MainAxisSize.min, 
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "View Cart",
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        CustomText(
                          "${state.cart.totalItems} ${state.cart.totalItems == 1 ? "item" : "items"}",
                          // style: const TextStyle(
                            color: AppColors.neutral200,
                            fontSize: 12,
                          // ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),

                    // Arrow icon
                    CustomImage(
                      imageUrl: 'assets/new/icons/chevron_right.png',
                      imageColor: AppColors.white,
                      height: 34,
                      width: 24,
                    )
                  ],
                ),
              ),
            ),
          ),
        ),


        );
      },
    );
  }
}

