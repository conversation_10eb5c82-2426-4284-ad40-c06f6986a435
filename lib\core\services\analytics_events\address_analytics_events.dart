import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';


class AddressAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track Homepage Address Bar Clicked
  Future<void> trackHomepageAddressBarClicked() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logHomepageAddressBarClicked(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          homepageSection: 'address_bar',
        );
      }
    } catch (e) {
      debugPrint('Error tracking homepage address bar clicked: $e');
    }
  }

  /// Track Address Map Landing Page
  Future<void> trackAddressMapLandingPage() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logAddressMapLandingPage(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          block: 'unknown',
          district: 'unknown',
          gramPanchayat: 'unknown',
          savedAddressAs: 'unknown',
          selectLocation: 'unknown',
          villagePurwa: 'unknown',
        );
      }
    } catch (e) {
      debugPrint('Error tracking address map landing page: $e');
    }
  }

  /// Track Address Saved
  Future<void> trackAddressSaved({
    required String savedAddressAs,
    required String selectLocation,
    required String block,
    required String gramPanchayat,
    required String villagePurwa,
    required String district,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logAddressSaved(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          savedAddressAs: savedAddressAs,
          selectLocation: selectLocation,
          block: block,
          gramPanchayat: gramPanchayat,
          villagePurwa: villagePurwa,
          district: district,
        );
      }
    } catch (e) {
      debugPrint('Error tracking address saved: $e');
    }
  }
}