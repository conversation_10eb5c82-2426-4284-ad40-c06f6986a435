import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/app_config.dart';

import 'app/app.dart';
import 'core/dependency_injection/di_container.dart';
import 'core/dependency_injection/di_setup.dart';
import 'app/bloc/app_bloc.dart';

// App lifecycle observer to track app launch and close events
class AppLifecycleObserver extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        //_trackAppLaunched();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        //_trackAppClosed();
        break;
      case AppLifecycleState.inactive:
        // App is inactive but not necessarily closed
        break;
    }
  }
}

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();
  // Setup dependency injection
  setupDI();
  await AppConfig.init(); // Initialize app configuration

  // Register app lifecycle observer
  final lifecycleObserver = AppLifecycleObserver();
  WidgetsBinding.instance.addObserver(lifecycleObserver);

  runApp(
    // Provide the AppBloc at the root of the widget tree
    // This allows the splash screen to determine initial routing
    BlocProvider(
      create: (context) => getIt<AppBloc>()..add(const AppStarted()),
      child: MyApp(lifecycleObserver: lifecycleObserver),
    ),
  );
}
