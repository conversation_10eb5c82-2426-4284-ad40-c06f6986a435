import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/extensions/localization_extension.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';
import 'package:rozana/features/search/presentation/screens/search_screen.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_button.dart';
import '../../../../app/bloc/app_bloc.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../../../widgets/custom_border.dart';
import '../../../../widgets/custom_text.dart';
import '../widgets/profile_setup_modal.dart';

import '../../bloc/profile_bloc.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String _appVersion = '';
  String _buildNumber = '';

  @override
  void initState() {
    super.initState();
    // Load user data and address count through bloc
    context.read<ProfileBloc>().add(const ProfileEvent.loadUserData());
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = packageInfo.version;
      _buildNumber = packageInfo.buildNumber;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: AppColors.neutral150,
        appBar: AppBar(
          backgroundColor: AppColors.neutral100,
          scrolledUnderElevation: 0,
          elevation: 0,
          centerTitle: false,
          titleSpacing: 0,
          leadingWidth: 0,
          automaticallyImplyLeading: false,
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              InkWell(
                onTap: () {
                  context.pop();
                },
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                  child: Image.asset(
                    'assets/new/icons/chevron_left.png',
                    height: 26,
                    width: 26,
                    color: AppColors.primary700,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: CustomText(
                  "My Profile",
                  color: AppColors.primary700,
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ],
          ),
        ),
        body: BlocListener<ProfileBloc, ProfileState>(
          listener: (context, state) {
            state.mapOrNull(
              error: (errorState) {
                if (errorState.message.contains('logout')) {
                  // Handle logout success by navigating to login
                  context.go(RouteNames.login);
                }
              },
            );
          },
          child: BlocBuilder<ProfileBloc, ProfileState>(
            builder: (context, state) {
              return ProfileBody(
                version: _appVersion,
                buildNumber: _buildNumber,
              );
            },
          ),
        ),
      ),
    );
  }
}

class ProfileBody extends StatelessWidget {
  const ProfileBody(
      {super.key, required this.version, required this.buildNumber});

  final String version;
  final String buildNumber;

  @override
  Widget build(BuildContext context) {
    bool isAuthenticated = getIt<AppBloc>().isAuthenticated;
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ProfileBloc>().add(const ProfileEvent.loadUserData());
        // Wait a bit for the bloc to process
        await Future.delayed(const Duration(milliseconds: 500));
      },
      color: AppColors.primary,
      child: LayoutBuilder(builder: (context, constraints) {
        return SingleChildScrollView(
          child: Column(
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(
                    minHeight: constraints.maxHeight -
                        (MediaQuery.of(context).padding.vertical) -
                        (kToolbarHeight) -
                        40),
                child: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.all(16.sp),
                  child: BlocBuilder<ProfileBloc, ProfileState>(
                    builder: (context, state) {
                      return state.map(
                        initial: (_) => Center(
                            child: CircularProgressIndicator(
                          color: AppColors.primary,
                        )),
                        loading: (_) => Center(
                            child: CircularProgressIndicator(
                          color: AppColors.primary,
                        )),
                        error: (errorState) =>
                            ProfileError(errorMessage: errorState.message),
                        loaded: (loadedState) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  if (isAuthenticated) {
                                    final state =
                                        context.read<ProfileBloc>().state;
                                    String? name;
                                    String? email;
                                    state.mapOrNull(
                                      loaded: (loadedState) {
                                        name = loadedState.userName;
                                        email = loadedState.userEmail;
                                      },
                                    );
                                    final updated = await showProfileSetupModal(
                                      context,
                                      initialName: name,
                                      initialEmail: email,
                                      isEdit: true,
                                    );
                                    if (updated == true) {
                                      // ignore: use_build_context_synchronously
                                      context.read<ProfileBloc>().add(
                                          const ProfileEvent.loadUserData());
                                    }
                                  }
                                },
                                child: ProfileCard(
                                  padding: EdgeInsets.all(16.sp),
                                  child: Row(
                                    children: [
                                      Visibility(
                                        visible: isAuthenticated,
                                        child: Padding(
                                          padding:
                                              EdgeInsets.only(right: 12.sp),
                                          child: CircleAvatar(
                                            radius: 20.sp,
                                            backgroundColor:
                                                AppColors.secondary100,
                                            child:
                                                BlocBuilder<AppBloc, AppState>(
                                              builder: (context, state) {
                                                return Center(
                                                    child: Image.asset(
                                                  'assets/new/icons/account_circle.png',
                                                  width: 24.sp,
                                                  height: 24.sp,
                                                ));
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            CustomText(
                                              isAuthenticated
                                                  ? loadedState.userName
                                                  : 'Hello there  👋',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w800,
                                              color: AppColors.neutral700,
                                            ),
                                            SizedBox(height: 2),
                                            Visibility(
                                              visible: isAuthenticated,
                                              replacement: Padding(
                                                padding:
                                                    EdgeInsets.only(top: 4.sp),
                                                child: CustomButton(
                                                  text: 'Login or sign up',
                                                  borderRadius: 10,
                                                  onPressed: () {
                                                    context
                                                        .push(RouteNames.login)
                                                        .then((_) {
                                                      if (!context.mounted) {
                                                        return;
                                                      }
                                                      context
                                                          .read<ProfileBloc>()
                                                          .add(const ProfileEvent
                                                              .loadUserData());
                                                    });
                                                  },
                                                ),
                                              ),
                                              child: CustomText(
                                                loadedState.phoneNumber
                                                    .split('+91')
                                                    .join('+91 - '),
                                                // '+91 - 9876543220',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.neutral600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Visibility(
                                visible: isAuthenticated,
                                child: Padding(
                                  padding: EdgeInsets.only(top: 12.sp),
                                  child: Row(
                                    children: [
                                      ProfileItemCard(
                                        image:
                                            'assets/new/icons/receipt_long.png',
                                        title: 'Orders',
                                        onTap: () {
                                          context.replace(RouteNames.orders);
                                        },
                                      ),
                                      SizedBox(width: 12.sp),
                                      ProfileItemCard(
                                        image:
                                            'assets/new/icons/support_agent.png',
                                        title: 'Support',
                                        onTap: () {
                                          final existingUserJson =
                                              AppPreferences.getUserdata();
                                          Map<String, dynamic> userData = {};

                                          if (existingUserJson != null &&
                                              existingUserJson.isNotEmpty) {
                                            userData =
                                                jsonDecode(existingUserJson);
                                          }
                                          context.push(RouteNames.support,
                                              extra: {
                                                'customer_id':
                                                    userData['enc_cus_code'],
                                                'iv': userData['iv']
                                              });
                                        },
                                      ),
                                      SizedBox(width: 12.sp),
                                      ProfileItemCard(
                                        image:
                                            'assets/new/icons/account_balance_wallet.png',
                                        title: 'Wallet',
                                        onTap: () {},
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Visibility(
                                visible: isAuthenticated,
                                child: Padding(
                                  padding: EdgeInsets.only(top: 12.sp),
                                  child: ProfileCard(
                                      padding: EdgeInsets.zero,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          ProfileItemTitle(
                                              title: 'Your account'),
                                          BlocBuilder<AddressBloc,
                                              AddressState>(
                                            builder: (context, state) {
                                              return ProfileItemTile(
                                                icon:
                                                    'assets/new/icons/location_on.png',
                                                title: 'Address book',
                                                trailingText:
                                                    "${state.addresses?.length ?? 0}",
                                                onTap: () {
                                                  context.push(
                                                      RouteNames.addresses);
                                                },
                                              );
                                            },
                                          ),
                                        ],
                                      )),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(top: 12.sp),
                                child: ProfileCard(
                                    padding: EdgeInsets.zero,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ProfileItemTitle(title: 'Rozana'),
                                        ProfileItemTile(
                                          icon:
                                              'assets/new/icons/language_icon.png',
                                          title: context.l10n.language,
                                          trailingWidget: BlocBuilder<
                                              LanguageBloc, LanguageState>(
                                            builder: (context, languageState) {
                                              final currentLanguage =
                                                  languageState.mapOrNull(
                                                        loaded: (state) =>
                                                            state.languageCode,
                                                      ) ??
                                                      'en';

                                              return Padding(
                                                padding: EdgeInsets.only(
                                                    right: 8.sp),
                                                child: DropdownButton<String>(
                                                  value: currentLanguage,
                                                  underline: const SizedBox(),
                                                  icon: Image.asset(
                                                    'assets/new/icons/chevron-down.png',
                                                    height: 24.sp,
                                                    width: 24.sp,
                                                    color: AppColors.primary400,
                                                  ),
                                                  items: [
                                                    DropdownMenuItem(
                                                      value: 'en',
                                                      child: CustomText(
                                                        context.l10n.english,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: AppColors
                                                            .primary400,
                                                      ),
                                                    ),
                                                    DropdownMenuItem(
                                                      value: 'hi',
                                                      child: CustomText(
                                                        context.l10n.hindi,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: AppColors
                                                            .primary400,
                                                      ),
                                                    ),
                                                  ],
                                                  onChanged:
                                                      (String? newLanguage) {
                                                    HapticFeedback
                                                        .lightImpact();
                                                    if (newLanguage != null &&
                                                        newLanguage !=
                                                            currentLanguage) {
                                                      context
                                                          .read<LanguageBloc>()
                                                          .add(LanguageEvent
                                                              .changeLanguage(
                                                                  newLanguage));
                                                    }
                                                  },
                                                ),
                                              );
                                            },
                                          ),
                                          showTrailingIcon: false,
                                        ),
                                        ProfileDottedLine(),
                                        ProfileItemTile(
                                          icon: 'assets/new/icons/info.png',
                                          title: 'About us',
                                          onTap: () {
                                            context.push(RouteNames.about);
                                          },
                                        ),
                                        ProfileDottedLine(),
                                        ProfileItemTile(
                                          icon:
                                              'assets/new/icons/privacy_tip.png',
                                          title: 'Privacy policy',
                                          onTap: () {
                                            context
                                                .push(RouteNames.privacyPolicy);
                                          },
                                        ),
                                        ProfileDottedLine(),
                                        ProfileItemTile(
                                          icon: 'assets/new/icons/feed.png',
                                          title: 'Terms of service',
                                          onTap: () {
                                            context.push(
                                                RouteNames.termsConditions);
                                          },
                                        ),
                                      ],
                                    )),
                              ),
                              Visibility(
                                visible: isAuthenticated,
                                child: Padding(
                                  padding: EdgeInsets.only(top: 12.sp),
                                  child: ProfileCard(
                                      padding: EdgeInsets.zero,
                                      child: Column(
                                        children: [
                                          ProfileItemTile(
                                            icon: 'assets/new/icons/logout.png',
                                            title: 'Logout',
                                            onTap: () {
                                              context.read<ProfileBloc>().add(
                                                  const ProfileEvent.logout());
                                            },
                                          ),
                                          ProfileDottedLine(),
                                          ProfileItemTile(
                                            icon: 'assets/new/icons/delete.png',
                                            title: 'Delete account',
                                            iconColor: AppColors.red400,
                                            textColor: AppColors.red500,
                                          ),
                                        ],
                                      )),
                                ),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
              CustomText(
                'Rozana © ${DateTime.now().year}',
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral400,
              ),
              SizedBox(height: 8.sp),
              CustomText(
                'Version $version($buildNumber)',
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral400,
              ),
              SizedBox(height: 30.sp),
            ],
          ),
        );
      }),
    );
  }
}

class ProfileCard extends StatelessWidget {
  const ProfileCard({super.key, this.padding, required this.child, this.side});

  final EdgeInsetsGeometry? padding;
  final Widget child;
  final BorderSide? side;

  @override
  Widget build(BuildContext context) {
    return CustomBorder(
      radius: 16,
      side: side,
      color: AppColors.neutral100,
      child: Container(
        padding:
            padding ?? EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
        child: child,
      ),
    );
  }
}

class ProfileItemCard extends StatelessWidget {
  const ProfileItemCard(
      {super.key, required this.image, required this.title, this.onTap});
  final String image;
  final String title;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
      },
      child: ProfileCard(
          side: BorderSide(color: AppColors.primary100, width: 0.5),
          child: Column(
            children: [
              Image.asset(
                image,
                width: 24.sp,
                height: 24.sp,
                color: AppColors.primary700,
              ),
              SizedBox(height: 4.sp),
              CustomText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.primary700,
              )
            ],
          )),
    ));
  }
}

class ProfileItemTitle extends StatelessWidget {
  const ProfileItemTitle({super.key, required this.title});
  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 14.sp),
          child: CustomText(
            title,
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColors.neutral700,
          ),
        ),
        Divider(
          height: 1,
          thickness: 1,
          color: AppColors.neutral150,
        ),
      ],
    );
  }
}

class ProfileItemTile extends StatelessWidget {
  const ProfileItemTile({
    super.key,
    required this.icon,
    required this.title,
    this.iconColor,
    this.textColor,
    this.onTap,
    this.trailingText,
    this.trailingWidget,
    this.showTrailingIcon = true,
  });
  final String icon;
  final String title;
  final Color? iconColor;
  final Color? textColor;
  final void Function()? onTap;
  final String? trailingText;
  final Widget? trailingWidget;
  final bool showTrailingIcon;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
      },
      child: Padding(
        padding: EdgeInsets.all(8.sp),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.all(6.sp),
              child: Image.asset(
                icon,
                width: 24.sp,
                height: 24.sp,
                color: iconColor ?? AppColors.primary600,
              ),
            ),
            SizedBox(width: 8.sp),
            Expanded(
              child: CustomText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: textColor ?? AppColors.primary700,
              ),
            ),
            Visibility(
              visible: (trailingText?.isNotEmpty ?? false) ||
                  (trailingWidget != null),
              child: Padding(
                padding: EdgeInsets.only(left: 8.sp),
                child: trailingWidget ??
                    CustomText(
                      trailingText ?? '',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary400,
                    ),
              ),
            ),
            Visibility(
              visible: showTrailingIcon,
              child: Padding(
                padding: EdgeInsets.all(6.sp),
                child: Image.asset(
                  'assets/new/icons/chevron_right.png',
                  width: 24.sp,
                  height: 24.sp,
                  color: AppColors.primary400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ProfileDottedLine extends StatelessWidget {
  const ProfileDottedLine({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: DottedLine(
        color: AppColors.neutral150,
      ),
    );
  }
}

class ProfileError extends StatelessWidget {
  const ProfileError({super.key, required this.errorMessage});

  final String errorMessage;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              context.l10n.somethingWentWrong,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.neutral600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.neutral400,
              ),
            ),
            const SizedBox(height: 24),
            AppButton(
              text: context.l10n.retry,
              onPressed: () {
                context
                    .read<ProfileBloc>()
                    .add(const ProfileEvent.loadUserData());
              },
              backgroundColor: AppColors.primary,
              width: 120,
            ),
          ],
        ),
      ),
    );
  }
}
