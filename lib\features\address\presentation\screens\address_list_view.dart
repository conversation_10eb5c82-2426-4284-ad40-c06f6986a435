import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/features/address/utils/address_utils.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../bloc/address_bloc.dart';
import '../widgets/address_option_bottomsheet.dart';

class AddressListView extends StatelessWidget {
  final List<AddressModel> addresses;
  final AddressModel? selectedAddress;
  final ScrollController? scrollController;
  final bool filterServiceStatus;

  const AddressListView({
    super.key,
    required this.addresses,
    this.selectedAddress,
    this.scrollController,
    this.filterServiceStatus = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.fromLTRB(16, 16, 16, 150),
      controller: scrollController,
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        final address = addresses[index];
        final isSelected = address.id == selectedAddress?.id;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Visibility(
              visible: index == 0,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: CustomText(
                  'Your saved addresses',
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: AppColors.neutral500,
                ),
              ),
            ),
            AbsorbPointer(
              absorbing:
                  filterServiceStatus && (!(address.isServicable ?? false)),
              child: Opacity(
                opacity:
                    filterServiceStatus && (!(address.isServicable ?? false))
                        ? 0.5
                        : 1,
                child: AddressListTile(
                  address: address,
                  isSelected: isSelected,
                  applyFilter: filterServiceStatus,
                ),
              ),
            ),
          ],
        );
      },
      separatorBuilder: (context, index) => SizedBox(height: 12),
    );
  }
}

class AddressListTile extends StatelessWidget {
  const AddressListTile({
    super.key,
    this.address,
    required this.isSelected,
    required this.applyFilter,
  });

  final AddressModel? address;
  final bool isSelected;
  final bool applyFilter;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AddressBloc, AddressState>(
      listenWhen: (previous, current) {
        return current.maybeMap(
          addressList: (_) => true,
          orElse: () => false,
        );
      },
      listener: (context, state) {
        if (state.maybeMap(addressList: (_) => true, orElse: () => false)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (context.mounted && context.canPop()) {
              context.pop();
            }
          });
        }
      },
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          AddressBloc addressBloc = getIt<AddressBloc>();
          addressBloc.add(AddressEvent.selectAddress(address?.id ?? ''));
        },
        child: BlocBuilder<AddressBloc, AddressState>(
          buildWhen: (previous, current) {
            bool? prevSelected = previous.mapOrNull(
              addressList: (state) => state.selectedAddress?.id != address?.id,
            );
            bool? currentSelected = current.mapOrNull(
              addressList: (state) => state.selectedAddress?.id != address?.id,
            );
            return (currentSelected != null &&
                (prevSelected != currentSelected));
          },
          builder: (context, state) {
            return Container(
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      isSelected ? AppColors.primary500 : AppColors.neutral100,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 40.sp,
                      width: 40.sp,
                      decoration: BoxDecoration(
                        color: Color(0xFFF7F7F7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Image.asset(
                          (address?.addressType?.toLowerCase() == 'home')
                              ? 'assets/new/icons/house.png'
                              : (address?.addressType?.toLowerCase() == 'work')
                                  ? 'assets/new/icons/corporate_fare.png'
                                  : 'assets/new/icons/pin_drop.png',
                          height: 24.sp,
                          width: 24.sp,
                          color: AppColors.primary600,
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            AddressUtils.getAddressTitle(address),
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            color: AppColors.primary700,
                          ),
                          SizedBox(height: 2),
                          CustomText(
                            address?.fullAddress ?? '',
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: AppColors.neutral500,
                            maxLines: 3,
                          ),
                          Text.rich(
                            TextSpan(
                              text: "Phone number: ",
                              children: [
                                TextSpan(
                                  text: (address?.phone ?? '').substring(
                                    ((address?.phone?.length ?? 10) - 10),
                                    address?.phone?.length ?? 0,
                                  ),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.neutral600,
                                  ),
                                ),
                              ],
                            ),
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColors.neutral500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: !applyFilter,
                      child: InkWell(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _showEditAddressBottomSheet(context, address);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(6.0),
                          child: Image.asset(
                            'assets/new/icons/edit_location.png',
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

void _showEditAddressBottomSheet(
  BuildContext context,
  AddressModel? address,
) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    useRootNavigator: true,
    builder: (ctx) {
      return EditAddressBottomSheet(
        address: address,
      );
    },
  );
}
