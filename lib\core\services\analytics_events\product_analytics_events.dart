import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';



class ProductAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track PDP opened
  Future<void> trackPDPOpened({
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String pdpLocation,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logPDPOpened(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          discount: discount,
          categoryName: categoryName,
          subCategoryName: subCategoryName,
          pdpLocation: pdpLocation,
          ipAddress: ipAddress,
          latitude: latitude,
          longitude: longitude,
        );
      }
    } catch (e) {
      debugPrint('Error tracking PDP opened: $e');
    }
  }

  /// Track PDP size selected
  Future<void> trackPDPSizeSelected({
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String sizeSelected,
    required String screenName,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logPDPSizeSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          discount: discount,
          categoryName: categoryName,
          subCategoryName: subCategoryName,
          size: sizeSelected,
          pdpLocation: 'PDP',
        );
      }
    } catch (e) {
      debugPrint('Error tracking PDP size selected: $e');
    }
  }

  /// Track PDP image scroll
  Future<void> trackPDPImageScroll({
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String imageScrollDirection,
    required String screenName,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logPDPImageScroll(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          discount: discount,
          categoryName: categoryName,
          subCategoryName: subCategoryName,
          subSubCategoryName: 'default',
          pdpLocation: 'PDP',
        );
      }
    } catch (e) {
      debugPrint('Error tracking PDP image scroll: $e');
    }
  }

  /// Track PDP view similar selected
  Future<void> trackPDPViewSimilarSelected({
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String viewSimilarLocation,
    required String screenName,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logPDPViewSimilarSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          discount: discount,
          categoryName: categoryName,
          subCategoryName: subCategoryName,
          subSubCategoryName: 'default',
          pdpLocation: 'PDP',
        );
      }
    } catch (e) {
      debugPrint('Error tracking PDP view similar selected: $e');
    }
  }

  /// Track product shared
  Future<void> trackProductShared({
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required Map<String, dynamic> allAttributes,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logProductShared(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          productSkuId: productSkuId,
          productName: productName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          discount: discount,
          allAttributes: allAttributes,
        );
      }
    } catch (e) {
      debugPrint('Error tracking product shared: $e');
    }
  }

  /// Track Add to Cart
  Future<void> trackAddToCart({
    required String productSkuId,
    required String productName,
    required String screenName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required Map<String, dynamic> allAttributes,
    String? variantBottomSheet,
    String? variantBottomSheetValue,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logAddToCart(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          productSkuId: productSkuId,
          productName: productName,
          screenName: screenName,
          mrp: mrp,
          sellingPrice: sellingPrice,
          discount: discount,
          categoryName: categoryName,
          allAttributes: allAttributes,
          variantBottomSheet: variantBottomSheet ?? '',
          variantBottomSheetValue: variantBottomSheetValue ?? '',
        );
      }
    } catch (e) {
      debugPrint('Error tracking add to cart: $e');
    }
  }

}
