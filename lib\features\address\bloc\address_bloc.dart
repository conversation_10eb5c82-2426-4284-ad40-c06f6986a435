// Export statements for event and state classes
export 'address_event.dart';
export 'address_state.dart';

// Package imports
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:rozana/core/network/google_api_client.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/app_validators.dart';
import 'package:rozana/core/utils/notifier.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:rozana/data/models/place_suggestion_model.dart';
import 'package:rozana/features/address/services/location_services.dart';
import 'package:rozana/features/address/utils/address_utils.dart';

// Local project imports
import '../../../core/dependency_injection/di_container.dart';
import '../../../core/services/analytics_events/address_analytics_events.dart';
import '../../../core/utils/debouncer.dart';
import '../../../core/utils/text_field_manager.dart';
import '../../location/services/google_places_service.dart';
import '../../search/services/typesense_service.dart';
import '../services/address_repository.dart';
import 'address_event.dart';
import 'address_state.dart';

class AddressBloc extends Bloc<AddressEvent, AddressState> {
  final AddressRepository _addressRepository;
  final AddressLocationService _locationService;
  final GooglePlacesService _googlePlacesService;
  final GoogleApiClient _googleApiClient;
  final double _deliveryRadiusMeters = 1000.0;

  static String? mapStyle;

  final _debouncer = Debouncer(
    milliseconds: 300,
  );

  static TextEditingController? searchController;
  static TextFieldManager? addressNameController;
  static TextFieldManager? flatNameController;
  static TextFieldManager? floorNoController;
  static TextFieldManager? localityController;
  static TextFieldManager? landmarkController;
  static TextFieldManager? contactNameController;
  static TextFieldManager? contactNumberController;

  static ValueNotifier<String> orderingFor = ValueNotifier('myself');
  static ValueNotifier<String> selectedAddressType = ValueNotifier('home');

  AddressBloc(
    this._addressRepository,
    this._locationService,
    this._googlePlacesService,
    this._googleApiClient,
  ) : super(const AddressState.initial()) {
    searchController = TextEditingController();
    on<AddressEvent>((event, emit) async {
      await event.when(
        init: () => _onInit(emit),
        started: (startMap, applyFilter) =>
            _handleStarted(emit, startMap: startMap, applyFilter: applyFilter),
        initMap: () => _onInitMap(emit),
        getCurrentLocation: () => _handleGetCurrentLocation(emit),
        selectFromMap: (address) => _handleSelectFromMap(address),
        initSearch: () => _onInitSearch(emit),
        saveAddress: () => _handleSaveAddress(),
        deleteAddress: (addressId) => _handleDeleteAddress(addressId),
        fetchAddresses: (filter) =>
            _handleFetchAddresses(filterServiceStatus: filter),
        selectAddress: (addressId) => _handleSelectAddress(addressId),
        mapCameraIdle: (lat, long) => _handleMapCameraIdle(lat, long),
        searchPlaces: (query) => _handleSearchPlaces(query),
        selectPlace: (placeId) => _handleSelectPlace(placeId),
        emitAsyncState: (state) async => emit(state),
      );
    });
  }

  bool _applyFilter = false;

  @override
  Future<void> close() {
    _debouncer.dispose();
    searchController?.dispose();
    addressNameController?.dispose();
    flatNameController?.dispose();
    floorNoController?.dispose();
    localityController?.dispose();
    landmarkController?.dispose();
    contactNameController?.dispose();
    contactNumberController?.dispose();
    return super.close();
  }

  Future<void> _onInit(Emitter<AddressState> emit) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_onInit()::::::");
    if (_addressRepository.isUserAuthenticated) {
      _handleFetchAndSelectAddresses(filterServiceStatus: _applyFilter);
    } else {
      await _handleGetCurrentLocation(emit, initializeStore: true);
    }
  }

  Future<void> _handleStarted(Emitter<AddressState> emit,
      {bool startMap = false, bool applyFilter = false}) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleStarted()::::::");
    rootBundle.loadString('assets/map/snazzy_style.json').then((style) {
      mapStyle = style;
    });
    _applyFilter = applyFilter;
    if (_addressRepository.isUserAuthenticated && !startMap) {
      await _handleFetchAndSelectAddresses(filterServiceStatus: applyFilter);
    } else {
      await _onInitMap(emit);
    }
  }

  Future<void> _onInitMap(Emitter<AddressState> emit) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_onInitMap()::::::");
    emit(AddressState.mapSelection(
      temporaryAddress: state.temporaryAddress,
      selectedAddress: state.selectedAddress,
      currentLocation: state.currentLocation,
      isServicable: state.isServicable,
      addresses: state.addresses,
      applyFilter: _applyFilter,
    ));
  }

  Future<void> _handleGetCurrentLocation(Emitter<AddressState> emit,
      {bool initializeStore = false}) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleGetCurrentLocation()::::::");
    if (state.currentLocation != null) {
      emit(state.copyWith(
        currentLocation: state.currentLocation,
        temporaryAddress: state.currentLocation,
        relocateMap: true,
      ));
    }
    try {
      final position = await _locationService.getCurrentPosition();
      if (position != null) {
        final address = await _locationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );
        final tempAddress = AddressModel(
          latitude: position.latitude,
          longitude: position.longitude,
          fullAddress:
              address.isNotEmpty ? address.first.street : 'Current location',
        );
        AddressModel? enhancedAddress = await _getEnhancedAddress(tempAddress);

        if (initializeStore) {
          final typesenseService = getIt<TypesenseService>();
          await typesenseService.reinitializeWithCoordinates(
            enhancedAddress?.latitude?.toDouble() ?? 0,
            enhancedAddress?.longitude?.toDouble() ?? 0,
          );

          bool isServiceable = typesenseService.isLocationServiceable;

          String duration = await _onFetchDuration();
          add(AddressEvent.emitAsyncState(
            state.copyWith(
              temporaryAddress: enhancedAddress,
              selectedAddress: enhancedAddress,
              currentLocation: enhancedAddress,
              relocateMap: true,
              isServicable: isServiceable,
              delliveryDuration: duration,
            ),
          ));
        } else {
          add(AddressEvent.emitAsyncState(
            state.copyWith(
              temporaryAddress: enhancedAddress,
              currentLocation: enhancedAddress,
              relocateMap: true,
            ),
          ));
        }
      } else {
        add(const AddressEvent.emitAsyncState(
            AddressState.error('Could not get current position')));
      }
    } catch (e) {
      LogMessage.p('Error getting current position: $e');
      add(const AddressEvent.emitAsyncState(
          AddressState.error('Failed to get location.')));
    }
  }

  Future<void> _handleMapCameraIdle(double latitude, double longitude) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleMapCameraIdle()::::::");
    final currentAddress =
        await _locationService.getAddressFromCoordinates(latitude, longitude);
    final tempAddress = AddressModel(
      latitude: latitude,
      longitude: longitude,
      fullAddress: currentAddress.isNotEmpty
          ? currentAddress.first.street
          : 'Address not found',
    );
    AddressModel? enhancedAddress = await _getEnhancedAddress(tempAddress);
    add(AddressEvent.emitAsyncState(
        state.copyWith(temporaryAddress: enhancedAddress, relocateMap: false)));
  }

  Future<void> _onInitSearch(Emitter<AddressState> emit) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_onInitSearch()::::::");
    emit(AddressState.searchScreen(
      temporaryAddress: state.temporaryAddress,
      currentLocation: state.currentLocation,
      selectedAddress: state.selectedAddress,
      addresses: state.addresses,
      applyFilter: _applyFilter,
    ));
  }

  Future<void> _handleSearchPlaces(String query) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleSearchPlaces()::::::");
    _debouncer.run(() async {
      await state.mapOrNull(
        searchScreen: (value) async {
          AddressModel? enhancedAddress = value.temporaryAddress;
          if (query.isEmpty) {
            state.mapOrNull(
              searchScreen: (searchState) {
                add(AddressEvent.emitAsyncState(searchState.copyWith(
                    temporaryAddress: enhancedAddress,
                    searchResults: [],
                    isSearching: false)));
              },
            );
            return;
          }

          try {
            final results = await _googlePlacesService.getPlaceAutocomplete(
                input: query, sessionToken: 'temp_token');

            List<PlaceSuggestionModel>? suggestions =
                results.map((e) => PlaceSuggestionModel.fromJson(e)).toList();

            state.mapOrNull(
              searchScreen: (searchState) {
                add(AddressEvent.emitAsyncState(searchState.copyWith(
                    temporaryAddress: enhancedAddress,
                    searchResults: suggestions,
                    isSearching: false)));
              },
            );
          } catch (e) {
            add(const AddressEvent.emitAsyncState(
                AddressState.error('Failed to search locations.')));
          }
        },
      );
    });
  }

  Future<void> _handleSelectPlace(String placeId) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleSelectPlace()::::::");

    state.mapOrNull(
      searchScreen: (value) async {
        getIt<AppNotifier>().showLoading();
        try {
          final location =
              await _googlePlacesService.getPlaceDetailsById(placeId);
          Map<String, dynamic>? latLngData = location;

          if (kIsWeb) {
            latLngData = location?['geometry']?['location'];
          }

          if (location != null) {
            final lat =
                double.tryParse(latLngData?['lat']?.toString() ?? '') ?? 0;
            final lng =
                double.tryParse(latLngData?['lng']?.toString() ?? '') ?? 0;
            final address =
                await _locationService.getAddressFromCoordinates(lat, lng);

            final newTempAddress = AddressModel(
              latitude: lat,
              longitude: lng,
              fullAddress: address.isNotEmpty
                  ? address.first.street
                  : location['name'] ?? 'Address not found',
            );
            AddressModel? newEnhancedAddress =
                await _getEnhancedAddress(newTempAddress);
            getIt<AppNotifier>().hideLoading();
            add(AddressEvent.emitAsyncState(AddressState.mapSelection(
              temporaryAddress: newEnhancedAddress,
              currentLocation: state.currentLocation,
              selectedAddress: state.selectedAddress,
              relocateMap: true,
              addresses: state.addresses,
              isServicable: state.isServicable,
              delliveryDuration: state.delliveryDuration,
              applyFilter: _applyFilter,
            )));
          } else {
            getIt<AppNotifier>().hideLoading();
            add(const AddressEvent.emitAsyncState(
                AddressState.error('Failed to get location details.')));
          }
        } catch (e) {
          LogMessage.p('Failed to select place: $e');
          add(const AddressEvent.emitAsyncState(
              AddressState.error('Failed to get location details.')));
        }
      },
    );
  }

  Future<void> _handleFetchAndSelectAddresses(
      {bool filterServiceStatus = false}) async {
    LogMessage.l(
        "ADDRESSBLOC=>::::::::::::_handleFetchAndSelectAddresses()::::::");
    add(const AddressEvent.emitAsyncState(AddressState.loading()));
    try {
      final addresses = await _addressRepository.getAllAddresses();

      AddressModel? selectedAddress = AppPreferences.getSelectedAddress();

      if (selectedAddress == null) {
        final currentPosition = await _locationService.getCurrentPosition();
        selectedAddress = _findNearestAddress(addresses, currentPosition);
      }

      if (selectedAddress == null && addresses.isNotEmpty) {
        selectedAddress = addresses.first;
      }

      if (selectedAddress != null) {
        AppPreferences.setSelectedAddress(selectedAddress);
      }

      final typesenseService = getIt<TypesenseService>();

      List<Future<AddressModel>> filteredAddresses = addresses.map((a) async {
        bool isServicable = a.isServicable ?? false;
        if (filterServiceStatus) {
          final store = await typesenseService.findStoreByCoordinates(
              a.latitude?.toDouble() ?? 0, a.longitude?.toDouble() ?? 0);
          isServicable = store != null;
        }
        return a.copyWith(
            isSelected: a.id == selectedAddress?.id,
            isServicable: isServicable);
      }).toList();

      List<AddressModel> updatedAddresses =
          await Future.wait(filteredAddresses);

      await typesenseService.reinitializeWithCoordinates(
        selectedAddress?.latitude?.toDouble() ?? 0,
        selectedAddress?.longitude?.toDouble() ?? 0,
      );

      bool isServiceable = typesenseService.isLocationServiceable;

      String duration = await _onFetchDuration();

      add(AddressEvent.emitAsyncState(AddressState.addressList(
        addresses: updatedAddresses,
        selectedAddress: selectedAddress,
        isLoading: false,
        hasError: false,
        isServicable: isServiceable,
        delliveryDuration: duration,
        applyFilter: filterServiceStatus,
      )));
    } catch (e) {
      LogMessage.p('Error during address fetch/selection: $e');
      add(const AddressEvent.emitAsyncState(
          AddressState.error('Failed to load addresses.')));
    }
  }

  AddressModel? _findNearestAddress(
      List<AddressModel> addresses, Position? currentPosition) {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_findNearestAddress()::::::");
    if (addresses.isEmpty) {
      return null;
    }

    if (currentPosition == null) {
      return addresses.first;
    }

    AddressModel? nearest;
    double minDistance = double.infinity;

    for (final address in addresses) {
      if (address.latitude != null && address.longitude != null) {
        final distance = Geolocator.distanceBetween(
          currentPosition.latitude,
          currentPosition.longitude,
          address.latitude!.toDouble(),
          address.longitude!.toDouble(),
        );

        if (distance <= _deliveryRadiusMeters && distance < minDistance) {
          minDistance = distance;
          nearest = address;
        }
      }
    }
    return nearest;
  }

  Future<void> _handleSelectFromMap(AddressModel? editAddress) async {
    try {
      LogMessage.l("ADDRESSBLOC=>::::::::::::_handleSelectFromMap()::::::");
      AddressModel? address = editAddress ?? state.temporaryAddress;
      if (_addressRepository.isUserAuthenticated) {
        addressNameController = TextFieldManager();
        flatNameController = TextFieldManager();
        floorNoController = TextFieldManager();
        localityController = TextFieldManager();
        landmarkController = TextFieldManager();
        contactNameController = TextFieldManager();
        contactNumberController = TextFieldManager();
        add(AddressEvent.emitAsyncState(AddressState.addressForm(
          temporaryAddress: address,
          addresses: state.addresses,
          isEdit: editAddress != null,
          selectedAddress: state.selectedAddress,
          isServicable: state.isServicable,
          delliveryDuration: state.delliveryDuration,
          applyFilter: _applyFilter,
        )));
      }
    } catch (e) {
      LogMessage.p('Error selecting address from map: $e');
      add(const AddressEvent.emitAsyncState(
          AddressState.error('Failed to select address.')));
    }
  }

  Future<void> _handleSaveAddress() async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleSaveAddress()::::::");
    //  getIt<AppNotifier>().showLoading();

    bool addressValid = false;

    ValidationState addressValidationState = AppValidator.emptyStringValidator(
        flatNameController?.text.trim(),
        'Please enter Flat / House no. / Building name');

    ValidationState contacNameValidation = AppValidator.invalidStringValidator(
        contactNameController?.text.trim(), 'Please enter contact name',
        invalidMessage: 'Please enter valid contact name');

    ValidationState contacNumberValidation = AppValidator.mobileNumberValidator(
        contactNumberController?.text.trim());

    if (addressNameController?.text.isNotEmpty ?? false) {
      String text = (addressNameController?.text ?? '').toLowerCase();
      if (text == 'home' || text == 'work') {
        addressNameController?.throwError(
            'This name is already in use for a specific address type. Please pick a different one.');
      }
    } else {
      addressNameController?.throwError('');
    }

    if (!addressValidationState.valid) {
      flatNameController?.throwError(addressValidationState.message ?? '');
    } else {
      flatNameController?.throwError('');
    }

    if (!contacNameValidation.valid) {
      contactNameController?.throwError(contacNameValidation.message ?? '');
    } else {
      contactNameController?.throwError('');
    }

    if (!contacNumberValidation.valid) {
      contactNumberController?.throwError(contacNumberValidation.message ?? '');
    } else {
      contactNumberController?.throwError('');
    }

    if (addressValidationState.valid &&
        contacNameValidation.valid &&
        contacNumberValidation.valid) {
      addressValid = true;
    }

    if (addressValid) {
      AddressModel? completeAddress = state.temporaryAddress?.copyWith(
        savedFor: orderingFor.value,
        addressName: addressNameController?.text.trim(),
        addressType: orderingFor.value.toLowerCase() == 'someone else'
            ? 'other'
            : selectedAddressType.value,
        addressLine1: flatNameController?.text.trim(),
        addressLine2: floorNoController?.text.trim(),
        landmark: landmarkController?.text.trim(),
        name: contactNameController?.text.trim(),
        phone: contactNumberController?.text.trim(),
      );

      try {
        await _addressRepository.saveAddress(completeAddress ?? AddressModel());

        // Track address saved analytics
        AddressAnalyticsEvents().trackAddressSaved(
          savedAddressAs: completeAddress?.addressName ?? 'Unknown',
          selectLocation: completeAddress?.fullAddress ?? 'Unknown',
          block: completeAddress?.addressLine1 ?? 'Unknown',
          gramPanchayat: completeAddress?.city ?? 'Unknown',
          villagePurwa: completeAddress?.landmark ?? 'Unknown',
          district: completeAddress?.state ?? 'Unknown',
        );

        await _handleFetchAndSelectAddresses(filterServiceStatus: _applyFilter);
      } catch (e) {
        add(const AddressEvent.emitAsyncState(
            AddressState.error('Failed to save address.')));
      }
    }
  }

  Future<void> _handleDeleteAddress(String addressId) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleDeleteAddress()::::::");
    state.mapOrNull(
      addressList: (value) async {
        try {
          getIt<AppNotifier>().showLoading();
          await _addressRepository.deleteAddress(addressId);
          await _handleFetchAndSelectAddresses(
              filterServiceStatus: _applyFilter);
          getIt<AppNotifier>().hideLoading();
        } catch (e) {
          getIt<AppNotifier>().hideLoading();
          add(AddressEvent.emitAsyncState(value.copyWith(hasError: true)));
          add(const AddressEvent.emitAsyncState(
              AddressState.error('Failed to delete address.')));
        }
      },
    );
  }

  Future<void> _handleFetchAddresses({bool filterServiceStatus = false}) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleFetchAddresses()::::::");
    await _handleFetchAndSelectAddresses(
        filterServiceStatus: filterServiceStatus);
  }

  Future<void> _handleSelectAddress(String addressId) async {
    LogMessage.l("ADDRESSBLOC=>::::::::::::_handleSelectAddress()::::::");
    state.mapOrNull(
      addressList: (value) async {
        getIt<AppNotifier>().showLoading();
        final List<AddressModel> addresses = value.addresses ?? [];
        final newSelectedAddress =
            addresses.firstWhere((a) => a.id == addressId);

        AppPreferences.setSelectedAddress(newSelectedAddress);

        final typesenseService = getIt<TypesenseService>();
        await typesenseService.reinitializeWithCoordinates(
          newSelectedAddress.latitude?.toDouble() ?? 0,
          newSelectedAddress.longitude?.toDouble() ?? 0,
        );

        bool isServiceable = typesenseService.isLocationServiceable;

        String duration = await _onFetchDuration();
        getIt<AppNotifier>().hideLoading();

        add(AddressEvent.emitAsyncState(value.copyWith(
          selectedAddress: newSelectedAddress,
          isServicable: isServiceable,
          delliveryDuration: duration,
        )));
      },
      mapSelection: (value) async {
        getIt<AppNotifier>().showLoading();

        AddressModel address =
            value.temporaryAddress ?? value.currentLocation ?? AddressModel();

        AppPreferences.setSelectedAddress(address);

        final typesenseService = getIt<TypesenseService>();
        await typesenseService.reinitializeWithCoordinates(
          address.latitude?.toDouble() ?? 0,
          address.longitude?.toDouble() ?? 0,
        );
        bool isServiceable = typesenseService.isLocationServiceable;

        String duration = await _onFetchDuration();
        getIt<AppNotifier>().hideLoading();

        add(AddressEvent.emitAsyncState(value.copyWith(
          selectedAddress: address,
          temporaryAddress: address,
          isServicable: isServiceable,
          delliveryDuration: duration,
        )));
      },
    );
  }

  Future<String> _onFetchDuration() async {
    try {
      final typesenseService = getIt<TypesenseService>();
      final storeLat = typesenseService.storeLatitude;
      final storeLng = typesenseService.storeLongitude;

      AddressModel? destAddress = state.selectedAddress;
      double? destLat;
      double? destLng;

      destLat = (destAddress?.latitude as double?);
      destLng = (destAddress?.longitude as double?);

      if (storeLat == null ||
          storeLng == null ||
          destLat == null ||
          destLng == null) {
        return '';
      }

      final googleApiClient = GoogleApiClient();
      final originalDurationText = await googleApiClient.getDuration(
        originLat: storeLat,
        originLng: storeLng,
        destLat: destLat,
        destLng: destLng,
      );
      return AddressUtils.addExtraTimeToDelivery(originalDurationText);
    } catch (e) {
      debugPrint('LocationBloc: Failed to fetch duration: $e');
      return '';
    }
  }

  Future<AddressModel?>? _getEnhancedAddress(AddressModel? address) async {
    String? addressString = address?.fullAddress;
    double? currentLatitude = address?.latitude?.toDouble();
    double? currentLongitude = address?.longitude?.toDouble();
    AddressModel? enhancedAddress = address;
    if (kIsWeb) {
      addressString = await _googleApiClient.getAddressFromLatLngForWeb(
        currentLatitude ?? 0,
        currentLongitude ?? 0,
      );

      List<dynamic>? placemarks =
          await _googleApiClient.getPlacemarkFromCoordinatesForWeb(
        latitude: currentLatitude ?? 0,
        longitude: currentLongitude ?? 0,
      );

      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        enhancedAddress = AddressModel(
          id: address?.id,
          fullAddress: addressString ?? '',
          addressLine1: placemark['street'] ?? '',
          city: placemark['locality'] ?? '',
          state: placemark['administrativeArea'] ?? '',
          pincode: placemark['postalCode'] ?? '',
          latitude: currentLatitude!,
          longitude: currentLongitude!,
          addressType: address?.addressType ?? 'home',
          isDefault: address?.isDefault ?? false,
          name: address?.name,
          phone: address?.phone,
        );
      }
    } else {
      addressString = await _googlePlacesService.getEnhancedAddress(
        currentLatitude!,
        currentLongitude!,
      );

      List<dynamic>? placemarks = await placemarkFromCoordinates(
        currentLatitude,
        currentLongitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        enhancedAddress = AddressModel(
          id: address?.id,
          fullAddress: addressString,
          addressLine1: placemark.street ?? '',
          city: placemark.locality ?? '',
          state: placemark.administrativeArea ?? '',
          pincode: placemark.postalCode ?? '',
          latitude: currentLatitude,
          longitude: currentLongitude,
          addressType: address?.addressType ?? 'home',
          isDefault: address?.isDefault ?? false,
          name: address?.name,
          phone: address?.phone,
        );
      }
    }
    return enhancedAddress;
  }
}
