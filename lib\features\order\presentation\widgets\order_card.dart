import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:rozana/core/utils/color_utils.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/widgets/app_card.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/lazy_loading_widget.dart';
import 'package:rozana/core/services/remote_config_service.dart';

final remoteConfig = RemoteConfigService();

class OrderStatusHelper {
  static String _getNormalizedKey(String status) {
    return status.toLowerCase().replaceAll(' ', '_');
  }

  static Map<String, dynamic>? _getStatusConfig(String status) {
    final config = remoteConfig.orderStatusConfig;
    if (config.isEmpty) return null;

    final statusKey = _getNormalizedKey(status);
    if (config.containsKey(statusKey) && config[statusKey] is Map) {
      return config[statusKey];
    }

    return null;
  }

  static T? _getStatusProperty<T>(
      String status, String property, T defaultValue) {
    final statusConfig = _getStatusConfig(status);
    if (statusConfig == null || !statusConfig.containsKey(property)) {
      return defaultValue;
    }

    final value = statusConfig[property];
    return (value is T) ? value : defaultValue;
  }

  static Color getColor(String status) {
    final defaultColor = AppColors.primary500;
    final colorHex = _getStatusProperty<String?>(status, 'color', null);
    return colorHex != null
        ? (ColorUtils.hexToColor(colorHex) ?? defaultColor)
        : defaultColor;
  }

  static String getIcon(String status) {
    return _getStatusProperty<String>(status, 'icon', 'moped_package') ??
        'moped_package';
  }

  static String getDisplayText(String status) {
    return _getStatusProperty<String>(status, 'displayText', status) ?? status;
  }

  static bool canReorder(String status) {
    return _getStatusProperty<bool>(status, 're_order', false) ?? false;
  }
}

String _getIconAsset(String iconName) {
  final Map<String, String> iconsMap = {
    'moped_package': 'assets/new/icons/moped_package.png',
    'check': 'assets/new/icons/check.png',
    'close': 'assets/new/icons/close.png',
    'undo': 'assets/new/icons/undo.png',
  };

  return iconsMap[iconName.toLowerCase()] ?? 'assets/new/icons/moped_package.png';
}

class OrderCard extends StatelessWidget {
  final OrderEntity order;
  final VoidCallback? onTap;
  final VoidCallback? onOrderAgain;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onOrderAgain,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(0),
      backgroundColor: AppColors.neutral100,
      borderRadius: 12,
      elevation: 2,
      onTap: onTap,
      child: Column(
        children: [
          if (order.historyItems.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: _OrderItemImages(historyItems: order.historyItems),
            )
          else
            const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _OrderStatusInfo(
              order: order,
            ),
          ),
          const SizedBox(height: 8),
          Divider(
            thickness: 0.5,
            color: AppColors.neutral150,
            height: 0.5,
          ),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: _OrderActionButton(
              order: order,
              onTap: onTap,
              onOrderAgain: onOrderAgain,
            ),
          ),
        ],
      ),
    );
  }
}

class _OrderItemImages extends StatelessWidget {
  final List<Map<String, dynamic>> historyItems;

  const _OrderItemImages({required this.historyItems});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> itemsWithQuantity = historyItems
        .where((item) =>
            item['thumbnail_url'] != null &&
            (item['thumbnail_url'] as String).isNotEmpty)
        .map((item) => {
              'url': item['thumbnail_url'] as String,
              'quantity': item['quantity'] ?? 1,
            })
        .toList();

    return GridLazyLoadingWidget<Map<String, dynamic>>(
      items: itemsWithQuantity,
      isLoading: false,
      hasMoreData: false,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        crossAxisSpacing: 6,
        mainAxisSpacing: 20,
        childAspectRatio: 1,
      ),
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemBuilder: (context, item, index) {
        return Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: CustomImage(
                imageUrl: item['url'],
                width: 56.sp,
                height: 56.sp,
                fit: BoxFit.cover,
              ),
            ),
            if (item['quantity'] > 1)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 18.sp,
                  height: 18.sp,
                  decoration: BoxDecoration(
                    color: AppColors.neutral150,
                    shape: BoxShape.circle,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '${item['quantity']}',
                    style: TextStyle(
                      color: AppColors.neutral800,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class DateTimeHelper {
  static String formatDateTime(dynamic input) {
    try {
      DateTime dateTime;

      if (input is String) {
        dateTime = DateTime.parse(input).toLocal();
      } else if (input is DateTime) {
        dateTime = input.toLocal();
      } else {
        throw ArgumentError("Unsupported type: ${input.runtimeType}");
      }

      return DateFormat("dd MMM yyyy, hh:mm a").format(dateTime);
    } catch (e) {
      return input.toString(); // fallback
    }
  }
}

class _OrderStatusInfo extends StatelessWidget {
  final OrderEntity order;

  const _OrderStatusInfo({required this.order});

  @override
  Widget build(BuildContext context) {
    final status = order.status;

    final Color statusColor = OrderStatusHelper.getColor(status);
    final String statusIcon = OrderStatusHelper.getIcon(status);
    final String displayText = OrderStatusHelper.getDisplayText(status);

    final isDeliveredOrCancelled = status.toLowerCase().contains('delivered') ||
        status.toLowerCase().contains('cancelled') ||
        status.toLowerCase().contains('refund');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomText(
              displayText,
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: AppColors.neutral700,
            ),
            const SizedBox(width: 6),
            CircleAvatar(
              radius: 10,
              backgroundColor: statusColor,
              child: CustomImage(
                imageUrl: _getIconAsset(statusIcon),
                width: 15,
                height: 15,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: CustomText(
                isDeliveredOrCancelled
                    ? 'Placed at ${DateTimeHelper.formatDateTime(order.orderDate)}'
                    : 'Delivery in ${order.estimatedDeliveryTime != null ? DateTimeHelper.formatDateTime(order.estimatedDeliveryTime!) : 'Next Day'}',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.neutral400,
              ),
            ),
            _OrderTotal(amount: order.totalAmount),
          ],
        ),
      ],
    );
  }
}

class _OrderTotal extends StatelessWidget {
  final double amount;

  const _OrderTotal({required this.amount});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        CustomText(
          '₹${amount.toStringAsFixed(1)}',
          fontSize: 16,
          fontWeight: FontWeight.w700,
          color: AppColors.primary,
        ),
        const SizedBox(width: 2),
        Image.asset(
          'assets/new/icons/chevron_right.png',
          height: 20,
          width: 20,
        ),
      ],
    );
  }
}

class _OrderActionButton extends StatefulWidget {
  final OrderEntity order;
  final VoidCallback? onTap;
  final VoidCallback? onOrderAgain;

  const _OrderActionButton({
    required this.order,
    this.onTap,
    this.onOrderAgain,
  });

  @override
  State<_OrderActionButton> createState() => _OrderActionButtonState();
}

class _OrderActionButtonState extends State<_OrderActionButton> {
  bool _isLoading = false;

  void _setLoading(bool loading) {
    if (mounted) {
      setState(() {
        _isLoading = loading;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final status = widget.order.status;
    final bool canReorderFlag = OrderStatusHelper.canReorder(status);
    final String buttonText =
        canReorderFlag && widget.order.historyItems.isNotEmpty
            ? 'Order again'
            : canReorderFlag
                ? 'Order details'
                : 'Track order';

    // Create a wrapped callback that handles loading state
    VoidCallback? wrappedCallback;
    if (canReorderFlag &&
        widget.onOrderAgain != null &&
        widget.order.historyItems.isNotEmpty) {
      wrappedCallback = _isLoading
          ? null
          : () {
              _setLoading(true);
              try {
                widget.onOrderAgain!();
              } catch (e) {
                _setLoading(false);
              }
            };
    } else if (widget.onTap != null) {
      wrappedCallback = widget.onTap;
    }

    return InkWell(
      onTap: wrappedCallback,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: canReorderFlag
              ? Border.all(color: AppColors.primary, width: 1.5)
              : null,
        ),
        alignment: Alignment.center,
        child: _isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    canReorderFlag ? AppColors.primary : AppColors.neutral800,
                  ),
                ),
              )
            : CustomText(
                buttonText,
                color: AppColors.primary500,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
      ),
    );
  }
}
