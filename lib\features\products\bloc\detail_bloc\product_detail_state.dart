import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/product_entity.dart';

part 'product_detail_state.freezed.dart';

@freezed
class ProductDetailState with _$ProductDetailState {
  const factory ProductDetailState.loading() = _Loading;
  const factory ProductDetailState.loaded({
    required ProductEntity product,
    required ProductEntity selectedVariant,
    @Default([]) List<ProductEntity> similarProducts,
    @Default(false) bool isSimilarProductsLoading,
  }) = _Loaded;

  const factory ProductDetailState.productError(String message) = _ProductError;
}
