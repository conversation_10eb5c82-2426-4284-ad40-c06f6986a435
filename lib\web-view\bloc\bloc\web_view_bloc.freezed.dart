// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'web_view_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WebViewEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is WebViewEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewEvent()';
  }
}

/// @nodoc
class $WebViewEventCopyWith<$Res> {
  $WebViewEventCopyWith(WebViewEvent _, $Res Function(WebViewEvent) __);
}

/// Adds pattern-matching-related methods to [WebViewEvent].
extension WebViewEventPatterns on WebViewEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started(_that);
      case _ContentLoaded() when contentLoaded != null:
        return contentLoaded(_that);
      case _ContentFailed() when contentFailed != null:
        return contentFailed(_that);
      case _SwitchToWebView() when switchToWebView != null:
        return switchToWebView(_that);
      case _SwitchToNativeView() when switchToNativeView != null:
        return switchToNativeView(_that);
      case _CheckScreenWidth() when checkScreenWidth != null:
        return checkScreenWidth(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    final _that = this;
    switch (_that) {
      case _Started():
        return started(_that);
      case _ContentLoaded():
        return contentLoaded(_that);
      case _ContentFailed():
        return contentFailed(_that);
      case _SwitchToWebView():
        return switchToWebView(_that);
      case _SwitchToNativeView():
        return switchToNativeView(_that);
      case _CheckScreenWidth():
        return checkScreenWidth(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started(_that);
      case _ContentLoaded() when contentLoaded != null:
        return contentLoaded(_that);
      case _ContentFailed() when contentFailed != null:
        return contentFailed(_that);
      case _SwitchToWebView() when switchToWebView != null:
        return switchToWebView(_that);
      case _SwitchToNativeView() when switchToNativeView != null:
        return switchToNativeView(_that);
      case _CheckScreenWidth() when checkScreenWidth != null:
        return checkScreenWidth(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started();
      case _ContentLoaded() when contentLoaded != null:
        return contentLoaded();
      case _ContentFailed() when contentFailed != null:
        return contentFailed(_that.error);
      case _SwitchToWebView() when switchToWebView != null:
        return switchToWebView();
      case _SwitchToNativeView() when switchToNativeView != null:
        return switchToNativeView();
      case _CheckScreenWidth() when checkScreenWidth != null:
        return checkScreenWidth(_that.width);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    final _that = this;
    switch (_that) {
      case _Started():
        return started();
      case _ContentLoaded():
        return contentLoaded();
      case _ContentFailed():
        return contentFailed(_that.error);
      case _SwitchToWebView():
        return switchToWebView();
      case _SwitchToNativeView():
        return switchToNativeView();
      case _CheckScreenWidth():
        return checkScreenWidth(_that.width);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    final _that = this;
    switch (_that) {
      case _Started() when started != null:
        return started();
      case _ContentLoaded() when contentLoaded != null:
        return contentLoaded();
      case _ContentFailed() when contentFailed != null:
        return contentFailed(_that.error);
      case _SwitchToWebView() when switchToWebView != null:
        return switchToWebView();
      case _SwitchToNativeView() when switchToNativeView != null:
        return switchToNativeView();
      case _CheckScreenWidth() when checkScreenWidth != null:
        return checkScreenWidth(_that.width);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Started implements WebViewEvent {
  const _Started();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Started);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewEvent.started()';
  }
}

/// @nodoc

class _ContentLoaded implements WebViewEvent {
  const _ContentLoaded();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ContentLoaded);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewEvent.contentLoaded()';
  }
}

/// @nodoc

class _ContentFailed implements WebViewEvent {
  const _ContentFailed(this.error);

  final String error;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContentFailedCopyWith<_ContentFailed> get copyWith =>
      __$ContentFailedCopyWithImpl<_ContentFailed>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContentFailed &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'WebViewEvent.contentFailed(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$ContentFailedCopyWith<$Res>
    implements $WebViewEventCopyWith<$Res> {
  factory _$ContentFailedCopyWith(
          _ContentFailed value, $Res Function(_ContentFailed) _then) =
      __$ContentFailedCopyWithImpl;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$ContentFailedCopyWithImpl<$Res>
    implements _$ContentFailedCopyWith<$Res> {
  __$ContentFailedCopyWithImpl(this._self, this._then);

  final _ContentFailed _self;
  final $Res Function(_ContentFailed) _then;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = null,
  }) {
    return _then(_ContentFailed(
      null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SwitchToWebView implements WebViewEvent {
  const _SwitchToWebView();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SwitchToWebView);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewEvent.switchToWebView()';
  }
}

/// @nodoc

class _SwitchToNativeView implements WebViewEvent {
  const _SwitchToNativeView();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SwitchToNativeView);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewEvent.switchToNativeView()';
  }
}

/// @nodoc

class _CheckScreenWidth implements WebViewEvent {
  const _CheckScreenWidth(this.width);

  final double width;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CheckScreenWidthCopyWith<_CheckScreenWidth> get copyWith =>
      __$CheckScreenWidthCopyWithImpl<_CheckScreenWidth>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CheckScreenWidth &&
            (identical(other.width, width) || other.width == width));
  }

  @override
  int get hashCode => Object.hash(runtimeType, width);

  @override
  String toString() {
    return 'WebViewEvent.checkScreenWidth(width: $width)';
  }
}

/// @nodoc
abstract mixin class _$CheckScreenWidthCopyWith<$Res>
    implements $WebViewEventCopyWith<$Res> {
  factory _$CheckScreenWidthCopyWith(
          _CheckScreenWidth value, $Res Function(_CheckScreenWidth) _then) =
      __$CheckScreenWidthCopyWithImpl;
  @useResult
  $Res call({double width});
}

/// @nodoc
class __$CheckScreenWidthCopyWithImpl<$Res>
    implements _$CheckScreenWidthCopyWith<$Res> {
  __$CheckScreenWidthCopyWithImpl(this._self, this._then);

  final _CheckScreenWidth _self;
  final $Res Function(_CheckScreenWidth) _then;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? width = null,
  }) {
    return _then(_CheckScreenWidth(
      null == width
          ? _self.width
          : width // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
mixin _$WebViewState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is WebViewState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewState()';
  }
}

/// @nodoc
class $WebViewStateCopyWith<$Res> {
  $WebViewStateCopyWith(WebViewState _, $Res Function(WebViewState) __);
}

/// Adds pattern-matching-related methods to [WebViewState].
extension WebViewStatePatterns on WebViewState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Error() when error != null:
        return error(_that);
      case _WebViewMode() when webViewMode != null:
        return webViewMode(_that);
      case _NativeViewMode() when nativeViewMode != null:
        return nativeViewMode(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Loaded():
        return loaded(_that);
      case _Error():
        return error(_that);
      case _WebViewMode():
        return webViewMode(_that);
      case _NativeViewMode():
        return nativeViewMode(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Error() when error != null:
        return error(_that);
      case _WebViewMode() when webViewMode != null:
        return webViewMode(_that);
      case _NativeViewMode() when nativeViewMode != null:
        return nativeViewMode(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Loaded() when loaded != null:
        return loaded();
      case _Error() when error != null:
        return error(_that.message);
      case _WebViewMode() when webViewMode != null:
        return webViewMode();
      case _NativeViewMode() when nativeViewMode != null:
        return nativeViewMode();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _Loaded():
        return loaded();
      case _Error():
        return error(_that.message);
      case _WebViewMode():
        return webViewMode();
      case _NativeViewMode():
        return nativeViewMode();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Loaded() when loaded != null:
        return loaded();
      case _Error() when error != null:
        return error(_that.message);
      case _WebViewMode() when webViewMode != null:
        return webViewMode();
      case _NativeViewMode() when nativeViewMode != null:
        return nativeViewMode();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements WebViewState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewState.initial()';
  }
}

/// @nodoc

class _Loading implements WebViewState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewState.loading()';
  }
}

/// @nodoc

class _Loaded implements WebViewState {
  const _Loaded();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loaded);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewState.loaded()';
  }
}

/// @nodoc

class _Error implements WebViewState {
  const _Error(this.message);

  final String message;

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ErrorCopyWith<_Error> get copyWith =>
      __$ErrorCopyWithImpl<_Error>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Error &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'WebViewState.error(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res>
    implements $WebViewStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) =
      __$ErrorCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$ErrorCopyWithImpl<$Res> implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_Error(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _WebViewMode implements WebViewState {
  const _WebViewMode();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WebViewMode);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewState.webViewMode()';
  }
}

/// @nodoc

class _NativeViewMode implements WebViewState {
  const _NativeViewMode();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _NativeViewMode);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'WebViewState.nativeViewMode()';
  }
}

// dart format on
