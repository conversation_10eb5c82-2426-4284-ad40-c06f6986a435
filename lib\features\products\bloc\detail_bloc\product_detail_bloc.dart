export 'product_detail_event.dart';
export 'product_detail_state.dart';

import 'package:bloc/bloc.dart';
import 'package:rozana/domain/entities/product_entity.dart';

import '../../../../core/network/api_client.dart';
import '../../../../core/network/api_endpoints.dart';
import '../../../../core/network/service_api_client.dart';
import '../../../../core/utils/logger.dart';
import '../../../../data/services/data_loading_manager.dart';
import 'product_detail_event.dart';
import 'product_detail_state.dart';

class ProductDetailBloc extends Bloc<ProductDetailEvent, ProductDetailState> {
  final DataLoadingManager _dataLoadingManager;
  ProductDetailBloc({required DataLoadingManager dataLoadingManager})
      : _dataLoadingManager = dataLoadingManager,
        super(ProductDetailState.loading()) {
    on<ProductDetailEvent>((event, emit) => event.map(
          fetchProduct: (e) async => await _onFetchProduct(
              e.sku, e.variantName, e.validateQuantity, emit),
          switchVariant: (e) async => await _onSwitcVariant(e.variant, emit),
          fetchSimilarProducts: (e) async => await _onFetchSimilarProducts(
              e.categoryId, e.excludeProductId, emit),
          loadMoreSimilarProducts: (e) async =>
              await _onLoadMoreSimilarProducts(
                  e.categoryId, e.excludeProductId, emit),
          checkUomQuantity: (e) => _onCheckUOMAvailability(emit,
              whSku: e.whSku,
              uom: e.uoms,
              facilityId: e.facilityId,
              parentEntity: e.parentEntity,
              selectedVariant: e.selectedVariant),
        ));
  }

  Future<void> _onSwitcVariant(
    ProductEntity variant,
    Emitter<ProductDetailState> emit,
  ) async {
    state.mapOrNull(
      loaded: (value) {
        emit(value.copyWith(selectedVariant: variant));
      },
    );
  }

  Future<void> _onFetchSimilarProducts(
    String categoryId,
    String excludeProductId,
    Emitter<ProductDetailState> emit,
  ) async {
    try {
      // Only fetch if we're in loaded state
      await state.maybeWhen(
        loaded: (product,
            selectedVariant,
            similarProducts,
            isSimilarProductsLoading,
            hasMoreSimilarProducts,
            similarProductsPage) async {
          // Set loading state for similar products
          emit(ProductDetailState.loaded(
            product: product,
            selectedVariant: selectedVariant,
            similarProducts: similarProducts,
            isSimilarProductsLoading: true,
            hasMoreSimilarProducts: hasMoreSimilarProducts,
            similarProductsPage: similarProductsPage,
          ));

          // Fetch similar products using DataLoadingManager (first page)
          final fetchedProducts = await _dataLoadingManager.loadProducts(
            categoryId: categoryId,
            pageSize: 10,
            page: 0,
          );

          // Filter out the current product
          final filteredProducts = fetchedProducts
              .where((product) => product.id != excludeProductId)
              .toList();

          // Update state with similar products
          emit(ProductDetailState.loaded(
            product: product,
            selectedVariant: selectedVariant,
            similarProducts: filteredProducts,
            isSimilarProductsLoading: false,
            hasMoreSimilarProducts: fetchedProducts.length >= 10,
            similarProductsPage: 0,
          ));
        },
        orElse: () async {
          // Do nothing if not in loaded state
        },
      );
    } catch (e) {
      // On error, just set loading to false and keep empty list
      await state.maybeWhen(
        loaded: (product,
            selectedVariant,
            similarProducts,
            isSimilarProductsLoading,
            hasMoreSimilarProducts,
            similarProductsPage) async {
          emit(ProductDetailState.loaded(
            product: product,
            selectedVariant: selectedVariant,
            similarProducts: [],
            isSimilarProductsLoading: false,
            hasMoreSimilarProducts: false,
            similarProductsPage: 0,
          ));
        },
        orElse: () async {
          // Do nothing if not in loaded state
        },
      );
    }
  }

  Future<void> _onLoadMoreSimilarProducts(
    String categoryId,
    String excludeProductId,
    Emitter<ProductDetailState> emit,
  ) async {
    try {
      // Only fetch if we're in loaded state and have more products
      await state.maybeWhen(
        loaded: (product,
            selectedVariant,
            similarProducts,
            isSimilarProductsLoading,
            hasMoreSimilarProducts,
            similarProductsPage) async {
          // Don't load if already loading or no more products
          if (isSimilarProductsLoading || !hasMoreSimilarProducts) return;

          // Set loading state for pagination
          emit(ProductDetailState.loaded(
            product: product,
            selectedVariant: selectedVariant,
            similarProducts: similarProducts,
            isSimilarProductsLoading: true,
            hasMoreSimilarProducts: hasMoreSimilarProducts,
            similarProductsPage: similarProductsPage,
          ));

          // Fetch next page of similar products
          final nextPage = similarProductsPage + 1;
          final fetchedProducts = await _dataLoadingManager.loadProducts(
            categoryId: categoryId,
            pageSize: 10,
            page: nextPage,
          );

          // Filter out the current product
          final filteredProducts = fetchedProducts
              .where((product) => product.id != excludeProductId)
              .toList();

          // Append to existing similar products
          final updatedSimilarProducts = [
            ...similarProducts,
            ...filteredProducts
          ];

          // Update state with more similar products
          emit(ProductDetailState.loaded(
            product: product,
            selectedVariant: selectedVariant,
            similarProducts: updatedSimilarProducts,
            isSimilarProductsLoading: false,
            hasMoreSimilarProducts: fetchedProducts.length >= 10,
            similarProductsPage: nextPage,
          ));
        },
        orElse: () async {
          // Do nothing if not in loaded state
        },
      );
    } catch (e) {
      // On error, just set loading to false
      await state.maybeWhen(
        loaded: (product,
            selectedVariant,
            similarProducts,
            isSimilarProductsLoading,
            hasMoreSimilarProducts,
            similarProductsPage) async {
          emit(ProductDetailState.loaded(
            product: product,
            selectedVariant: selectedVariant,
            similarProducts: similarProducts,
            isSimilarProductsLoading: false,
            hasMoreSimilarProducts: hasMoreSimilarProducts,
            similarProductsPage: similarProductsPage,
          ));
        },
        orElse: () async {
          // Do nothing if not in loaded state
        },
      );
    }
  }

  Future<void> _onFetchProduct(
    String sku,
    String variantName,
    bool validateQuantity,
    Emitter<ProductDetailState> emit,
  ) async {
    try {
      // Emit loading state
      final productEntities = await _dataLoadingManager.loadProducts(
        query: '*',
        page: 0,
        pageSize: 1,
        specificSku: sku,
        validateQuantity: validateQuantity,
      );

      if (productEntities.isNotEmpty) {
        ProductEntity selectedVariant = productEntities.first;
        String whSku = selectedVariant.whSku ?? '';
        List<num?> uoms = [];
        if (selectedVariant.variants?.isEmpty ?? true) {
          uoms = [selectedVariant.packUomQuantity];
        } else {
          uoms = selectedVariant.variants
                  ?.map((e) => e.packUomQuantity)
                  .toList() ??
              [];
        }

        if ((variantName.isNotEmpty) &&
            (productEntities.first.variants?.isNotEmpty ?? false)) {
          selectedVariant = (productEntities.first.variants ?? []).firstWhere(
              (e) => e.skuID?.split('-').last == variantName,
              orElse: () =>
                  productEntities.first.variants?.first ??
                  productEntities.first);
        }

        add(ProductDetailEvent.checkUomQuantity(
            uoms: uoms,
            whSku: whSku,
            facilityId: selectedVariant.facilityId ?? '',
            parentEntity: productEntities.first,
            selectedVariant: selectedVariant));
      } else {
        emit(ProductDetailState.productError('Product not found'));
      }
    } catch (e) {
      // Emit error state
      emit(ProductDetailState.productError(e.toString()));
    }
  }

  Future<dynamic> _onCheckUOMAvailability(Emitter<ProductDetailState> emit,
      {required String whSku,
      required List<num?> uom,
      required String facilityId,
      required ProductEntity parentEntity,
      required ProductEntity selectedVariant}) async {
    try {
      var response = await ServiceApiClient.sendImsRequest(
          endUrl: EndUrl.getUOMStockAvailability,
          method: HttpMethod.get,
          queryParameters: {
            "facility": facilityId,
            whSku: (uom).join(','),
          });

      Map dataList = response?.data['data']?[whSku] ?? {};

      final Map<String, num> data = dataList.map((key, value) {
        final calculatedQty = value["calculated_qty"] as num;
        return MapEntry(key, calculatedQty);
      });

      selectedVariant = selectedVariant.copyWith(
          availableQty:
              (data[selectedVariant.packUomQuantity?.toString()] ?? 0).toInt());

      List<ProductEntity>? variants = (parentEntity.variants ?? [])
          .map((e) => (e.copyWith(
              availableQty:
                  (data[e.packUomQuantity?.toString()] ?? 0).toInt())))
          .toList();

      parentEntity = parentEntity.copyWith(variants: variants);

      state.maybeMap(
        loaded: (value) {
          emit(value.copyWith(
            product: parentEntity,
            selectedVariant: selectedVariant,
          ));
        },
        orElse: () {
          emit(ProductDetailState.loaded(
            product: parentEntity,
            selectedVariant: selectedVariant,
          ));
        },
      );

      // Trigger similar products fetching if categoryId is available
      if (parentEntity.categoryId?.isNotEmpty == true) {
        add(ProductDetailEvent.fetchSimilarProducts(
          categoryId: parentEntity.categoryId!,
          excludeProductId: parentEntity.id,
        ));
      }
    } catch (e) {
      LogMessage.p("Failed to check uom availability => $e");
    }
  }
}
