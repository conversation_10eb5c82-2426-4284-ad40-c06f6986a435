// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SearchState {
  String get query;
  bool get isLoading;
  bool get hasMore;
  int get page;
  List<Map<String, dynamic>> get products;
  List<Map<String, dynamic>> get categories;
  List<Map<String, dynamic>> get subcategories;
  List<String> get recentSearches;
  List<Map<String, dynamic>> get suggestions;
  String? get error;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchStateCopyWith<SearchState> get copyWith =>
      _$SearchStateCopyWithImpl<SearchState>(this as SearchState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchState &&
            (identical(other.query, query) || other.query == query) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.page, page) || other.page == page) &&
            const DeepCollectionEquality().equals(other.products, products) &&
            const DeepCollectionEquality()
                .equals(other.categories, categories) &&
            const DeepCollectionEquality()
                .equals(other.subcategories, subcategories) &&
            const DeepCollectionEquality()
                .equals(other.recentSearches, recentSearches) &&
            const DeepCollectionEquality()
                .equals(other.suggestions, suggestions) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      query,
      isLoading,
      hasMore,
      page,
      const DeepCollectionEquality().hash(products),
      const DeepCollectionEquality().hash(categories),
      const DeepCollectionEquality().hash(subcategories),
      const DeepCollectionEquality().hash(recentSearches),
      const DeepCollectionEquality().hash(suggestions),
      error);

  @override
  String toString() {
    return 'SearchState(query: $query, isLoading: $isLoading, hasMore: $hasMore, page: $page, products: $products, categories: $categories, subcategories: $subcategories, recentSearches: $recentSearches, suggestions: $suggestions, error: $error)';
  }
}

/// @nodoc
abstract mixin class $SearchStateCopyWith<$Res> {
  factory $SearchStateCopyWith(
          SearchState value, $Res Function(SearchState) _then) =
      _$SearchStateCopyWithImpl;
  @useResult
  $Res call(
      {String query,
      bool isLoading,
      bool hasMore,
      int page,
      List<Map<String, dynamic>> products,
      List<Map<String, dynamic>> categories,
      List<Map<String, dynamic>> subcategories,
      List<String> recentSearches,
      List<Map<String, dynamic>> suggestions,
      String? error});
}

/// @nodoc
class _$SearchStateCopyWithImpl<$Res> implements $SearchStateCopyWith<$Res> {
  _$SearchStateCopyWithImpl(this._self, this._then);

  final SearchState _self;
  final $Res Function(SearchState) _then;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
    Object? isLoading = null,
    Object? hasMore = null,
    Object? page = null,
    Object? products = null,
    Object? categories = null,
    Object? subcategories = null,
    Object? recentSearches = null,
    Object? suggestions = null,
    Object? error = freezed,
  }) {
    return _then(_self.copyWith(
      query: null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      products: null == products
          ? _self.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      categories: null == categories
          ? _self.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      subcategories: null == subcategories
          ? _self.subcategories
          : subcategories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      recentSearches: null == recentSearches
          ? _self.recentSearches
          : recentSearches // ignore: cast_nullable_to_non_nullable
              as List<String>,
      suggestions: null == suggestions
          ? _self.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SearchState].
extension SearchStatePatterns on SearchState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SearchState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SearchState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SearchState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SearchState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String query,
            bool isLoading,
            bool hasMore,
            int page,
            List<Map<String, dynamic>> products,
            List<Map<String, dynamic>> categories,
            List<Map<String, dynamic>> subcategories,
            List<String> recentSearches,
            List<Map<String, dynamic>> suggestions,
            String? error)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SearchState() when $default != null:
        return $default(
            _that.query,
            _that.isLoading,
            _that.hasMore,
            _that.page,
            _that.products,
            _that.categories,
            _that.subcategories,
            _that.recentSearches,
            _that.suggestions,
            _that.error);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String query,
            bool isLoading,
            bool hasMore,
            int page,
            List<Map<String, dynamic>> products,
            List<Map<String, dynamic>> categories,
            List<Map<String, dynamic>> subcategories,
            List<String> recentSearches,
            List<Map<String, dynamic>> suggestions,
            String? error)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchState():
        return $default(
            _that.query,
            _that.isLoading,
            _that.hasMore,
            _that.page,
            _that.products,
            _that.categories,
            _that.subcategories,
            _that.recentSearches,
            _that.suggestions,
            _that.error);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String query,
            bool isLoading,
            bool hasMore,
            int page,
            List<Map<String, dynamic>> products,
            List<Map<String, dynamic>> categories,
            List<Map<String, dynamic>> subcategories,
            List<String> recentSearches,
            List<Map<String, dynamic>> suggestions,
            String? error)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SearchState() when $default != null:
        return $default(
            _that.query,
            _that.isLoading,
            _that.hasMore,
            _that.page,
            _that.products,
            _that.categories,
            _that.subcategories,
            _that.recentSearches,
            _that.suggestions,
            _that.error);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _SearchState implements SearchState {
  const _SearchState(
      {required this.query,
      required this.isLoading,
      required this.hasMore,
      required this.page,
      required final List<Map<String, dynamic>> products,
      required final List<Map<String, dynamic>> categories,
      required final List<Map<String, dynamic>> subcategories,
      required final List<String> recentSearches,
      required final List<Map<String, dynamic>> suggestions,
      this.error})
      : _products = products,
        _categories = categories,
        _subcategories = subcategories,
        _recentSearches = recentSearches,
        _suggestions = suggestions;

  @override
  final String query;
  @override
  final bool isLoading;
  @override
  final bool hasMore;
  @override
  final int page;
  final List<Map<String, dynamic>> _products;
  @override
  List<Map<String, dynamic>> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final List<Map<String, dynamic>> _categories;
  @override
  List<Map<String, dynamic>> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<Map<String, dynamic>> _subcategories;
  @override
  List<Map<String, dynamic>> get subcategories {
    if (_subcategories is EqualUnmodifiableListView) return _subcategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subcategories);
  }

  final List<String> _recentSearches;
  @override
  List<String> get recentSearches {
    if (_recentSearches is EqualUnmodifiableListView) return _recentSearches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentSearches);
  }

  final List<Map<String, dynamic>> _suggestions;
  @override
  List<Map<String, dynamic>> get suggestions {
    if (_suggestions is EqualUnmodifiableListView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestions);
  }

  @override
  final String? error;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchStateCopyWith<_SearchState> get copyWith =>
      __$SearchStateCopyWithImpl<_SearchState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchState &&
            (identical(other.query, query) || other.query == query) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.page, page) || other.page == page) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality()
                .equals(other._subcategories, _subcategories) &&
            const DeepCollectionEquality()
                .equals(other._recentSearches, _recentSearches) &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      query,
      isLoading,
      hasMore,
      page,
      const DeepCollectionEquality().hash(_products),
      const DeepCollectionEquality().hash(_categories),
      const DeepCollectionEquality().hash(_subcategories),
      const DeepCollectionEquality().hash(_recentSearches),
      const DeepCollectionEquality().hash(_suggestions),
      error);

  @override
  String toString() {
    return 'SearchState(query: $query, isLoading: $isLoading, hasMore: $hasMore, page: $page, products: $products, categories: $categories, subcategories: $subcategories, recentSearches: $recentSearches, suggestions: $suggestions, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$SearchStateCopyWith<$Res>
    implements $SearchStateCopyWith<$Res> {
  factory _$SearchStateCopyWith(
          _SearchState value, $Res Function(_SearchState) _then) =
      __$SearchStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String query,
      bool isLoading,
      bool hasMore,
      int page,
      List<Map<String, dynamic>> products,
      List<Map<String, dynamic>> categories,
      List<Map<String, dynamic>> subcategories,
      List<String> recentSearches,
      List<Map<String, dynamic>> suggestions,
      String? error});
}

/// @nodoc
class __$SearchStateCopyWithImpl<$Res> implements _$SearchStateCopyWith<$Res> {
  __$SearchStateCopyWithImpl(this._self, this._then);

  final _SearchState _self;
  final $Res Function(_SearchState) _then;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
    Object? isLoading = null,
    Object? hasMore = null,
    Object? page = null,
    Object? products = null,
    Object? categories = null,
    Object? subcategories = null,
    Object? recentSearches = null,
    Object? suggestions = null,
    Object? error = freezed,
  }) {
    return _then(_SearchState(
      query: null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      products: null == products
          ? _self._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      categories: null == categories
          ? _self._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      subcategories: null == subcategories
          ? _self._subcategories
          : subcategories // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      recentSearches: null == recentSearches
          ? _self._recentSearches
          : recentSearches // ignore: cast_nullable_to_non_nullable
              as List<String>,
      suggestions: null == suggestions
          ? _self._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
