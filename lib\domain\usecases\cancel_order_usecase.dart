import '../repositories/order_repository_interface.dart';

/// Use case for cancelling orders
/// Encapsulates the business logic for order cancellation
class CancelOrderUseCase {
  final OrderRepositoryInterface _repository;

  CancelOrderUseCase(this._repository);

  /// Execute the use case to cancel an order
  ///
  /// [orderId] - ID of the order to cancel
  /// Returns true if cancellation was successful, false otherwise
  Future<bool> call(String orderId) async {
    try {
      if (orderId.isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      // First check if the order exists and can be cancelled
      final order = await _repository.getOrderDetails(orderId);
      if (order == null) {
        throw Exception('Order not found with ID: $orderId');
      }

      if (!order.canCancel) {
        throw Exception(
            'Order cannot be cancelled. Current status: ${order.status}');
      }

      return await _repository.cancelOrder(orderId);
    } catch (e) {
      // Log error and rethrow
      throw Exception('Failed to cancel order: $e');
    }
  }

  /// Validate if order can be cancelled with detailed reason
  Future<CancellationValidationResult> validateCancellation(
      String orderId) async {
    try {
      final order = await _repository.getOrderDetails(orderId);

      if (order == null) {
        return CancellationValidationResult(
          canCancel: false,
          reason: 'Order not found',
        );
      }

      if (!order.canCancel) {
        return CancellationValidationResult(
          canCancel: false,
          reason: 'Order cancellation is not allowed',
        );
      }

      return CancellationValidationResult(
        canCancel: true,
        reason: 'Order can be cancelled',
      );
    } catch (e) {
      return CancellationValidationResult(
        canCancel: false,
        reason: 'Error validating cancellation: $e',
      );
    }
  }
}

/// Result of cancellation validation
class CancellationValidationResult {
  final bool canCancel;
  final String reason;

  CancellationValidationResult({
    required this.canCancel,
    required this.reason,
  });
}
