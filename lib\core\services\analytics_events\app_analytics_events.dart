import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';

class AppAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track App Launched
  Future<void> trackAppLaunched() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logAppLaunched(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          currentState: 'app_launched',
        );
      }
    } catch (e) {
      debugPrint('Error tracking app launched: $e');
    }
  }

  /// Track App Closed
  Future<void> trackAppClosed() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logAppClosed(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          currentState: 'app_closed',
        );
      }
    } catch (e) {
      debugPrint('Error tracking app closed: $e');
    }
  }

  /// Track Language Selected
  Future<void> trackLanguageSelected(String language) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logLanguageSelected(
          mobileNumber: analyticsData['mobileNumber'],
          languageSelected: language,
        );
      }
    } catch (e) {
      debugPrint('Error tracking language selected: $e');
    }
  }

  /// Track Take Me To Home CTA
  Future<void> trackTakeMeToHome() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logTakeMeToHomeCTA(
          mobileNumber: analyticsData['mobileNumber'],
        );
      }
    } catch (e) {
      debugPrint('Error tracking take me to home: $e');
    }
  }

}
