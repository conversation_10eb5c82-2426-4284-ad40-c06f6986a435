import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'connectivity_event.dart';
import 'connectivity_state.dart';

/// A BLoC that manages and emits network connectivity states.
/// It uses the `connectivity_plus` package to listen for real-time changes
/// and provides methods to check connectivity on demand.
class ConnectivityBloc extends Bloc<ConnectivityEvent, ConnectivityState> {
  /// Subscription to the `connectivity_plus` stream to listen for network changes.
  late final StreamSubscription<List<ConnectivityResult>>
      _connectivitySubscription; // Changed to List<ConnectivityResult> for newer connectivity_plus

  /// Instance of `Connectivity` to perform network checks.
  final Connectivity _connectivity = Connectivity();

  ConnectivityBloc() : super(const ConnectivityState.initial()) {
    // Initial state now uses freezed constructor
    // Register a single event handler for the sealed union ConnectivityEvent.
    // We use event.when() to pattern match on the specific event type.
    on<ConnectivityEvent>((event, emit) async {
      // Ensure all branches of event.when return a Future<void>
      await event.when(
        checkConnectivity: () => _onCheckConnectivity(emit),
        connectivityChanged: (result) => _onConnectivityChanged(
            result, emit), // This now returns Future<void>
        retryConnection: () => _onRetryConnection(emit),
      );
    });

    // Immediately start listening to connectivity changes.
    // This stream provides continuous updates.
    // Note: connectivity_plus 5.0.0+ returns List<ConnectivityResult>
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((results) {
      // Get the first result or default to none if list is empty
      final ConnectivityResult result =
          results.isNotEmpty ? results.first : ConnectivityResult.none;
      add(ConnectivityEvent.connectivityChanged(result));
    });

    // Perform an initial check when the BLoC is created.
    add(const ConnectivityEvent.checkConnectivity());
  }

  /// Handles the [ConnectivityEvent.checkConnectivity] event.
  /// This is typically called on BLoC initialization or when a retry is requested.
  Future<void> _onCheckConnectivity(Emitter<ConnectivityState> emit) async {
    // Emit ConnectivityState.checking to indicate a check is in progress.
    emit(const ConnectivityState.checking());
    // Perform the actual connectivity check.
    final List<ConnectivityResult> results =
        await _connectivity.checkConnectivity();
    final ConnectivityResult result =
        results.isNotEmpty ? results.first : ConnectivityResult.none;
    // Emit the appropriate connected/disconnected state based on the result.
    _emitConnectivityState(emit, result);
  }

  /// Handles the [ConnectivityEvent.connectivityChanged] event, which is triggered by the
  /// `connectivity_plus` stream.
  Future<void> _onConnectivityChanged(
      ConnectivityResult result, Emitter<ConnectivityState> emit) async {
    // Emit the appropriate connected/disconnected state based on the new result.
    _emitConnectivityState(emit, result);
  }

  /// Handles the [ConnectivityEvent.retryConnection] event, typically from a "Reload" button.
  /// It re-initiates a connectivity check.
  Future<void> _onRetryConnection(Emitter<ConnectivityState> emit) async {
    // Emit ConnectivityState.checking to show loading during retry.
    emit(const ConnectivityState.checking());
    // Perform the connectivity check.
    final List<ConnectivityResult> results =
        await _connectivity.checkConnectivity();
    final ConnectivityResult result =
        results.isNotEmpty ? results.first : ConnectivityResult.none;
    // Emit the state based on the retry result.
    await Future.delayed(Duration(milliseconds: 800), () {});
    _emitConnectivityState(emit, result);
  }

  /// Helper method to determine and emit the correct network state.
  void _emitConnectivityState(
      Emitter<ConnectivityState> emit, ConnectivityResult result) {
    if (result == ConnectivityResult.none) {
      emit(const ConnectivityState.disconnected());
    } else {
      emit(const ConnectivityState.connected());
    }
  }

  @override
  Future<void> close() {
    // Cancel the connectivity subscription when the BLoC is closed
    // to prevent memory leaks.
    _connectivitySubscription.cancel();
    return super.close();
  }
}
