// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'address_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AddressState implements DiagnosticableTreeMixin {
  List<AddressModel>? get addresses;
  AddressModel? get temporaryAddress;
  AddressModel? get selectedAddress;
  AddressModel? get currentLocation;
  bool get relocateMap;
  bool get isServicable;
  String? get delliveryDuration;
  bool get applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AddressStateCopyWith<AddressState> get copyWith =>
      _$AddressStateCopyWithImpl<AddressState>(
          this as AddressState, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AddressState &&
            const DeepCollectionEquality().equals(other.addresses, addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class $AddressStateCopyWith<$Res> {
  factory $AddressStateCopyWith(
          AddressState value, $Res Function(AddressState) _then) =
      _$AddressStateCopyWithImpl;
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class _$AddressStateCopyWithImpl<$Res> implements $AddressStateCopyWith<$Res> {
  _$AddressStateCopyWithImpl(this._self, this._then);

  final AddressState _self;
  final $Res Function(AddressState) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_self.copyWith(
      addresses: freezed == addresses
          ? _self.addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [AddressState].
extension AddressStatePatterns on AddressState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddressForm value)? addressForm,
    TResult Function(_AddressList value)? addressList,
    TResult Function(_MapSelection value)? mapSelection,
    TResult Function(_MapSearch value)? searchScreen,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _AddressForm() when addressForm != null:
        return addressForm(_that);
      case _AddressList() when addressList != null:
        return addressList(_that);
      case _MapSelection() when mapSelection != null:
        return mapSelection(_that);
      case _MapSearch() when searchScreen != null:
        return searchScreen(_that);
      case _Error() when error != null:
        return error(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddressForm value) addressForm,
    required TResult Function(_AddressList value) addressList,
    required TResult Function(_MapSelection value) mapSelection,
    required TResult Function(_MapSearch value) searchScreen,
    required TResult Function(_Error value) error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _AddressForm():
        return addressForm(_that);
      case _AddressList():
        return addressList(_that);
      case _MapSelection():
        return mapSelection(_that);
      case _MapSearch():
        return searchScreen(_that);
      case _Error():
        return error(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddressForm value)? addressForm,
    TResult? Function(_AddressList value)? addressList,
    TResult? Function(_MapSelection value)? mapSelection,
    TResult? Function(_MapSearch value)? searchScreen,
    TResult? Function(_Error value)? error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _AddressForm() when addressForm != null:
        return addressForm(_that);
      case _AddressList() when addressList != null:
        return addressList(_that);
      case _MapSelection() when mapSelection != null:
        return mapSelection(_that);
      case _MapSearch() when searchScreen != null:
        return searchScreen(_that);
      case _Error() when error != null:
        return error(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        initial,
    TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        loading,
    TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            bool isEdit,
            String? delliveryDuration,
            bool applyFilter)?
        addressForm,
    TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool isLoading,
            bool hasError,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        addressList,
    TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        mapSelection,
    TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            List<PlaceSuggestionModel>? searchResults,
            bool isSearching,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        searchScreen,
    TResult Function(
            String message,
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        error,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _Loading() when loading != null:
        return loading(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _AddressForm() when addressForm != null:
        return addressForm(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.isEdit,
            _that.delliveryDuration,
            _that.applyFilter);
      case _AddressList() when addressList != null:
        return addressList(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.isLoading,
            _that.hasError,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _MapSelection() when mapSelection != null:
        return mapSelection(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _MapSearch() when searchScreen != null:
        return searchScreen(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.searchResults,
            _that.isSearching,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _Error() when error != null:
        return error(
            _that.message,
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)
        initial,
    required TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)
        loading,
    required TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            bool isEdit,
            String? delliveryDuration,
            bool applyFilter)
        addressForm,
    required TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool isLoading,
            bool hasError,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)
        addressList,
    required TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)
        mapSelection,
    required TResult Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            List<PlaceSuggestionModel>? searchResults,
            bool isSearching,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)
        searchScreen,
    required TResult Function(
            String message,
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)
        error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _Loading():
        return loading(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _AddressForm():
        return addressForm(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.isEdit,
            _that.delliveryDuration,
            _that.applyFilter);
      case _AddressList():
        return addressList(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.isLoading,
            _that.hasError,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _MapSelection():
        return mapSelection(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _MapSearch():
        return searchScreen(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.searchResults,
            _that.isSearching,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _Error():
        return error(
            _that.message,
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        initial,
    TResult? Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        loading,
    TResult? Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            bool isEdit,
            String? delliveryDuration,
            bool applyFilter)?
        addressForm,
    TResult? Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool isLoading,
            bool hasError,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        addressList,
    TResult? Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        mapSelection,
    TResult? Function(
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            List<PlaceSuggestionModel>? searchResults,
            bool isSearching,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        searchScreen,
    TResult? Function(
            String message,
            List<AddressModel>? addresses,
            AddressModel? temporaryAddress,
            AddressModel? selectedAddress,
            AddressModel? currentLocation,
            bool relocateMap,
            bool isServicable,
            String? delliveryDuration,
            bool applyFilter)?
        error,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _Loading() when loading != null:
        return loading(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _AddressForm() when addressForm != null:
        return addressForm(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.isEdit,
            _that.delliveryDuration,
            _that.applyFilter);
      case _AddressList() when addressList != null:
        return addressList(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.isLoading,
            _that.hasError,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _MapSelection() when mapSelection != null:
        return mapSelection(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _MapSearch() when searchScreen != null:
        return searchScreen(
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.searchResults,
            _that.isSearching,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _Error() when error != null:
        return error(
            _that.message,
            _that.addresses,
            _that.temporaryAddress,
            _that.selectedAddress,
            _that.currentLocation,
            _that.relocateMap,
            _that.isServicable,
            _that.delliveryDuration,
            _that.applyFilter);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial with DiagnosticableTreeMixin implements AddressState {
  const _Initial(
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      this.relocateMap = false,
      this.isServicable = true,
      this.delliveryDuration = '...',
      this.applyFilter = false})
      : _addresses = addresses;

  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @override
  @JsonKey()
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitialCopyWith<_Initial> get copyWith =>
      __$InitialCopyWithImpl<_Initial>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.initial'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Initial &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.initial(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$InitialCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$InitialCopyWith(_Initial value, $Res Function(_Initial) _then) =
      __$InitialCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$InitialCopyWithImpl<$Res> implements _$InitialCopyWith<$Res> {
  __$InitialCopyWithImpl(this._self, this._then);

  final _Initial _self;
  final $Res Function(_Initial) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_Initial(
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _Loading with DiagnosticableTreeMixin implements AddressState {
  const _Loading(
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      this.relocateMap = false,
      this.isServicable = true,
      this.delliveryDuration = '...',
      this.applyFilter = false})
      : _addresses = addresses;

  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @override
  @JsonKey()
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadingCopyWith<_Loading> get copyWith =>
      __$LoadingCopyWithImpl<_Loading>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.loading'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loading &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.loading(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$LoadingCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$LoadingCopyWith(_Loading value, $Res Function(_Loading) _then) =
      __$LoadingCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$LoadingCopyWithImpl<$Res> implements _$LoadingCopyWith<$Res> {
  __$LoadingCopyWithImpl(this._self, this._then);

  final _Loading _self;
  final $Res Function(_Loading) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_Loading(
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _AddressForm with DiagnosticableTreeMixin implements AddressState {
  const _AddressForm(
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      this.relocateMap = false,
      this.isServicable = false,
      this.isEdit = false,
      this.delliveryDuration,
      this.applyFilter = false})
      : _addresses = addresses;

  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @JsonKey()
  final bool isEdit;
  @override
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AddressFormCopyWith<_AddressForm> get copyWith =>
      __$AddressFormCopyWithImpl<_AddressForm>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.addressForm'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('isEdit', isEdit))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AddressForm &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.isEdit, isEdit) || other.isEdit == isEdit) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      relocateMap,
      isServicable,
      isEdit,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.addressForm(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, relocateMap: $relocateMap, isServicable: $isServicable, isEdit: $isEdit, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$AddressFormCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$AddressFormCopyWith(
          _AddressForm value, $Res Function(_AddressForm) _then) =
      __$AddressFormCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool relocateMap,
      bool isServicable,
      bool isEdit,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$AddressFormCopyWithImpl<$Res> implements _$AddressFormCopyWith<$Res> {
  __$AddressFormCopyWithImpl(this._self, this._then);

  final _AddressForm _self;
  final $Res Function(_AddressForm) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? isEdit = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_AddressForm(
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      isEdit: null == isEdit
          ? _self.isEdit
          : isEdit // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _AddressList with DiagnosticableTreeMixin implements AddressState {
  const _AddressList(
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      required this.isLoading,
      required this.hasError,
      this.relocateMap = false,
      this.isServicable = false,
      this.delliveryDuration,
      this.applyFilter = false})
      : _addresses = addresses;

  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  final bool isLoading;
  final bool hasError;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @override
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AddressListCopyWith<_AddressList> get copyWith =>
      __$AddressListCopyWithImpl<_AddressList>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.addressList'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('isLoading', isLoading))
      ..add(DiagnosticsProperty('hasError', hasError))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AddressList &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      isLoading,
      hasError,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.addressList(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, isLoading: $isLoading, hasError: $hasError, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$AddressListCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$AddressListCopyWith(
          _AddressList value, $Res Function(_AddressList) _then) =
      __$AddressListCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool isLoading,
      bool hasError,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$AddressListCopyWithImpl<$Res> implements _$AddressListCopyWith<$Res> {
  __$AddressListCopyWithImpl(this._self, this._then);

  final _AddressList _self;
  final $Res Function(_AddressList) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? isLoading = null,
    Object? hasError = null,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_AddressList(
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _MapSelection with DiagnosticableTreeMixin implements AddressState {
  const _MapSelection(
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      this.relocateMap = false,
      this.isServicable = false,
      this.delliveryDuration,
      this.applyFilter = false})
      : _addresses = addresses;

  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @override
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MapSelectionCopyWith<_MapSelection> get copyWith =>
      __$MapSelectionCopyWithImpl<_MapSelection>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.mapSelection'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MapSelection &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.mapSelection(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$MapSelectionCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$MapSelectionCopyWith(
          _MapSelection value, $Res Function(_MapSelection) _then) =
      __$MapSelectionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$MapSelectionCopyWithImpl<$Res>
    implements _$MapSelectionCopyWith<$Res> {
  __$MapSelectionCopyWithImpl(this._self, this._then);

  final _MapSelection _self;
  final $Res Function(_MapSelection) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_MapSelection(
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _MapSearch with DiagnosticableTreeMixin implements AddressState {
  const _MapSearch(
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      final List<PlaceSuggestionModel>? searchResults,
      this.isSearching = false,
      this.relocateMap = false,
      this.isServicable = false,
      this.delliveryDuration,
      this.applyFilter = false})
      : _addresses = addresses,
        _searchResults = searchResults;

  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  final List<PlaceSuggestionModel>? _searchResults;
  List<PlaceSuggestionModel>? get searchResults {
    final value = _searchResults;
    if (value == null) return null;
    if (_searchResults is EqualUnmodifiableListView) return _searchResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @JsonKey()
  final bool isSearching;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @override
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MapSearchCopyWith<_MapSearch> get copyWith =>
      __$MapSearchCopyWithImpl<_MapSearch>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.searchScreen'))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('searchResults', searchResults))
      ..add(DiagnosticsProperty('isSearching', isSearching))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MapSearch &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            const DeepCollectionEquality()
                .equals(other._searchResults, _searchResults) &&
            (identical(other.isSearching, isSearching) ||
                other.isSearching == isSearching) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      const DeepCollectionEquality().hash(_searchResults),
      isSearching,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.searchScreen(addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, searchResults: $searchResults, isSearching: $isSearching, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$MapSearchCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$MapSearchCopyWith(
          _MapSearch value, $Res Function(_MapSearch) _then) =
      __$MapSearchCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      List<PlaceSuggestionModel>? searchResults,
      bool isSearching,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$MapSearchCopyWithImpl<$Res> implements _$MapSearchCopyWith<$Res> {
  __$MapSearchCopyWithImpl(this._self, this._then);

  final _MapSearch _self;
  final $Res Function(_MapSearch) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? searchResults = freezed,
    Object? isSearching = null,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_MapSearch(
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      searchResults: freezed == searchResults
          ? _self._searchResults
          : searchResults // ignore: cast_nullable_to_non_nullable
              as List<PlaceSuggestionModel>?,
      isSearching: null == isSearching
          ? _self.isSearching
          : isSearching // ignore: cast_nullable_to_non_nullable
              as bool,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _Error with DiagnosticableTreeMixin implements AddressState {
  const _Error(this.message,
      {final List<AddressModel>? addresses,
      this.temporaryAddress,
      this.selectedAddress,
      this.currentLocation,
      this.relocateMap = false,
      this.isServicable = false,
      this.delliveryDuration,
      this.applyFilter = false})
      : _addresses = addresses;

  final String message;
  final List<AddressModel>? _addresses;
  @override
  List<AddressModel>? get addresses {
    final value = _addresses;
    if (value == null) return null;
    if (_addresses is EqualUnmodifiableListView) return _addresses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? temporaryAddress;
  @override
  final AddressModel? selectedAddress;
  @override
  final AddressModel? currentLocation;
  @override
  @JsonKey()
  final bool relocateMap;
  @override
  @JsonKey()
  final bool isServicable;
  @override
  final String? delliveryDuration;
  @override
  @JsonKey()
  final bool applyFilter;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ErrorCopyWith<_Error> get copyWith =>
      __$ErrorCopyWithImpl<_Error>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AddressState.error'))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('addresses', addresses))
      ..add(DiagnosticsProperty('temporaryAddress', temporaryAddress))
      ..add(DiagnosticsProperty('selectedAddress', selectedAddress))
      ..add(DiagnosticsProperty('currentLocation', currentLocation))
      ..add(DiagnosticsProperty('relocateMap', relocateMap))
      ..add(DiagnosticsProperty('isServicable', isServicable))
      ..add(DiagnosticsProperty('delliveryDuration', delliveryDuration))
      ..add(DiagnosticsProperty('applyFilter', applyFilter));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Error &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality()
                .equals(other._addresses, _addresses) &&
            (identical(other.temporaryAddress, temporaryAddress) ||
                other.temporaryAddress == temporaryAddress) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.currentLocation, currentLocation) ||
                other.currentLocation == currentLocation) &&
            (identical(other.relocateMap, relocateMap) ||
                other.relocateMap == relocateMap) &&
            (identical(other.isServicable, isServicable) ||
                other.isServicable == isServicable) &&
            (identical(other.delliveryDuration, delliveryDuration) ||
                other.delliveryDuration == delliveryDuration) &&
            (identical(other.applyFilter, applyFilter) ||
                other.applyFilter == applyFilter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      const DeepCollectionEquality().hash(_addresses),
      temporaryAddress,
      selectedAddress,
      currentLocation,
      relocateMap,
      isServicable,
      delliveryDuration,
      applyFilter);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AddressState.error(message: $message, addresses: $addresses, temporaryAddress: $temporaryAddress, selectedAddress: $selectedAddress, currentLocation: $currentLocation, relocateMap: $relocateMap, isServicable: $isServicable, delliveryDuration: $delliveryDuration, applyFilter: $applyFilter)';
  }
}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res>
    implements $AddressStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) =
      __$ErrorCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String message,
      List<AddressModel>? addresses,
      AddressModel? temporaryAddress,
      AddressModel? selectedAddress,
      AddressModel? currentLocation,
      bool relocateMap,
      bool isServicable,
      String? delliveryDuration,
      bool applyFilter});
}

/// @nodoc
class __$ErrorCopyWithImpl<$Res> implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

  /// Create a copy of AddressState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
    Object? addresses = freezed,
    Object? temporaryAddress = freezed,
    Object? selectedAddress = freezed,
    Object? currentLocation = freezed,
    Object? relocateMap = null,
    Object? isServicable = null,
    Object? delliveryDuration = freezed,
    Object? applyFilter = null,
  }) {
    return _then(_Error(
      null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      addresses: freezed == addresses
          ? _self._addresses
          : addresses // ignore: cast_nullable_to_non_nullable
              as List<AddressModel>?,
      temporaryAddress: freezed == temporaryAddress
          ? _self.temporaryAddress
          : temporaryAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      selectedAddress: freezed == selectedAddress
          ? _self.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      currentLocation: freezed == currentLocation
          ? _self.currentLocation
          : currentLocation // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
      relocateMap: null == relocateMap
          ? _self.relocateMap
          : relocateMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isServicable: null == isServicable
          ? _self.isServicable
          : isServicable // ignore: cast_nullable_to_non_nullable
              as bool,
      delliveryDuration: freezed == delliveryDuration
          ? _self.delliveryDuration
          : delliveryDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      applyFilter: null == applyFilter
          ? _self.applyFilter
          : applyFilter // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
