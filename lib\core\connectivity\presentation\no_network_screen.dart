import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';

import '../../themes/color_schemes.dart';
import '../bloc/connectivity_bloc.dart';
import '../bloc/connectivity_event.dart';
import '../bloc/connectivity_state.dart';

class NoNetworkScreen extends StatelessWidget {
  const NoNetworkScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.white,
        body:
            Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Lottie.asset(
                    'assets/lotties/no_connection.json',
                    width: 250,
                    height: 250,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 30),

                  Text(
                    'Oops! No Internet Connection',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 15),

                  Text(
                    'It looks like you\'re offline. Please check your Wi-Fi or mobile data settings and try again.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[700],
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),

                  Text(
                    'Tip: Sometimes, simply toggling your airplane mode can help!',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),

                  // Reload Button with BLoC integration
                  BlocBuilder<ConnectivityBloc, ConnectivityState>(
                    builder: (context, state) {
                      final bool isChecking = state.maybeWhen(
                        checking: () => true,
                        orElse: () => false,
                      );

                      return ElevatedButton.icon(
                        onPressed: isChecking
                            ? null
                            : () {
                                context.read<ConnectivityBloc>().add(
                                    const ConnectivityEvent.retryConnection());
                              },
                        icon: isChecking
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.refresh),
                        label: Text(isChecking
                            ? 'Checking Connection...'
                            : 'Try Again'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 40, vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 8, // Added more elevation
                          backgroundColor:
                              AppColors.primary, // More vibrant color
                          foregroundColor: Colors.white,
                          textStyle: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: 100)
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
