import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';

class SearchAnalyticsEvents {
  final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  /// Get analytics data
  Future<Map<String, dynamic>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track User Search Box Selected
  Future<void> trackUserSearchBoxSelected() async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logUserSearchBoxSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          screenName: 'Search Screen',
          searchText: '',
        );
      }
    } catch (e) {
      debugPrint('Error tracking user search box selected: $e');
    }
  }

  /// Track User Search Text Entered
  Future<void> trackUserSearchTextEntered({
    required String searchText,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logUserSearchTextEntered(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          searchText: searchText,
          screenName: 'Search Screen',
        );
      }
    } catch (e) {
      debugPrint('Error tracking user search text entered: $e');
    }
  }
}