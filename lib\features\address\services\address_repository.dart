import 'package:rozana/core/services/firestore_address_service.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/core/utils/logger.dart';

class AddressRepository {
  final FirestoreAddressService _firestoreService;

  AddressRepository(this._firestoreService);

  bool get isUserAuthenticated => _firestoreService.isUserAuthenticated;

  Future<List<AddressModel>> getAllAddresses() async {
    if (!isUserAuthenticated) {
      return [];
    }
    try {
      LogMessage.p('Fetching addresses from Firestore');
      return await _firestoreService.getAllAddresses();
    } catch (e) {
      LogMessage.p('Error fetching addresses from Firestore: $e');
      return [];
    }
  }

  Future<void> saveAddress(AddressModel address) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to save addresses');
    }
    try {
      await _firestoreService.saveAddress(address);
    } catch (e) {
      LogMessage.p('Error saving address to Firestore: $e');
      rethrow;
    }
  }

  Future<void> deleteAddress(String addressId) async {
    if (!isUserAuthenticated) {
      throw Exception('User must be authenticated to delete addresses');
    }
    try {
      await _firestoreService.deleteAddress(addressId);
    } catch (e) {
      LogMessage.p('Error deleting address from Firestore: $e');
      rethrow;
    }
  }
}
