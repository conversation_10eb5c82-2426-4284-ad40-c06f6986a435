import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../core/themes/color_schemes.dart';

class CustomBorder extends StatelessWidget {
  const CustomBorder({
    super.key,
    this.radius,
    required this.child,
    this.color,
    this.side,
    this.topLeft,
    this.topRight,
    this.bottomLeft,
    this.bottomRight,
  });
  final double? radius;
  final Widget child;
  final Color? color;
  final BorderSide? side;
  final double? topLeft;
  final double? topRight;
  final double? bottomLeft;
  final double? bottomRight;

  @override
  Widget build(BuildContext context) {
    double radiusValue = ((radius ?? 12) * 2.5).sp;
    return Material(
      shape: ContinuousRectangleBorder(
        borderRadius: (topLeft != null ||
                topRight != null ||
                bottomLeft != null ||
                bottomRight != null)
            ? BorderRadius.only(
                topLeft: Radius.circular(((topLeft ?? 0) * 2.5).sp),
                topRight: Radius.circular(((topRight ?? 0) * 2.5).sp),
                bottomLeft: Radius.circular(((bottomLeft ?? 0) * 2.5).sp),
                bottomRight: Radius.circular(((bottomRight ?? 0) * 2.5).sp))
            : BorderRadius.circular(radiusValue),
        side: side ?? BorderSide.none,
      ),
      color: color ?? AppColors.neutral100,
      child: ClipRRect(
        borderRadius: (topLeft != null ||
                topRight != null ||
                bottomLeft != null ||
                bottomRight != null)
            ? BorderRadius.only(
                topLeft: Radius.circular(((topLeft ?? 0) * 2.5).sp),
                topRight: Radius.circular(((topRight ?? 0) * 2.5).sp),
                bottomLeft: Radius.circular(((bottomLeft ?? 0) * 2.5).sp),
                bottomRight: Radius.circular(((bottomRight ?? 0) * 2.5).sp))
            : BorderRadius.circular((radius ?? 0).sp),
        child: child,
      ),
    );
  }
}
