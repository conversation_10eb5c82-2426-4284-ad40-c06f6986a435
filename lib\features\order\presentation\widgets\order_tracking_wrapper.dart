import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/domain/usecases/get_order_history_usecase.dart';
import 'package:rozana/features/order/bloc/order_tracking/order_tracking.dart';
import 'package:rozana/features/order/presentation/widgets/order_tracking_floating_widget.dart';

import '../../../../core/dependency_injection/di_container.dart';

/// A wrapper that adds the order tracking floating widget to any child widget
/// This widget is responsible for initializing the OrderTrackingBloc and
/// displaying the floating order tracking widget when there are pending orders
class OrderTrackingWrapper extends StatelessWidget {
  final Widget child;
  final bool excludeOrderTile;

  const OrderTrackingWrapper({
    super.key,
    required this.child,
    this.excludeOrderTile = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) => OrderTrackingBloc(
              getOrderHistoryUseCase: getIt<GetOrderHistoryUseCase>(),
            )..add(InitializeOrderTracking()),
        child: (!excludeOrderTile)
            ? const OrderTrackingFloatingWidget()
            : SizedBox.shrink());
  }
}
