import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:rozana/core/themes/color_schemes.dart';

class RavenlinkTracker extends StatefulWidget {
  final Map<String, dynamic> invoice;
  final double height;
  final bool showTitle;

  const RavenlinkTracker({
    super.key,
    required this.invoice,
    this.height = 350,
    this.showTitle = true,
  });

  @override
  State<RavenlinkTracker> createState() => _RavenlinkTrackerState();
}

class _RavenlinkTrackerState extends State<RavenlinkTracker> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    final url = widget.invoice['raven_link'] as String? ?? '';
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('Ravenlink WebView error: ${error.description}');
          },
        ),
      )
      ..loadRequest(Uri.parse(url));
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
              ),
            ),
        ],
      ),
    );
  }
}
