import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/order_analytics_events.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/custom_button.dart';
import 'package:rozana/widgets/lazy_loading_widget.dart';
import 'package:rozana/widgets/skeleton_loader_factory.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import '../../bloc/order_bloc.dart';
import '../widgets/order_card.dart';

class OrderHistoryScreen extends StatefulWidget {
  const OrderHistoryScreen({super.key});

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}

class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  bool isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    // Initialize order history
    isAuthenticated = getIt<AppBloc>().isAuthenticated;
    getIt<OrderBloc>().add(const OrderEvent.init());
    // Setup scroll listener for pagination
    _scrollController.addListener(_onScroll);
    // Track order history page landed
    OrderAnalyticsEvents().trackOrderHistoryPageLanded();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      final state = getIt<OrderBloc>().state;
      if (state.hasMoreData && !state.isLoadingMore) {
        getIt<OrderBloc>().add(const OrderEvent.loadMoreOrders());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.neutral150,
      appBar: AppBar(
        backgroundColor: AppColors.neutral100,
        scrolledUnderElevation: 0,
        elevation: 0,
        centerTitle: false,
        titleSpacing: 0,
        leadingWidth: 0,
        automaticallyImplyLeading: false,
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            InkWell(
              onTap: () => context.pop(),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                child: Image.asset(
                  'assets/new/icons/chevron_left.png',
                  height: 26,
                  width: 26,
                  color: AppColors.primary600,
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: CustomText(
                "Orders",
                color: AppColors.primary700,
                fontSize: 20,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Order list
          if (isAuthenticated)
            Expanded(
              child: BlocConsumer<OrderBloc, OrderState>(
                listener: (context, state) {
                  state.maybeWhen(
                    error: (message, orderId) {},
                    orderCancelled: (orderId, message) async {},
                    itemsAddedToCart: (itemCount, navigateToCart) {
                      if (navigateToCart) {
                        context.go(RouteNames.cart);
                      }
                    },
                    orElse: () {},
                  );
                },
                builder: (context, state) {
                  return state.when(
                    initial: () => _buildSkeletonLoader(),
                    loading: () => _buildSkeletonLoader(),
                    itemsAddedToCart: (_, __) => _buildSkeletonLoader(),
                    orderHistoryLoaded: (orders, isLoadingMore, hasMoreData,
                        currentPage, searchQuery) {
                      return RefreshIndicator(
                        onRefresh: () async {
                          getIt<OrderBloc>().add(
                            const OrderEvent.refreshOrderHistory(),
                          );
                        },
                        child: LazyLoadingWidget<OrderEntity>(
                          items: orders,
                          isLoading: false,
                          hasMoreData: hasMoreData,
                          onLoadMore: hasMoreData && !isLoadingMore
                              ? () => getIt<OrderBloc>().add(
                                    const OrderEvent.loadMoreOrders(),
                                  )
                              : null,
                          scrollController: _scrollController,
                          padding: const EdgeInsets.only(top: 8, bottom: 150),
                          itemBuilder: (context, order, index) {
                            return OrderCard(
                              order: order,
                              onTap: () async {
                                OrderAnalyticsEvents().trackOrderListTrackOrderClicked(orderId: order.id);
                                _navigateToOrderDetails(order.id);
                              },
                              onOrderAgain: () async {
                                OrderAnalyticsEvents().trackOrderListOrderAgainClicked(orderId: order.id);
                                _handleOrderAgain(order);
                              },
                            );
                          },
                          loadingBuilder: (context) => const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: CircularProgressIndicator(),
                            ),
                          ),
                          emptyBuilder: (context) => _buildEmptyState(),
                        ),
                      );
                    },
                    empty: (searchQuery) => _buildEmptyState(),
                    error: (message, orderId) => _buildErrorState(message),
                    orderDetailsLoaded: (_) => const SizedBox.shrink(),
                    orderCancelled: (_, __) => const SizedBox.shrink(),
                  );
                },
              ),
            ),

          if (!isAuthenticated)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: CustomText(
                        "Please log in to access your past orders.",
                        maxLines: 3,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            )
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 80,
              color: AppColors.neutral200,
            ),
            const SizedBox(height: 16),
            CustomText(
              'No Orders Found',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            const SizedBox(height: 8),
            CustomText(
              'You haven\'t placed any orders yet',
              fontSize: 14,
              color: AppColors.neutral400,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Start Shopping',
              onPressed: () => context.go('/'),
              backgroundColor: AppColors.primary,
              textColor: AppColors.neutral100,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            CustomText(
              'Something went wrong',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
            const SizedBox(height: 8),
            CustomText(
              message,
              fontSize: 14,
              color: AppColors.neutral400,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Try Again',
              onPressed: () => getIt<OrderBloc>().add(
                const OrderEvent.refreshOrderHistory(),
              ),
              backgroundColor: AppColors.primary,
              textColor: AppColors.neutral100,
              borderRadius: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: 5,
      itemBuilder: (context, index) {
        return SkeletonLoaderFactory.createOrderCardSkeleton();
      },
    );
  }

  void _navigateToOrderDetails(String orderId) {
    context.push('${RouteNames.orders}/$orderId');
  }

  // Handle 'Order again' functionality
  void _handleOrderAgain(OrderEntity order) {
    context.read<OrderBloc>().add(OrderEvent.orderAgain(order));
  }
}
