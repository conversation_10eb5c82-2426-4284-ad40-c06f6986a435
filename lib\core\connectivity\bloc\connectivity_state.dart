import 'package:freezed_annotation/freezed_annotation.dart';

part 'connectivity_state.freezed.dart';

/// Defines the sealed union for all connectivity-related states using Freezed.
/// This provides immutable states with built-in value equality.
@freezed
abstract class ConnectivityState with _$ConnectivityState {
  /// Initial state of the connectivity BLoC.
  const factory ConnectivityState.initial() = _ConnectivityInitial;

  /// State indicating that the device is currently connected to a network.
  const factory ConnectivityState.connected() = _ConnectivityConnected;

  /// State indicating that the device is currently disconnected from all networks.
  const factory ConnectivityState.disconnected() = _ConnectivityDisconnected;

  /// Optional state indicating that a connectivity check is currently in progress.
  /// Useful for showing a loading indicator.
  const factory ConnectivityState.checking() = _ConnectivityChecking;
}
