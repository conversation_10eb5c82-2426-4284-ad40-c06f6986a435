// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OrderEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderEvent()';
  }
}

/// @nodoc
class $OrderEventCopyWith<$Res> {
  $OrderEventCopyWith(OrderEvent _, $Res Function(OrderEvent) __);
}

/// Adds pattern-matching-related methods to [OrderEvent].
extension OrderEventPatterns on OrderEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInit value)? init,
    TResult Function(LoadOrderHistory value)? loadOrderHistory,
    TResult Function(LoadMoreOrders value)? loadMoreOrders,
    TResult Function(LoadOrderDetails value)? loadOrderDetails,
    TResult Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult Function(CancelOrder value)? cancelOrder,
    TResult Function(ClearOrderDetails value)? clearOrderDetails,
    TResult Function(SearchOrders value)? searchOrders,
    TResult Function(ResetOrder value)? reset,
    TResult Function(ReturnOrder value)? returnOrder,
    TResult Function(OrderAgain value)? orderAgain,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case OrderInit() when init != null:
        return init(_that);
      case LoadOrderHistory() when loadOrderHistory != null:
        return loadOrderHistory(_that);
      case LoadMoreOrders() when loadMoreOrders != null:
        return loadMoreOrders(_that);
      case LoadOrderDetails() when loadOrderDetails != null:
        return loadOrderDetails(_that);
      case RefreshOrderHistory() when refreshOrderHistory != null:
        return refreshOrderHistory(_that);
      case CancelOrder() when cancelOrder != null:
        return cancelOrder(_that);
      case ClearOrderDetails() when clearOrderDetails != null:
        return clearOrderDetails(_that);
      case SearchOrders() when searchOrders != null:
        return searchOrders(_that);
      case ResetOrder() when reset != null:
        return reset(_that);
      case ReturnOrder() when returnOrder != null:
        return returnOrder(_that);
      case OrderAgain() when orderAgain != null:
        return orderAgain(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInit value) init,
    required TResult Function(LoadOrderHistory value) loadOrderHistory,
    required TResult Function(LoadMoreOrders value) loadMoreOrders,
    required TResult Function(LoadOrderDetails value) loadOrderDetails,
    required TResult Function(RefreshOrderHistory value) refreshOrderHistory,
    required TResult Function(CancelOrder value) cancelOrder,
    required TResult Function(ClearOrderDetails value) clearOrderDetails,
    required TResult Function(SearchOrders value) searchOrders,
    required TResult Function(ResetOrder value) reset,
    required TResult Function(ReturnOrder value) returnOrder,
    required TResult Function(OrderAgain value) orderAgain,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInit():
        return init(_that);
      case LoadOrderHistory():
        return loadOrderHistory(_that);
      case LoadMoreOrders():
        return loadMoreOrders(_that);
      case LoadOrderDetails():
        return loadOrderDetails(_that);
      case RefreshOrderHistory():
        return refreshOrderHistory(_that);
      case CancelOrder():
        return cancelOrder(_that);
      case ClearOrderDetails():
        return clearOrderDetails(_that);
      case SearchOrders():
        return searchOrders(_that);
      case ResetOrder():
        return reset(_that);
      case ReturnOrder():
        return returnOrder(_that);
      case OrderAgain():
        return orderAgain(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInit value)? init,
    TResult? Function(LoadOrderHistory value)? loadOrderHistory,
    TResult? Function(LoadMoreOrders value)? loadMoreOrders,
    TResult? Function(LoadOrderDetails value)? loadOrderDetails,
    TResult? Function(RefreshOrderHistory value)? refreshOrderHistory,
    TResult? Function(CancelOrder value)? cancelOrder,
    TResult? Function(ClearOrderDetails value)? clearOrderDetails,
    TResult? Function(SearchOrders value)? searchOrders,
    TResult? Function(ResetOrder value)? reset,
    TResult? Function(ReturnOrder value)? returnOrder,
    TResult? Function(OrderAgain value)? orderAgain,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInit() when init != null:
        return init(_that);
      case LoadOrderHistory() when loadOrderHistory != null:
        return loadOrderHistory(_that);
      case LoadMoreOrders() when loadMoreOrders != null:
        return loadMoreOrders(_that);
      case LoadOrderDetails() when loadOrderDetails != null:
        return loadOrderDetails(_that);
      case RefreshOrderHistory() when refreshOrderHistory != null:
        return refreshOrderHistory(_that);
      case CancelOrder() when cancelOrder != null:
        return cancelOrder(_that);
      case ClearOrderDetails() when clearOrderDetails != null:
        return clearOrderDetails(_that);
      case SearchOrders() when searchOrders != null:
        return searchOrders(_that);
      case ResetOrder() when reset != null:
        return reset(_that);
      case ReturnOrder() when returnOrder != null:
        return returnOrder(_that);
      case OrderAgain() when orderAgain != null:
        return orderAgain(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult Function()? loadMoreOrders,
    TResult Function(String orderId, bool showLoader)? loadOrderDetails,
    TResult Function(String status)? refreshOrderHistory,
    TResult Function(String orderId)? cancelOrder,
    TResult Function()? clearOrderDetails,
    TResult Function(String query)? searchOrders,
    TResult Function()? reset,
    TResult Function(String orderId, OrderItemEntity item, String reason)?
        returnOrder,
    TResult Function(OrderEntity order)? orderAgain,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case OrderInit() when init != null:
        return init();
      case LoadOrderHistory() when loadOrderHistory != null:
        return loadOrderHistory(_that.customerId, _that.status, _that.page,
            _that.pageSize, _that.refresh);
      case LoadMoreOrders() when loadMoreOrders != null:
        return loadMoreOrders();
      case LoadOrderDetails() when loadOrderDetails != null:
        return loadOrderDetails(_that.orderId, _that.showLoader);
      case RefreshOrderHistory() when refreshOrderHistory != null:
        return refreshOrderHistory(_that.status);
      case CancelOrder() when cancelOrder != null:
        return cancelOrder(_that.orderId);
      case ClearOrderDetails() when clearOrderDetails != null:
        return clearOrderDetails();
      case SearchOrders() when searchOrders != null:
        return searchOrders(_that.query);
      case ResetOrder() when reset != null:
        return reset();
      case ReturnOrder() when returnOrder != null:
        return returnOrder(_that.orderId, _that.item, _that.reason);
      case OrderAgain() when orderAgain != null:
        return orderAgain(_that.order);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(String customerId, String status, int page,
            int pageSize, bool refresh)
        loadOrderHistory,
    required TResult Function() loadMoreOrders,
    required TResult Function(String orderId, bool showLoader) loadOrderDetails,
    required TResult Function(String status) refreshOrderHistory,
    required TResult Function(String orderId) cancelOrder,
    required TResult Function() clearOrderDetails,
    required TResult Function(String query) searchOrders,
    required TResult Function() reset,
    required TResult Function(
            String orderId, OrderItemEntity item, String reason)
        returnOrder,
    required TResult Function(OrderEntity order) orderAgain,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInit():
        return init();
      case LoadOrderHistory():
        return loadOrderHistory(_that.customerId, _that.status, _that.page,
            _that.pageSize, _that.refresh);
      case LoadMoreOrders():
        return loadMoreOrders();
      case LoadOrderDetails():
        return loadOrderDetails(_that.orderId, _that.showLoader);
      case RefreshOrderHistory():
        return refreshOrderHistory(_that.status);
      case CancelOrder():
        return cancelOrder(_that.orderId);
      case ClearOrderDetails():
        return clearOrderDetails();
      case SearchOrders():
        return searchOrders(_that.query);
      case ResetOrder():
        return reset();
      case ReturnOrder():
        return returnOrder(_that.orderId, _that.item, _that.reason);
      case OrderAgain():
        return orderAgain(_that.order);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(String customerId, String status, int page, int pageSize,
            bool refresh)?
        loadOrderHistory,
    TResult? Function()? loadMoreOrders,
    TResult? Function(String orderId, bool showLoader)? loadOrderDetails,
    TResult? Function(String status)? refreshOrderHistory,
    TResult? Function(String orderId)? cancelOrder,
    TResult? Function()? clearOrderDetails,
    TResult? Function(String query)? searchOrders,
    TResult? Function()? reset,
    TResult? Function(String orderId, OrderItemEntity item, String reason)?
        returnOrder,
    TResult? Function(OrderEntity order)? orderAgain,
  }) {
    final _that = this;
    switch (_that) {
      case OrderInit() when init != null:
        return init();
      case LoadOrderHistory() when loadOrderHistory != null:
        return loadOrderHistory(_that.customerId, _that.status, _that.page,
            _that.pageSize, _that.refresh);
      case LoadMoreOrders() when loadMoreOrders != null:
        return loadMoreOrders();
      case LoadOrderDetails() when loadOrderDetails != null:
        return loadOrderDetails(_that.orderId, _that.showLoader);
      case RefreshOrderHistory() when refreshOrderHistory != null:
        return refreshOrderHistory(_that.status);
      case CancelOrder() when cancelOrder != null:
        return cancelOrder(_that.orderId);
      case ClearOrderDetails() when clearOrderDetails != null:
        return clearOrderDetails();
      case SearchOrders() when searchOrders != null:
        return searchOrders(_that.query);
      case ResetOrder() when reset != null:
        return reset();
      case ReturnOrder() when returnOrder != null:
        return returnOrder(_that.orderId, _that.item, _that.reason);
      case OrderAgain() when orderAgain != null:
        return orderAgain(_that.order);
      case _:
        return null;
    }
  }
}

/// @nodoc

class OrderInit implements OrderEvent {
  const OrderInit();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OrderInit);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderEvent.init()';
  }
}

/// @nodoc

class LoadOrderHistory implements OrderEvent {
  const LoadOrderHistory(
      {this.customerId = 'customer_123',
      this.status = '',
      this.page = 0,
      this.pageSize = 10,
      this.refresh = false});

  @JsonKey()
  final String customerId;
  @JsonKey()
  final String status;
  @JsonKey()
  final int page;
  @JsonKey()
  final int pageSize;
  @JsonKey()
  final bool refresh;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadOrderHistoryCopyWith<LoadOrderHistory> get copyWith =>
      _$LoadOrderHistoryCopyWithImpl<LoadOrderHistory>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadOrderHistory &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.refresh, refresh) || other.refresh == refresh));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, customerId, status, page, pageSize, refresh);

  @override
  String toString() {
    return 'OrderEvent.loadOrderHistory(customerId: $customerId, status: $status, page: $page, pageSize: $pageSize, refresh: $refresh)';
  }
}

/// @nodoc
abstract mixin class $LoadOrderHistoryCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $LoadOrderHistoryCopyWith(
          LoadOrderHistory value, $Res Function(LoadOrderHistory) _then) =
      _$LoadOrderHistoryCopyWithImpl;
  @useResult
  $Res call(
      {String customerId, String status, int page, int pageSize, bool refresh});
}

/// @nodoc
class _$LoadOrderHistoryCopyWithImpl<$Res>
    implements $LoadOrderHistoryCopyWith<$Res> {
  _$LoadOrderHistoryCopyWithImpl(this._self, this._then);

  final LoadOrderHistory _self;
  final $Res Function(LoadOrderHistory) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? customerId = null,
    Object? status = null,
    Object? page = null,
    Object? pageSize = null,
    Object? refresh = null,
  }) {
    return _then(LoadOrderHistory(
      customerId: null == customerId
          ? _self.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      refresh: null == refresh
          ? _self.refresh
          : refresh // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class LoadMoreOrders implements OrderEvent {
  const LoadMoreOrders();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoadMoreOrders);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderEvent.loadMoreOrders()';
  }
}

/// @nodoc

class LoadOrderDetails implements OrderEvent {
  const LoadOrderDetails(this.orderId, {this.showLoader = true});

  final String orderId;
  @JsonKey()
  final bool showLoader;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadOrderDetailsCopyWith<LoadOrderDetails> get copyWith =>
      _$LoadOrderDetailsCopyWithImpl<LoadOrderDetails>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadOrderDetails &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.showLoader, showLoader) ||
                other.showLoader == showLoader));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId, showLoader);

  @override
  String toString() {
    return 'OrderEvent.loadOrderDetails(orderId: $orderId, showLoader: $showLoader)';
  }
}

/// @nodoc
abstract mixin class $LoadOrderDetailsCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $LoadOrderDetailsCopyWith(
          LoadOrderDetails value, $Res Function(LoadOrderDetails) _then) =
      _$LoadOrderDetailsCopyWithImpl;
  @useResult
  $Res call({String orderId, bool showLoader});
}

/// @nodoc
class _$LoadOrderDetailsCopyWithImpl<$Res>
    implements $LoadOrderDetailsCopyWith<$Res> {
  _$LoadOrderDetailsCopyWithImpl(this._self, this._then);

  final LoadOrderDetails _self;
  final $Res Function(LoadOrderDetails) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orderId = null,
    Object? showLoader = null,
  }) {
    return _then(LoadOrderDetails(
      null == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      showLoader: null == showLoader
          ? _self.showLoader
          : showLoader // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class RefreshOrderHistory implements OrderEvent {
  const RefreshOrderHistory({this.status = ''});

  @JsonKey()
  final String status;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshOrderHistoryCopyWith<RefreshOrderHistory> get copyWith =>
      _$RefreshOrderHistoryCopyWithImpl<RefreshOrderHistory>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshOrderHistory &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  @override
  String toString() {
    return 'OrderEvent.refreshOrderHistory(status: $status)';
  }
}

/// @nodoc
abstract mixin class $RefreshOrderHistoryCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $RefreshOrderHistoryCopyWith(
          RefreshOrderHistory value, $Res Function(RefreshOrderHistory) _then) =
      _$RefreshOrderHistoryCopyWithImpl;
  @useResult
  $Res call({String status});
}

/// @nodoc
class _$RefreshOrderHistoryCopyWithImpl<$Res>
    implements $RefreshOrderHistoryCopyWith<$Res> {
  _$RefreshOrderHistoryCopyWithImpl(this._self, this._then);

  final RefreshOrderHistory _self;
  final $Res Function(RefreshOrderHistory) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? status = null,
  }) {
    return _then(RefreshOrderHistory(
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class CancelOrder implements OrderEvent {
  const CancelOrder(this.orderId);

  final String orderId;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CancelOrderCopyWith<CancelOrder> get copyWith =>
      _$CancelOrderCopyWithImpl<CancelOrder>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CancelOrder &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId);

  @override
  String toString() {
    return 'OrderEvent.cancelOrder(orderId: $orderId)';
  }
}

/// @nodoc
abstract mixin class $CancelOrderCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $CancelOrderCopyWith(
          CancelOrder value, $Res Function(CancelOrder) _then) =
      _$CancelOrderCopyWithImpl;
  @useResult
  $Res call({String orderId});
}

/// @nodoc
class _$CancelOrderCopyWithImpl<$Res> implements $CancelOrderCopyWith<$Res> {
  _$CancelOrderCopyWithImpl(this._self, this._then);

  final CancelOrder _self;
  final $Res Function(CancelOrder) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orderId = null,
  }) {
    return _then(CancelOrder(
      null == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class ClearOrderDetails implements OrderEvent {
  const ClearOrderDetails();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ClearOrderDetails);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderEvent.clearOrderDetails()';
  }
}

/// @nodoc

class SearchOrders implements OrderEvent {
  const SearchOrders(this.query);

  final String query;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchOrdersCopyWith<SearchOrders> get copyWith =>
      _$SearchOrdersCopyWithImpl<SearchOrders>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchOrders &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  @override
  String toString() {
    return 'OrderEvent.searchOrders(query: $query)';
  }
}

/// @nodoc
abstract mixin class $SearchOrdersCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $SearchOrdersCopyWith(
          SearchOrders value, $Res Function(SearchOrders) _then) =
      _$SearchOrdersCopyWithImpl;
  @useResult
  $Res call({String query});
}

/// @nodoc
class _$SearchOrdersCopyWithImpl<$Res> implements $SearchOrdersCopyWith<$Res> {
  _$SearchOrdersCopyWithImpl(this._self, this._then);

  final SearchOrders _self;
  final $Res Function(SearchOrders) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? query = null,
  }) {
    return _then(SearchOrders(
      null == query
          ? _self.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class ResetOrder implements OrderEvent {
  const ResetOrder();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ResetOrder);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OrderEvent.reset()';
  }
}

/// @nodoc

class ReturnOrder implements OrderEvent {
  const ReturnOrder(this.orderId, this.item, this.reason);

  final String orderId;
  final OrderItemEntity item;
  final String reason;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReturnOrderCopyWith<ReturnOrder> get copyWith =>
      _$ReturnOrderCopyWithImpl<ReturnOrder>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ReturnOrder &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.item, item) || other.item == item) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId, item, reason);

  @override
  String toString() {
    return 'OrderEvent.returnOrder(orderId: $orderId, item: $item, reason: $reason)';
  }
}

/// @nodoc
abstract mixin class $ReturnOrderCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $ReturnOrderCopyWith(
          ReturnOrder value, $Res Function(ReturnOrder) _then) =
      _$ReturnOrderCopyWithImpl;
  @useResult
  $Res call({String orderId, OrderItemEntity item, String reason});
}

/// @nodoc
class _$ReturnOrderCopyWithImpl<$Res> implements $ReturnOrderCopyWith<$Res> {
  _$ReturnOrderCopyWithImpl(this._self, this._then);

  final ReturnOrder _self;
  final $Res Function(ReturnOrder) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? orderId = null,
    Object? item = null,
    Object? reason = null,
  }) {
    return _then(ReturnOrder(
      null == orderId
          ? _self.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      null == item
          ? _self.item
          : item // ignore: cast_nullable_to_non_nullable
              as OrderItemEntity,
      null == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class OrderAgain implements OrderEvent {
  const OrderAgain(this.order);

  final OrderEntity order;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OrderAgainCopyWith<OrderAgain> get copyWith =>
      _$OrderAgainCopyWithImpl<OrderAgain>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OrderAgain &&
            (identical(other.order, order) || other.order == order));
  }

  @override
  int get hashCode => Object.hash(runtimeType, order);

  @override
  String toString() {
    return 'OrderEvent.orderAgain(order: $order)';
  }
}

/// @nodoc
abstract mixin class $OrderAgainCopyWith<$Res>
    implements $OrderEventCopyWith<$Res> {
  factory $OrderAgainCopyWith(
          OrderAgain value, $Res Function(OrderAgain) _then) =
      _$OrderAgainCopyWithImpl;
  @useResult
  $Res call({OrderEntity order});
}

/// @nodoc
class _$OrderAgainCopyWithImpl<$Res> implements $OrderAgainCopyWith<$Res> {
  _$OrderAgainCopyWithImpl(this._self, this._then);

  final OrderAgain _self;
  final $Res Function(OrderAgain) _then;

  /// Create a copy of OrderEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? order = null,
  }) {
    return _then(OrderAgain(
      null == order
          ? _self.order
          : order // ignore: cast_nullable_to_non_nullable
              as OrderEntity,
    ));
  }
}

// dart format on
