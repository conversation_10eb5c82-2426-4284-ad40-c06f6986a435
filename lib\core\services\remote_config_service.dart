import 'dart:async';
import 'dart:convert';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../config/environment_config.dart';
import '../themes/color_schemes.dart';
import '../utils/color_utils.dart';
import 'package:flutter/material.dart';

class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;

  final Completer<void> _initializationCompleter = Completer<void>();

  Future<void> get initialized => _initializationCompleter.future;

  factory RemoteConfigService() => _instance;

  RemoteConfigService._internal();

  Future<void> initialize() async {
    try {
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 5),
        minimumFetchInterval: Duration.zero,
      ));

      await _remoteConfig.fetchAndActivate().timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          return false;
        },
      );

      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }
    } catch (e) {
      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }
    }
  }

  /// Force refresh remote config values regardless of cache expiration
  /// Returns true if values were fetched from the server, false otherwise
  Future<bool> forceRefresh() async {
    try {
      final fetchedRemotely = await _remoteConfig.fetchAndActivate();
      return fetchedRemotely;
    } catch (e) {
      return false;
    }
  }

  /// Get the environment config for the current environment
  Map<String, dynamic> get _currentEnvConfig {
    try {
      final String envKey = EnvironmentConfig.environment.name;
      final configValue = _remoteConfig.getValue(envKey);
      if (configValue.source == ValueSource.valueStatic) {
        return _getDefaultConfig();
      }
      final configString = configValue.asString();
      final Map<String, dynamic> result = _processJsonString(configString);
      return result.isEmpty ? _getDefaultConfig() : result;
    } catch (e) {
      return _getDefaultConfig();
    }
  }

  /// Safely processes a JSON string that might be double-encoded or have escape characters
  /// Returns an empty map if parsing fails
  Map<String, dynamic> _processJsonString(String configString) {
    if (configString.isEmpty) {
      return {};
    }

    try {
      final Map<String, dynamic> decodedJson = json.decode(configString);
      return decodedJson;
    } catch (e1) {
      String processedString = configString;

      // Handle double-quoted JSON strings
      if (processedString.startsWith('"') && processedString.endsWith('"')) {
        processedString =
            processedString.substring(1, processedString.length - 1);
      }

      // Replace escaped quotes
      processedString = processedString.replaceAll('\\"', '"');

      try {
        final Map<String, dynamic> decodedJson = json.decode(processedString);
        return decodedJson;
      } catch (e2) {
        return {};
      }
    }
  }

  Map<String, dynamic> get getThemeConfig {
    try {
      final String envKey = EnvironmentConfig.environment.name;
      final configString = _remoteConfig.getString('${envKey}_theme_settings');
      if (configString.isEmpty) {
        final configString = _remoteConfig.getString('theme_settings');
        return _processJsonString(configString);
      }

      return _processJsonString(configString);
    } catch (e) {
      return {};
    }
  }

  Map<String, Color?> get getDynamicColors {
    try {
      final configString = _remoteConfig.getString('app_colors_json');
      Map<String, Color?> appColors = {};

      if (configString.isEmpty) {
        return appColors;
      }

      final Map<String, dynamic> decodedJson = _processJsonString(configString);
      if (decodedJson.isEmpty) {
        return AppColors.defaultColors;
      }

      appColors = decodedJson.map((key, value) =>
          MapEntry(key, ColorUtils.hexToColor(value.toString())));
      return appColors;
    } catch (e) {
      return AppColors.defaultColors;
    }
  }

  Map<String, dynamic> get getAppVersionUpdate {
    try {
      final configString = _remoteConfig.getString('version_update');
      final Map<String, dynamic> result = _processJsonString(configString);
      return result.isEmpty ? _getDefaultAppVersionUpdate() : result;
    } catch (e) {
      return _getDefaultAppVersionUpdate();
    }
  }

  /// Get the Typesense config from the environment config
  Map<String, dynamic> get _typesenseConfig {
    final envConfig = _currentEnvConfig;
    if (envConfig.containsKey('typesense')) {
      return envConfig['typesense'];
    } else {
      return _getDefaultTypesenseConfig();
    }
  }

  String get razorpayKey {
    final envConfig = _currentEnvConfig;
    return envConfig['razorpay_key'] ?? 'rzp_test_Jagi7su7aEjQ9P';
  }

  String get omsBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['omsBaseUrl'] ?? '';
  }

  String get imsBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['imsBaseUrl'] ?? '';
  }

  String get walletBaseUrl {
    final envConfig = _currentEnvConfig;
    return envConfig['walletBaseUrl'] ?? '';
  }

  String get walletAPIKey {
    final envConfig = _currentEnvConfig;
    return envConfig['walletAPIKey'] ?? '';
  }

  Map<String, dynamic> get orderStatusConfig {
    try {
      final configString = _currentEnvConfig['order_status_configs'];
      return configString is Map<String, dynamic> ? configString : {};
    } catch (e) {
      return {};
    }
  }

  /// Get default config from the defaults JSON
  Map<String, dynamic> _getDefaultConfig() {
    return {
      "typesense": _getDefaultTypesenseConfig(),
      "razorpay_key": 'rzp_test_Jagi7su7aEjQ9P',
      "omsBaseUrl": '',
      "imsBaseUrl": '',
      "walletBaseUrl": '',
      "walletAPIKey": ''
    };
  }

  /// Get default config from the defaults JSON
  Map<String, dynamic> _getDefaultAppVersionUpdate() {
    return {
      "android": {
        "versionCode": 1,
        "forceUpdate": false,
        "versionNumber": "1.0.0"
      },
      "ios": {"versionCode": 1, "forceUpdate": false, "versionNumber": "1.0.0"}
    };
  }

  /// Get default Typesense config as fallback
  Map<String, dynamic> _getDefaultTypesenseConfig() {
    return {
      "api_key": EnvironmentConfig.typesenseApiKey,
      "host": EnvironmentConfig.typesenseHost,
      "port": EnvironmentConfig.typesensePort,
      "protocol": EnvironmentConfig.typesenseProtocol,
      "collections": {
        "products": "facility_products",
        "categories": "store_categories",
        "stores": "stores",
        "top_products": "top_products"
      }
    };
  }

  /// Get the Typesense API key based on current environment
  String get typesenseApiKey => _typesenseConfig['api_key'];

  /// Get the Typesense host based on current environment
  String get typesenseHost => _typesenseConfig['host'];

  /// Get the Typesense port based on current environment
  String get typesensePort => _typesenseConfig['port'];

  /// Get the Typesense protocol based on current environment
  String get typesenseProtocol => _typesenseConfig['protocol'];

  Map<String, dynamic> get typesenseExtras => _typesenseConfig['extras'] ?? {};

  /// Get collection names
  Map<String, dynamic> get _collections =>
      _typesenseConfig['collections'] ??
      {
        "products": "facility_products",
        "categories": "store_categories",
        "stores": "stores",
        "top_products": "top_products",
        "sliders": "sliders"
      };

  String get productsCollection =>
      _collections['products'] ?? 'facility_products';

  String get categoriesCollection =>
      _collections['categories'] ?? 'store_categories';

  String get storesCollection => _collections['stores'] ?? 'stores';

  String get topProductsCollection =>
      _collections['top_products'] ?? 'top_products';

  String get slidersCollection => _collections['sliders'] ?? 'sliders';
  String get sectionConfig =>
      _collections['section_config'] ?? 'section_config';
}
