import 'dart:io';

import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:url_launcher/url_launcher.dart';

void showUpdateDialog(
  BuildContext context, {
  required int currentVersion,
  required int versionCode,
  String? versionNumber,
  required bool forceUpdate,
}) {
  showDialog(
    context: context,
    barrierDismissible: !forceUpdate,
    builder: (BuildContext context) {
      final Widget dialogContent = Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                child: Lottie.asset(
                  'assets/lotties/update.json',
                  height: 200,
                  fit: BoxFit.fitWidth,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                forceUpdate ? 'Update Required' : 'Update Available',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Text(
                forceUpdate
                    ? 'This version ($versionNumber) is no longer supported.'
                    : 'A new version ($versionNumber) is available.',
              ),
              const SizedBox(height: 8),
              Text('Please update to continue using the app.'),
              const SizedBox(height: 24),
              forceUpdate
                  ? Center(
                      child: ElevatedButton(
                        onPressed: () => _launchAppStore(),
                        child: const Text('Update Now'),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Later'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _launchAppStore();
                          },
                          child: const Text('Update'),
                        ),
                      ],
                    ),
            ],
          ),
        ),
      );

      return forceUpdate
          ? PopScope(
              canPop: false, // Prevent back button dismissal
              child: dialogContent,
            )
          : dialogContent;
    },
  );
}

Future<void> _launchAppStore() async {
  final String storeUrl = Platform.isIOS
      ? 'https://apps.apple.com/app/rozana/id1626867256'
      : 'https://play.google.com/store/apps/details?id=com.user.mobile.rozana';
  final Uri url = Uri.parse(storeUrl);
  if (await canLaunchUrl(url)) {
    await launchUrl(url, mode: LaunchMode.externalApplication);
  } else {
    debugPrint('Could not launch app store URL: $storeUrl');
  }
}
