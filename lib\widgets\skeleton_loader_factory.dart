import 'package:flutter/material.dart';
import 'enhanced_shimmer.dart';
import 'skeleton_colors.dart';

class SkeletonLoaderFactory {
  /// Creates a banner skeleton loader with the specified dimensions.
  static Widget createBannerSkeleton({
    double? width,
    double? height,
    double radius = 8.0,
  }) {
    return RepaintBoundary(
      child: Semantics(
        label: 'Loading banner',
        hint: 'Please wait while banner content loads',
        child: EnhancedShimmerBox(
          width: width,
          height: height,
          radius: radius,
          staggered: false, // No staggered animation - load all at once
        ),
      ),
    );
  }

  /// Creates a category item skeleton loader.
  static Widget createCategorySkeleton({
    double? width,
    double? height,
    double radius = 8.0,
  }) {
    return RepaintBoundary(
      child: Semantics(
        label: 'Loading category',
        hint: 'Please wait while category loads',
        child: EnhancedShimmerBox(
          width: width,
          height: height,
          radius: radius,
          staggered: false, // No staggered animation - load all at once
        ),
      ),
    );
  }

  /// Creates a product card skeleton loader.
  static Widget createProductSkeleton({
    double? width,
    double? height,
    double radius = 10.0,
  }) {
    return RepaintBoundary(
      child: Seman<PERSON>(
        label: 'Loading product',
        hint: 'Please wait while product details load',
        child: EnhancedShimmerBox(
          width: width,
          height: height,
          radius: radius,
          staggered: false, // No staggered animation - load all at once
        ),
      ),
    );
  }

  /// Creates a text skeleton loader with specified dimensions.
  static Widget createTextSkeleton({
    double? width,
    double height = 16.0,
    double radius = 4.0,
  }) {
    return RepaintBoundary(
      child: Semantics(
        label: 'Loading text',
        excludeSemantics:
            true, // Exclude from screen readers as it's just placeholder text
        child: EnhancedShimmerBox(
          width: width,
          height: height,
          radius: radius,
        ),
      ),
    );
  }

  /// Creates a grid of skeleton loaders, useful for product grids.
  static Widget createSkeletonGrid({
    required int itemCount,
    required double itemHeight,
    required double itemWidth,
    required int crossAxisCount,
    double mainAxisSpacing = 12,
    double crossAxisSpacing = 8,
    EdgeInsets padding = EdgeInsets.zero,
  }) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: itemWidth / itemHeight,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) => EnhancedShimmer(
        baseColor: SkeletonColors.baseColor(),
        highlightColor: SkeletonColors.highlightColor(),
        child: Container(
          width: itemWidth,
          height: itemHeight,
          decoration: BoxDecoration(
            color: SkeletonColors.backgroundColor(),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: SkeletonColors.borderColor(),
              width: 1,
            ),
          ),
        ),
      ),
    );
  }

  /// Creates a horizontal list of category skeleton loaders for dashboard top bar.
  static Widget createHorizontalCategoryListSkeleton({
    int itemCount = 9,
    double height = 50,
    double width = 50,
    double spacing = 8,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 8),
  }) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: padding,
      child: Row(
        children: List.generate(
          itemCount,
          (index) => Padding(
            padding:
                EdgeInsets.only(right: index < itemCount - 1 ? spacing : 0),
            child: Column(
              children: [
                EnhancedShimmer(
                  baseColor: SkeletonColors.baseColor(),
                  highlightColor: SkeletonColors.highlightColor(),
                  child: Container(
                    width: width,
                    height: width * 0.8,
                    decoration: BoxDecoration(
                      color: SkeletonColors.backgroundColor(),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 6),
                EnhancedShimmer(
                  baseColor: SkeletonColors.baseColor(),
                  highlightColor: SkeletonColors.highlightColor(),
                  child: Container(
                    width: width * 0.8,
                    height: 10,
                    decoration: BoxDecoration(
                      color: SkeletonColors.backgroundColor(),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Creates an order card skeleton loader
  static Widget createOrderCardSkeleton({
    double width = double.infinity,
    double height = 150,
    double radius = 12,
    EdgeInsets margin = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  }) {
    return RepaintBoundary(
      child: Semantics(
        label: 'Loading order',
        hint: 'Please wait while orders load',
        child: Container(
          width: width,
          height: height,
          margin: margin,
          decoration: BoxDecoration(
            color: SkeletonColors.backgroundColor(),
            borderRadius: BorderRadius.circular(radius),
            boxShadow: [
              BoxShadow(
                color: SkeletonColors.shadowColor(),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order ID and status row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  createTextSkeleton(width: 120, height: 18),
                  createTextSkeleton(width: 80, height: 24, radius: 12),
                ],
              ),
              const SizedBox(height: 12),

              // Date and delivery info row
              Row(
                children: [
                  createTextSkeleton(width: 100, height: 14),
                  const SizedBox(width: 16),
                  createTextSkeleton(width: 120, height: 14),
                ],
              ),
              const SizedBox(height: 12),

              // Order summary row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  createTextSkeleton(width: 80, height: 14),
                  createTextSkeleton(width: 70, height: 16),
                ],
              ),

              const Spacer(),

              // Action buttons row
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  createTextSkeleton(width: 80, height: 36, radius: 18),
                  const SizedBox(width: 12),
                  createTextSkeleton(width: 80, height: 36, radius: 18),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Creates an order details skeleton loader
  static Widget createOrderDetailsSkeleton({
    double width = double.infinity,
    EdgeInsets padding = const EdgeInsets.all(16),
  }) {
    return RepaintBoundary(
      child: Semantics(
        label: 'Loading order details',
        hint: 'Please wait while order details load',
        child: Padding(
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order ID and status section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  createTextSkeleton(width: 150, height: 20),
                  createTextSkeleton(width: 100, height: 28, radius: 14),
                ],
              ),
              const SizedBox(height: 16),

              // Order date and delivery info
              createTextSkeleton(width: 200, height: 16),
              const SizedBox(height: 8),
              createTextSkeleton(width: 180, height: 16),
              const SizedBox(height: 24),

              // Delivery address section
              createTextSkeleton(width: 120, height: 18),
              const SizedBox(height: 8),
              createTextSkeleton(width: width - 32, height: 16),
              const SizedBox(height: 4),
              createTextSkeleton(width: width * 0.7, height: 16),
              const SizedBox(height: 24),

              // Payment method section
              createTextSkeleton(width: 140, height: 18),
              const SizedBox(height: 8),
              Row(
                children: [
                  createCategorySkeleton(width: 40, height: 40, radius: 8),
                  const SizedBox(width: 12),
                  createTextSkeleton(width: 120, height: 16),
                ],
              ),
              const SizedBox(height: 24),

              // Order items section
              createTextSkeleton(width: 120, height: 18),
              const SizedBox(height: 12),

              // Order items list (3 items)
              for (int i = 0; i < 3; i++) ...[
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: i < 2
                      ? BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                                color: SkeletonColors.borderColor(), width: 1),
                          ),
                        )
                      : null,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      createCategorySkeleton(width: 60, height: 60, radius: 8),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            createTextSkeleton(width: 180, height: 16),
                            const SizedBox(height: 4),
                            createTextSkeleton(width: 100, height: 14),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                createTextSkeleton(width: 60, height: 14),
                                createTextSkeleton(width: 70, height: 16),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Order timeline section
              createTextSkeleton(width: 120, height: 18),
              const SizedBox(height: 16),

              // Timeline items (3 steps)
              for (int i = 0; i < 3; i++) ...[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: SkeletonColors.timelineDotColor(),
                            shape: BoxShape.circle,
                          ),
                        ),
                        if (i < 2)
                          Container(
                            width: 2,
                            height: 40,
                            color: SkeletonColors.timelineConnectorColor(),
                          ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          createTextSkeleton(width: 120, height: 16),
                          const SizedBox(height: 4),
                          createTextSkeleton(width: 160, height: 14),
                          SizedBox(height: i < 2 ? 16.0 : 0.0),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 24),

              // Price details section
              createTextSkeleton(width: 120, height: 18),
              const SizedBox(height: 12),

              // Price breakdown
              for (int i = 0; i < 4; i++) ...[
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      createTextSkeleton(width: 120, height: 14),
                      createTextSkeleton(width: 70, height: 14),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: SkeletonColors.borderColor(),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    createTextSkeleton(width: 140, height: 18),
                    createTextSkeleton(width: 80, height: 18),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  createTextSkeleton(width: 140, height: 44, radius: 22),
                  const SizedBox(width: 16),
                  createTextSkeleton(width: 140, height: 44, radius: 22),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
