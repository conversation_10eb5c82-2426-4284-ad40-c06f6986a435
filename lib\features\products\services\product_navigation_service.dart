import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../routes/app_router.dart';

class ProductNavigationService {
  ProductNavigationService._();
  static final ProductNavigationService _instance =
      ProductNavigationService._();
  static ProductNavigationService get instance => _instance;

  final List<String> _productStack = [];

  List<String> get productStack => List.unmodifiable(_productStack);

  void push(String sku) {
    if (_productStack.isNotEmpty && _productStack.last == sku) {
      return;
    }
    _productStack.add(sku);
  }

  void pop() {
    if (_productStack.isNotEmpty) {
      _productStack.removeLast();
    }
  }

  // A method to check if the stack contains a product
  bool contains(String sku) => _productStack.contains(sku);

  void clear() {
    _productStack.clear();
  }
}

class ProductNavigatorObserver extends NavigatorObserver {
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    // We only pop our custom stack if the route being popped was a product detail screen
    // You must use the same route name as in your GoRouter configuration.
    if (route.settings.name == 'productDetail') {
      ProductNavigationService.instance.pop();
    }
    super.didPop(route, previousRoute);
  }
}

class NavigationHelper {
  static void navigateToProduct(
    BuildContext context,
    String destinationSku,
    Map<String, dynamic>? extra,
  ) {
    final GoRouter router = GoRouter.of(context);
    final ProductNavigationService navService =
        ProductNavigationService.instance;
    final GoRouterState state = GoRouterState.of(context);
    final String? currentSku = state.pathParameters['id'];

    if (currentSku != null && currentSku == destinationSku) {
      return;
    }

    // Now, we use our synchronized stack to make the decision
    if (navService.contains(destinationSku)) {
      // Scenario B: Product is already in the stack, so navigate back.
      // This is the "popUntil" logic.
      int routesToPop = navService.productStack.length -
          navService.productStack.indexOf(destinationSku) -
          1;

      // Pop the routes from the GoRouter stack. The observer will automatically
      // update our custom stack in sync.
      for (int i = 0; i < routesToPop; i++) {
        router.pop();
      }
    } else {
      // Scenario A: Product is not in the stack, so push a new route.
      router.pushNamed(
        RouteNames.productDetail,
        pathParameters: {'id': destinationSku},
        extra: extra,
      );

      // Add to our custom history stack
      navService.push(destinationSku);
    }
  }
}
