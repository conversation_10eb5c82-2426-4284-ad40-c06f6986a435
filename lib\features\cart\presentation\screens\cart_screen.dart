import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/cart_analytics_events.dart';
import 'package:rozana/core/services/analytics_events/payment_analytics_events.dart';

import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/data/models/payment_method_model.dart';
import 'package:rozana/features/auth/presentation/views/login_bottom_sheet.dart';
import 'package:rozana/features/cart/presentation/widgets/cart_section_card.dart';
import 'package:rozana/features/cart/presentation/widgets/min_order_info_wrapper.dart';
import 'package:rozana/features/cart/presentation/widgets/payment_confirmation_screen.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/wallet/bloc/wallet_bloc.dart';
import 'package:rozana/features/wallet/bloc/wallet_state.dart';
import 'package:rozana/features/wallet/bloc/wallet_event.dart';
import 'package:rozana/features/cart/bloc/free_product_bloc/free_product_bloc.dart';
import 'package:rozana/features/cart/bloc/free_product_bloc/free_product_event.dart';
import 'package:rozana/features/cart/bloc/free_product_bloc/free_product_state.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_border.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/services/remote_config_service.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/extensions/localization_extension.dart';
import '../../../../core/utils/color_utils.dart';
import '../../../address/bloc/address_bloc.dart';
import '../../../address/presentation/screens/address_selection_bottomsheet.dart';
import '../../../auth/bloc/login_bloc/login_bloc.dart';
import '../../../search/presentation/screens/search_screen.dart';
import '../../../search/services/typesense_service.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/empty_cart.dart';
import '../widgets/free_product_card.dart';
import '../widgets/payment_method_selector.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartBloc _cartBloc = getIt<CartBloc>();
  final WalletBloc _walletBloc = getIt<WalletBloc>();
  final FreeProductBloc _freeProductBloc = getIt<FreeProductBloc>();
  final TextEditingController _couponController = TextEditingController();
  late StreamSubscription<WalletState> _walletSub;
  String _paymentMethod = 'cod';
  bool _useWallet = false;
  double _walletAmount = 0.0;
  double _maxWalletAmount = 0.0;

  @override
  void initState() {
    super.initState();
    themeSettings =
        (RemoteConfigService().getThemeConfig['cart']?.isNotEmpty ?? false)
            ? RemoteConfigService().getThemeConfig['cart']
            : themeSettings;
    _cartBloc.add(CartEvent.init());

    // Track my bag landing page
    CartAnalyticsEvents().trackMyBagLandingPage();

    // Fetch wallet balance when screen initializes
    final appBloc = getIt<AppBloc>();
    if (appBloc.isAuthenticated) {
      _walletBloc.add(FetchWalletBalance());
    }
    num? lastCheckCartTotal;
    // Listen to cart changes to trigger free product checks
    _cartBloc.stream.listen((cartState) {
      final total = cartState.cart.total;
      if (total != null && total > 0) {
        if (total != lastCheckCartTotal && !_freeProductBloc.isClosed) {
          lastCheckCartTotal = total;
          _freeProductBloc.add(FreeProductEvent.checkOffer(
            cartAmount: total.toDouble(),
          ));
        }

        _updateMaxWalletAmount();
      } else {
        lastCheckCartTotal = null;
      }
    });

    // Listen to wallet state changes
    _walletSub = _walletBloc.stream.listen((walletState) {
      if (walletState is WalletLoaded && mounted) {
        setState(() {
          // Update max wallet amount when wallet balance changes
          _updateMaxWalletAmount();
        });
      }
    });

    // Load default address if user is authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (appBloc.isAuthenticated) {
        _cartBloc.add(CartEvent.loadDefaultAddress());
      }
      _searchProductsByCartAmount();
    });
  }

  Map<String, dynamic> _getCartDataForAnalytics() {
    try {
      final cartState = _cartBloc.state;
      final cart = cartState.cart;

      // Get wallet balance
      double walletBalance = 0.0;
      if (_walletBloc.state is WalletLoaded) {
        walletBalance =
            (_walletBloc.state as WalletLoaded).wallet.currentBalance;
      }

      return {
        'numberOfProducts': cart.totalItems,
        'itemsTotal': cart.subTotal?.toString() ?? '0.00',
        'deliveryCharge': cart.deliveryFee?.toString() ?? '0.00',
        'tax': cart.tax?.toString() ?? '0.00',
        'discount': cart.discount?.toString() ?? '0.00',
        'grandTotal': cart.total?.toString() ?? '0.00',
        'freeProductsAdded': 0,
        'deliverySlots': 1,
        'walletBalance': walletBalance,
      };
    } catch (e) {
      debugPrint('Error getting cart data for analytics: $e');
      return {};
    }
  }

  Future<void> _searchProductsByCartAmount() async {
    try {
      final typesenseService = getIt<TypesenseService>();
      final result = await typesenseService.getFreeProductsByCartAmount(
        cartAmount: 3000,
        pageSize: 10,
      );

      if (result != null) {
        final productId = result['productId'] as String?;
        final variantId = result['variantId'] as String?;
        if (productId != null && variantId != null) {
          final products = await typesenseService.searchProducts(
            productId: productId,
            variantId: variantId,
            pageSize: 1,
          );
          debugPrint(products.toString());
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void dispose() {
    _freeProductBloc.close();
    _couponController.dispose();
    _walletSub.cancel();
    super.dispose();
  }

  // Calculate the maximum amount that can be used from wallet
  Future<void> _updateMaxWalletAmount() async {
    // Get current wallet balance
    double walletBalance = 0.0;
    if (_walletBloc.state is WalletLoaded) {
      walletBalance = (_walletBloc.state as WalletLoaded).wallet.currentBalance;
    }

    // Get cart total
    final cartTotal = _cartBloc.state.cart.total ?? 0;

    // Max wallet amount is the smaller of wallet balance and cart total
    _maxWalletAmount =
        walletBalance < cartTotal ? walletBalance : cartTotal.toDouble();

    // Adjust current wallet amount if needed
    if (_useWallet && mounted) {
      setState(() {
        _walletAmount = _maxWalletAmount;
      });
    }
  }

  // Update payment methods in cart
  Future<void> _updatePaymentMethods() async {
    final List<PaymentMethodModel> paymentMethods = [];
    final cartTotal = _cartBloc.state.cart.total ?? 0;
    if (_useWallet && _walletAmount > 0) {
      final actualWalletAmount =
          _walletAmount > cartTotal ? cartTotal : _walletAmount;
      paymentMethods.add(PaymentMethodModel(
        type: 'wallet',
        amount: actualWalletAmount.toDouble(),
      ));
      if (actualWalletAmount >= cartTotal) {
        setState(() {
          _paymentMethod = '';
        });
      } else if (_paymentMethod.isEmpty) {
        setState(() {
          _paymentMethod = 'cod';
        });
      }
    } else if (_paymentMethod.isEmpty) {
      setState(() {
        _paymentMethod = 'cod';
      });
    }

    final remainingAmount = cartTotal - (_useWallet ? _walletAmount : 0);

    if (remainingAmount > 0 && _paymentMethod.isNotEmpty) {
      paymentMethods.add(PaymentMethodModel(
        type: _paymentMethod,
        amount: remainingAmount.toDouble(),
      ));
    }

    await _cartBloc.updateCartPaymentMethods(paymentMethods);
  }

  String _getPaymentMethodIcon() {
    if (_useWallet && _walletAmount > 0) {
      return 'assets/new/icons/account_balance_wallet.png';
    } else {
      return _paymentMethod == 'cod'
          ? 'assets/new/icons/payment-methods.png'
          : 'assets/new/icons/payment-methods_2.png';
    }
  }

  String _getPaymentMethodText() {
    final cartTotal = _cartBloc.state.cart.total ?? 0;
    final walletCoversAll = _useWallet && _walletAmount >= cartTotal;

    if (walletCoversAll) {
      return 'Using Wallet';
    } else if (_useWallet && _walletAmount > 0) {
      if (_paymentMethod == 'cod') {
        return 'Wallet + Cash on delivery';
      } else {
        return 'Wallet + Online payment';
      }
    } else {
      return _paymentMethod == 'cod'
          ? 'Cash on delivery'
          : 'UPI / Credit, Debit card / Netbanking';
    }
  }

  Map<String, dynamic> themeSettings = {
    "background_color": "#F0F0F6",
    "topbar_color": "#FFFFFF",
    // "highlight_color": "#BEABD3",
    // "divider_color": "#5C2C90",
    "icon_primary_color": "#371A56",
    "icon_secondary_color": "#4A2373"
  };

  @override
  Widget build(BuildContext context) {
    final Color iconPrimaryColor =
        ColorUtils.hexToColor(themeSettings['icon_primary_color']) ??
            AppColors.primary700;

    final Color iconSecondaryColor =
        ColorUtils.hexToColor(themeSettings['icon_secondary_color']) ??
            AppColors.primary600;
    return MultiBlocProvider(
      providers: [
        BlocProvider<FreeProductBloc>(create: (context) => _freeProductBloc),
        BlocProvider<WalletBloc>.value(value: _walletBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AppBloc, AppState>(
            listener: (context, state) {
              state.maybeMap(
                loaded: (loaded) {
                  if (loaded.isAuthenticated) {
                    // Load default address when user logs in
                    _cartBloc.add(CartEvent.loadDefaultAddress());
                    _walletBloc.add(FetchWalletBalance());
                  } else {
                    // Clear address when user logs out
                    _cartBloc.add(CartEvent.clearAddress());
                    _walletBloc.add(FetchWalletBalance());
                  }
                },
                orElse: () {},
              );
            },
          ),
          BlocListener<CartBloc, CartState>(
            listenWhen: (previous, current) =>
                previous.orderStatus != current.orderStatus,
            listener: (context, state) {
              // Handle order status changes
              switch (state.orderStatus) {
                case OrderProcessingStatus.success:
                  // Navigate to order success screen
                  if (state.orderId != null && state.orderData != null) {
                    context.go(
                      RouteNames.orderSuccess,
                      extra: {
                        'orderId': state.orderId,
                        'orderData': state.orderData,
                      },
                    );
                  }
                  break;
                case OrderProcessingStatus.verifyingPayment:
                  final bool paymentCancelled = state.orderData != null &&
                      state.orderData!['paymentCancelled'] == true;

                  if (!paymentCancelled) {
                    Navigator.of(context).push(
                      PageRouteBuilder(
                        opaque: true,
                        pageBuilder: (context, _, __) =>
                            PaymentConfirmationScreen(
                          initialState: PaymentAnimationState.processing,
                        ),
                        transitionsBuilder:
                            (context, animation, secondaryAnimation, child) {
                          return FadeTransition(
                              opacity: animation, child: child);
                        },
                      ),
                    );
                  }
                  break;
                case OrderProcessingStatus.error:
                  Navigator.of(context, rootNavigator: true)
                      .popUntil((route) => route.isFirst);

                  if (state.error != null) {
                    final messenger = ScaffoldMessenger.of(context);
                    messenger.hideCurrentSnackBar();
                    messenger.showSnackBar(
                      SnackBar(
                        content: Text('${context.l10n.error}: ${state.error}'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                  break;
                default:
                  break;
              }
            },
          ),
        ],
        child: MinOrderWrapper(
          child: Scaffold(
            backgroundColor:
                ColorUtils.hexToColor(themeSettings['background_color']) ??
                    AppColors.neutral150,
            appBar: AppBar(
              backgroundColor: AppColors.neutral100,
              scrolledUnderElevation: 0,
              elevation: 0,
              centerTitle: false,
              titleSpacing: 0,
              leadingWidth: 0,
              automaticallyImplyLeading: false,
              title: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      try {
                        context.pop();
                      } catch (e) {
                        context.go(RouteNames.home);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                      child: Image.asset(
                        'assets/new/icons/chevron_left.png',
                        height: 26,
                        width: 26,
                        color: iconSecondaryColor,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: CustomText(
                      "Cart",
                      color: iconPrimaryColor,
                      fontSize: 20,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ],
              ),
            ),
            body: BlocBuilder<CartBloc, CartState>(
              buildWhen: (previous, current) =>
                  previous.isLoading != current.isLoading,
              builder: (context, state) {
                if (state.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                return BlocBuilder<CartBloc, CartState>(
                  buildWhen: (previous, current) =>
                      previous.cart.items != current.cart.items,
                  builder: (context, state) {
                    if (state.cart.items?.isEmpty ?? true) {
                      return const EmptyCart();
                    }

                    return _buildCartContentOptimized(context);
                  },
                );
              },
            ),
            bottomNavigationBar: BlocBuilder<CartBloc, CartState>(
              buildWhen: (previous, current) =>
                  previous.cart.items != current.cart.items ||
                  previous.cart.total != current.cart.total ||
                  previous.cart.totalItems != current.cart.totalItems,
              builder: (context, state) {
                if (state.cart.items?.isEmpty ?? true) {
                  return const SizedBox();
                }

                return _buildCheckoutBar(context);
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCartContentOptimized(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                (current.stockStatus != StockAvailableStatus.quantityChecking &&
                    (previous.stockStatus != current.stockStatus)) ||
                (previous.cart.items?.length != current.cart.items?.length),
            builder: (context, state) {
              List<CartItemModel>? availableItems = [];
              List<CartItemModel>? unAvailableItems = [];
              List<CartItemModel>? ondcItems = [];

              state.cart.items?.forEach((item) {
                if ((item.availableQuantity ?? 0) > 0) {
                  if (item.source == 'warehouse') {
                    ondcItems.add(item);
                  } else {
                    availableItems.add(item);
                  }
                } else {
                  unAvailableItems.add(item);
                }
              });
              return Column(
                children: [
                  Visibility(
                    visible:
                        state.stockStatus == StockAvailableStatus.outOfStock &&
                            unAvailableItems.isNotEmpty,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: CartSectionCard(
                        title: CustomText(
                          '${unAvailableItems.length} ${(unAvailableItems.length) > 1 ? "items are" : "item is"} not in stock',
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.red600,
                        ),
                        trailing: InkWell(
                          onTap: () {
                            getIt<CartBloc>().add(CartEvent.removeItem(
                              items: unAvailableItems.map((e) => e).toList(),
                            ));
                            getIt<CartBloc>()
                                .add(CartEvent.checkAvailability());
                          },
                          child: SizedBox(
                            height: 36.sp,
                            width: 36.sp,
                            child: Center(
                              child: Image.asset(
                                'assets/new/icons/cancel.png',
                                height: 24.sp,
                                width: 24.sp,
                                color: AppColors.red600,
                              ),
                            ),
                          ),
                        ),
                        children: [
                          ListView.separated(
                              primary: false,
                              shrinkWrap: true,
                              itemBuilder: (ctx, index) {
                                CartItemModel? item = unAvailableItems[index];
                                return CartItemCard(
                                  key: ValueKey(item.id),
                                  item: item,
                                  isEditable: false,
                                );
                              },
                              separatorBuilder: (ctx, index) => Divider(
                                    thickness: 0.5,
                                    color: AppColors.neutral150,
                                    height: 0.5,
                                  ),
                              itemCount: unAvailableItems.length),
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: availableItems.isNotEmpty,
                    child: CartSectionCard(
                      logo: 'assets/new/icons/moped.png',
                      title: BlocBuilder<AddressBloc, AddressState>(
                        builder: (context, state) {
                          String? durationText = state.delliveryDuration;

                          String displayText = durationText?.isNotEmpty ?? false
                              ? 'Delivery in $durationText'
                              : 'Delivery in 1hr';
                          return CustomText(
                            displayText,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: AppColors.primary700,
                          );
                        },
                      ),
                      subTitle:
                          '${availableItems.length} ${(availableItems.length) > 1 ? "items" : "item"} for delivery',
                      children: [
                        ListView.separated(
                            primary: false,
                            shrinkWrap: true,
                            itemBuilder: (ctx, index) {
                              CartItemModel? item = availableItems[index];
                              return CartItemCard(
                                key: ValueKey(item.id),
                                item: item,
                              );
                            },
                            separatorBuilder: (ctx, index) => Divider(
                                  thickness: 0.5,
                                  color: AppColors.neutral150,
                                  height: 0.5,
                                ),
                            itemCount: availableItems.length),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: ondcItems.isNotEmpty,
                    child: Padding(
                      padding: EdgeInsets.only(top: 12),
                      child: CartSectionCard(
                        logo: 'assets/new/icons/moped.png',
                        title: CustomText(
                          'Next day delivery',
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.primary700,
                        ),
                        subTitle:
                            '${ondcItems.length} ${(ondcItems.length) > 1 ? "items" : "item"} for delivery',
                        children: [
                          ListView.separated(
                              primary: false,
                              shrinkWrap: true,
                              itemBuilder: (ctx, index) {
                                CartItemModel? item = ondcItems[index];
                                return CartItemCard(
                                  key: ValueKey(item.id),
                                  item: item,
                                );
                              },
                              separatorBuilder: (ctx, index) => Divider(
                                    thickness: 0.5,
                                    color: AppColors.neutral150,
                                    height: 0.5,
                                  ),
                              itemCount: ondcItems.length),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),

          // Free Product Selection Section
          BlocBuilder<FreeProductBloc, FreeProductState>(
            builder: (context, freeProductState) {
              if (freeProductState.hasActiveOffer &&
                  freeProductState.freeProducts.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Available Free Products',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.neutral700,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...freeProductState.freeProducts.map(
                      (product) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: FreeProductCard(product: product),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),

          // Selected Free Products Section
          BlocBuilder<FreeProductBloc, FreeProductState>(
            builder: (context, freeProductState) {
              if (freeProductState.selectedFreeProducts.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: CustomBorder(
                        color: AppColors.success.withValues(alpha: 0.1),
                        radius: 8,
                        side: BorderSide(
                          color: AppColors.success.withValues(alpha: 0.3),
                        ),
                        child: SizedBox(
                          width: double.infinity,
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.card_giftcard,
                                      color: AppColors.success,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Your Free Products',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.success,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                ...freeProductState.selectedFreeProducts.map(
                                  (product) => Padding(
                                    padding: const EdgeInsets.only(bottom: 8),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.check_circle,
                                          color: AppColors.success,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            product.name,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: AppColors.neutral700,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          'FREE',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.success,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),

          // const SizedBox(height: 16),

          // Coupon section
          // _buildCouponSection(),

          const SizedBox(height: 16),

          // Cart summary with Selector to update when cart totals change
          BlocBuilder<CartBloc, CartState>(
              buildWhen: (previous, current) => previous.cart != current.cart,
              builder: (context, state) {
                num itemsTotalMRP = 0;
                num itemsSellingPrice = 0;

                state.cart.items?.forEach((e) {
                  itemsTotalMRP =
                      itemsTotalMRP + ((e.price ?? 0) * (e.quantity ?? 1));
                  itemsSellingPrice = itemsSellingPrice +
                      ((e.discountedPrice ?? 0) * (e.quantity ?? 1));
                });

                num itemDiscountedPrice = itemsTotalMRP - itemsSellingPrice;

                return CartSectionCard(
                  logo: 'assets/new/icons/receipt_long.png',
                  titleText: "Bill details",
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      child: Column(
                        children: [
                          BillDetailRow(
                            logo: 'assets/new/icons/local_mall.png',
                            title: 'Items total',
                            customTag: Visibility(
                              visible: (itemDiscountedPrice) > 0,
                              child: Padding(
                                padding: EdgeInsets.only(right: 6),
                                child: CustomBorder(
                                  radius: 4,
                                  color: AppColors.blue100,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 4, vertical: 2),
                                    child: CustomText(
                                      'Saved ₹${formattedPrice(itemsTotalMRP - itemsSellingPrice)}',
                                      fontSize: 10,
                                      fontWeight: FontWeight.w700,
                                      color: AppColors.blue600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            originalPrice: itemsTotalMRP,
                            discountedPrice: itemsSellingPrice,
                          ),
                          BillDetailRow(
                            logo: 'assets/new/icons/moped.png',
                            title: 'Delivery charge',
                            showBaseLine: true,
                            customTag: Visibility(
                              visible: (state.cart.deliveryFee ?? 0) <= 0,
                              child: Padding(
                                padding: EdgeInsets.only(right: 6),
                                child: CustomBorder(
                                  color: AppColors.green100,
                                  radius: 4,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 4, vertical: 2),
                                    child: CustomText(
                                      'Free delivery',
                                      fontSize: 10,
                                      fontWeight: FontWeight.w700,
                                      color: AppColors.green600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            originalPrice: state.cart.deliveryFee,
                            discountedPrice: state.cart.deliveryFee,
                          ),
                          BillDetailRow(
                            logo: 'assets/new/icons/receipt.png',
                            title: 'Taxes',
                            originalPrice: state.cart.tax,
                            discountedPrice: state.cart.tax,
                          ),
                          if (_useWallet)
                            BillDetailRow(
                              logo: 'assets/new/icons/savings.png',
                              title: 'Rozana Money',
                              originalPrice: _walletAmount,
                              discountedPrice: _walletAmount,
                              isSavings: true,
                            ),
                          SizedBox(height: 12),
                        ],
                      ),
                    ),
                    Divider(
                      thickness: 0.5,
                      color: AppColors.neutral150,
                      height: 0.5,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                      child: Row(
                        children: [
                          CustomText(
                            'Grand total',
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            color: AppColors.neutral600,
                            textHeight: 1.4,
                          ),
                          Spacer(),
                          SizedBox(width: 12),
                          CustomText(
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            '₹${formattedPrice((state.cart.total ?? 0) - (_walletAmount))}',
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            textHeight: 1.4,
                            color: AppColors.neutral700,
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: _useWallet,
                      child: Column(
                        children: [
                          Image.asset(
                            'assets/new/images/tide_effect.png',
                            width: double.infinity,
                          ),
                          ColoredBox(
                            color: AppColors.blue100,
                            child: Padding(
                              padding: EdgeInsets.fromLTRB(
                                  12.sp, 8.sp, 12.sp, 12.sp),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomText(
                                          'Your total savings',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w700,
                                          color: AppColors.blue500,
                                        ),
                                      ),
                                      CustomText(
                                        '₹${formattedPrice(itemDiscountedPrice + _walletAmount)}',
                                        fontSize: 14,
                                        fontWeight: FontWeight.w700,
                                        color: AppColors.blue600,
                                      ),
                                    ],
                                  ),
                                  CustomText(
                                    'Includes ₹$_walletAmount savings using rozana money',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.neutral500,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                );
              }),

          const SizedBox(height: 12),
          BlocProvider.value(
            value: _walletBloc,
            child: BlocBuilder<WalletBloc, WalletState>(
              builder: (context, walletState) {
                // Get wallet state directly from the instance
                // final walletState = _walletBloc.state;
                double walletBalance = 0.0;
                bool showWallet = false;

                if (walletState is WalletLoaded) {
                  walletBalance = walletState.wallet.currentBalance;
                  showWallet = walletBalance > 0;
                } else {
                  walletBalance = 0;
                  showWallet = false;
                }

                return BlocBuilder<CartBloc, CartState>(
                  builder: (context, cartState) {
                    bool hasOndc = (cartState.cart.items ?? [])
                        .any((e) => (e.source == 'warehouse'));
                    showWallet = hasOndc ? false : showWallet;
                    return CartSectionCard(
                      logo: 'assets/new/icons/receipt_long.png',
                      titleText: "Pay using",
                      children: [
                        PaymentMethodSelector(
                          selectedMethod: _paymentMethod,
                          onMethodSelected: (method) {
                            setState(() {
                              _paymentMethod = method;
                            });
                            _updatePaymentMethods();
                          },
                          cartData: _getCartDataForAnalytics(),
                          showWalletOption: showWallet,
                          walletBalance: walletBalance,
                          hasOndc: hasOndc,
                        ),

                        // Wallet payment option (only shown if wallet has balance)
                        if (showWallet)
                          ColoredBox(
                            color: AppColors.yellow100,
                            child: Padding(
                              padding: EdgeInsets.only(
                                  left: 12.sp,
                                  right: 12.sp,
                                  top: 12.sp,
                                  bottom: 12.sp),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      SizedBox.square(
                                        dimension: 20.sp,
                                        child: Image.asset(
                                          'assets/new/icons/savings.png',
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            CustomText(
                                              'Use Rozana Money',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w700,
                                              color: AppColors.neutral600,
                                            ),
                                            Text.rich(
                                              TextSpan(
                                                  text: 'Balance: ',
                                                  children: [
                                                    TextSpan(
                                                      text:
                                                          '₹${walletBalance.toStringAsFixed(2)}',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w700,
                                                      ),
                                                    ),
                                                  ]),
                                              style: TextStyle(
                                                fontFamily: 'Mukta',
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.neutral500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          setState(() {
                                            _useWallet = !_useWallet;
                                            if (_useWallet) {
                                              _walletAmount = _maxWalletAmount;
                                              final cartTotal =
                                                  _cartBloc.state.cart.total ??
                                                      0;
                                              if (_walletAmount >= cartTotal) {
                                                _paymentMethod = '';
                                              }
                                            } else {
                                              _walletAmount = 0;
                                              if (_paymentMethod.isEmpty) {
                                                _paymentMethod = 'cod';
                                              }
                                            }
                                          });
                                          _updatePaymentMethods();
                                        },
                                        child: Container(
                                          width: 40,
                                          height: 24,
                                          margin: EdgeInsets.only(
                                              top: 8, bottom: 8),
                                          padding: EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                            color: _useWallet
                                                ? AppColors.primary
                                                : AppColors.neutral200,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          alignment: Alignment.centerLeft,
                                          child: AnimatedPadding(
                                            padding: EdgeInsetsGeometry.only(
                                                left: _useWallet ? 16 : 0),
                                            duration:
                                                Duration(milliseconds: 300),
                                            child: CircleAvatar(
                                              radius: 10,
                                              backgroundColor:
                                                  AppColors.neutral100,
                                              child: Visibility(
                                                visible: _useWallet,
                                                child: Center(
                                                  child: Image.asset(
                                                    'assets/new/icons/check-tick.png',
                                                    height: 16,
                                                    width: 16,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  if (_useWallet) ...[
                                    if (_walletAmount <
                                        (_cartBloc.state.cart.total ?? 0))
                                      Padding(
                                        padding: EdgeInsets.only(
                                            left: 31, right: 12, top: 4),
                                        child: CustomText(
                                          'Remaining amount will be paid via ${_paymentMethod == 'cod' ? 'Cash on Delivery' : 'Online Payment'}',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.neutral500,
                                          maxLines: 2,
                                        ),
                                      ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutBar(BuildContext context) {
    return BlocBuilder<AddressBloc, AddressState>(
      buildWhen: (previous, current) =>
          previous.selectedAddress != current.selectedAddress,
      builder: (context, locationState) {
        bool isLocationLoading = locationState.maybeMap(
            orElse: () => false, loading: (value) => true);
        return BlocBuilder<AppBloc, AppState>(
          builder: (context, appState) {
            return BlocBuilder<CartBloc, CartState>(
              builder: (context, cartState) {
                final isAuthenticated = appState.maybeMap(
                  loaded: (loaded) => loaded.isAuthenticated,
                  orElse: () => false,
                );

                AddressModel? address = (locationState.isServicable)
                    ? locationState.selectedAddress
                    : null;

                StockAvailableStatus stockStatus = cartState.stockStatus;

                // Determine button text and action based on authentication, address status and order processing
                String buttonText;
                VoidCallback? onPressed;

                if (isLocationLoading && address?.id == null) {
                  buttonText = 'Fetching address...';
                  onPressed = null;
                } else if (cartState.orderStatus ==
                    OrderProcessingStatus.processing) {
                  buttonText = 'Processing...';
                  onPressed = null;
                } else if (!isAuthenticated) {
                  buttonText = 'Login to proceed';
                  // context.l10n.loginToProceed;
                  onPressed = () async {
                    // Log initiated checkout event to AppsFlyer
                    await AppsFlyerEvents.initiatedCheckout(
                      cartItems: cartState.cart.items,
                      totalPrice: cartState.cart.total?.toDouble(),
                    );
                    _placeOrder(cartState);
                  };
                } else if (address?.id == null) {
                  buttonText = 'Select address at next step';
                  // context.l10n.selectAddress;
                  onPressed = () async {
                    // Log initiated checkout event to AppsFlyer
                    await AppsFlyerEvents.initiatedCheckout(
                      cartItems: cartState.cart.items,
                      totalPrice: cartState.cart.total?.toDouble(),
                    );
                    // await _navigateToAddressSelection();
                    getIt<AddressBloc>().add(const AddressEvent.fetchAddresses(
                        filterServiceStatus: true));

                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (context) => const AddressSelectionBottomSheet(),
                    );
                  };
                } else if ((getIt<TypesenseService>().minPurchaseAmount ?? 0) >
                    (cartState.cart.total ?? 0)) {
                  buttonText = 'Add More';
                  onPressed = null;
                } else {
                  buttonText = context.l10n.placeOrder;
                  onPressed = () => _placeOrder(cartState);
                }

                return Container(
                  decoration: BoxDecoration(
                    color: AppColors.neutral100,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.06),
                        blurRadius: 2,
                        spreadRadius: 0,
                        offset: const Offset(0, -1),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Visibility(
                        visible: isAuthenticated && (address?.id != null),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.fromLTRB(16, 10, 16, 8),
                              child: Row(
                                children: [
                                  Image.asset(
                                    (address?.addressType == 'home')
                                        ? 'assets/new/icons/house.png'
                                        : (address?.addressType == 'work')
                                            ? 'assets/new/icons/corporate_fare.png'
                                            : 'assets/new/icons/pin_drop.png',
                                    width: 20,
                                    height: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            CustomText(
                                              'Delivering to',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color: AppColors.neutral600,
                                              textHeight: 1.4,
                                            ),
                                            SizedBox(width: 3),
                                            CustomText(
                                              capitalizeFirstLetter(
                                                  address?.addressType ??
                                                      'Address'),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700,
                                              color: AppColors.primary700,
                                            ),
                                          ],
                                        ),
                                        CustomText(
                                          address?.fullAddress ?? '',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: AppColors.neutral500,
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  TextButton(
                                    onPressed: () {
                                      HapticFeedback.mediumImpact();

                                      // Track go to payment change address selected analytics
                                      final cartData =
                                          _getCartDataForAnalytics();
                                      PaymentAnalyticsEvents()
                                          .trackGoToPaymentChangeAddressSelected(
                                        numberOfProducts:
                                            cartData['numberOfProducts'] ?? 0,
                                        itemsTotal:
                                            cartData['itemsTotal'] ?? '0',
                                        deliveryCharge:
                                            cartData['deliveryCharge'] ?? '0',
                                        tax: cartData['tax'] ?? '0',
                                        discount: cartData['discount'] ?? '0',
                                        grandTotal:
                                            cartData['grandTotal'] ?? '0',
                                        freeProductsAdded:
                                            cartData['freeProductsAdded'] ?? 0,
                                        currentAddress: address?.fullAddress ??
                                            'No address selected',
                                      );

                                      // _navigateToAddressSelection();
                                      getIt<AddressBloc>().add(
                                          const AddressEvent.fetchAddresses(
                                              filterServiceStatus: true));
                                      showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        builder: (context) =>
                                            const AddressSelectionBottomSheet(),
                                      );
                                    },
                                    style: ButtonStyle(
                                        padding: WidgetStatePropertyAll(
                                            EdgeInsets.fromLTRB(
                                                16, 10, 0, 10))),
                                    child: IntrinsicWidth(
                                      child: Column(
                                        children: [
                                          CustomText(
                                            'Change',
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                            color: AppColors.primary600,
                                            textHeight: 1.6,
                                          ),
                                          SizedBox(
                                            width: double.infinity,
                                            child: DottedLine(
                                              color: AppColors.primary600,
                                              height: 1.5,
                                              dashWidth: 4,
                                              dashSpace: 4,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(height: 8),
                            Divider(
                              thickness: 0.5,
                              color: AppColors.neutral150,
                              height: 0.5,
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
                        child: Row(
                          children: [
                            Visibility(
                              visible: isAuthenticated && (address?.id != null),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                    maxWidth:
                                        MediaQuery.of(context).size.width *
                                            0.35),
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Image.asset(
                                            _getPaymentMethodIcon(),
                                            height: 24,
                                            width: 34,
                                          ),
                                          SizedBox(width: 4),
                                          CustomText(
                                            'PAY USING',
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.neutral500,
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 4),
                                      CustomText(
                                        _getPaymentMethodText(),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.neutral700,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: (stockStatus ==
                                            StockAvailableStatus.inStock ||
                                        !(isAuthenticated) ||
                                        (address?.id == null))
                                    ? onPressed
                                    : null,
                                child: CustomBorder(
                                  radius: 11,
                                  color: !isAuthenticated
                                      ? AppColors.primary
                                      : ((address?.id == null &&
                                              !isLocationLoading)
                                          ? AppColors.primary
                                          : (((stockStatus ==
                                                      StockAvailableStatus
                                                          .inStock) &&
                                                  onPressed != null)
                                              ? AppColors.primary
                                              : AppColors.primary100)),
                                  child: Container(
                                    height: 48.sp,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 5),
                                    child: Row(
                                      children: [
                                        Visibility(
                                          visible: isAuthenticated &&
                                              (address?.id != null),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              CustomText(
                                                '₹${((cartState.cart.total ?? 0) - _walletAmount).toStringAsFixed(2)}',
                                                fontSize: 16,
                                                fontWeight: FontWeight.w700,
                                                color: AppColors.neutral100,
                                                textHeight: 1.2,
                                              ),
                                              CustomText(
                                                'Total',
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.primary100,
                                                textHeight: 1.2,
                                              ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          child: Center(
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                CustomText(
                                                  buttonText,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors.neutral150,
                                                ),
                                                Icon(
                                                  Icons.arrow_right,
                                                  color: AppColors.neutral150,
                                                )
                                              ],
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  // Future<void> _navigateToAddressSelection() async {
  //   // Check if user has any existing addresses
  //   final addressService = AddressService();
  //   final addresses = await addressService.getAllAddresses();

  //   if (!mounted) return;

  //   if (addresses.isEmpty) {
  //     // No addresses exist, go directly to add address form
  //     final newAddress = await context.push<AddressModel>(
  //       RouteNames.mapForNewAddress,
  //       extra: {'fromCart': true},
  //     );

  //     // After returning from address form, select the new address
  //     if (mounted && newAddress != null) {
  //       _cartBloc.add(CartEvent.selectAddress(newAddress));
  //     }
  //   } else {
  //     final selectedAddress = await context.push<AddressModel>(
  //       RouteNames.addresses,
  //       extra: {
  //         'selectMode': true,
  //         'onAddressSelected': (AddressModel address) {
  //           context.pop(address);
  //         },
  //       },
  //     );

  //     if (mounted && selectedAddress != null) {
  //       _cartBloc.add(CartEvent.selectAddress(selectedAddress));
  //     }
  //   }
  // }

  void _showLoginBottomSheet(BuildContext context) {
    final LoginBloc loginBloc = getIt<LoginBloc>();
    loginBloc.add(LoginEvent.initLogin(showHint: false));

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (ctx) {
        return BlocListener<AppBloc, AppState>(
          listenWhen: (previous, current) =>
              previous.maybeMap(
                  orElse: () => false,
                  loaded: (value) => value.isAuthenticated) !=
              current.maybeMap(
                  orElse: () => false,
                  loaded: (value) => value.isAuthenticated),
          listener: (context, state) {
            state.mapOrNull(
              loaded: (value) {
                if (value.isAuthenticated) {
                  context.pop();
                }
              },
            );
          },
          child: BlocProvider.value(
            value: loginBloc,
            child: LoginBottomSheet(),
          ),
        );
      },
    ).then((_) {
      loginBloc.close();
      LoginBloc.cleanupSmsResources();
    });
  }

  // Trigger order placement through BLoC
  void _placeOrder(CartState state) {
    if (state.cart.items?.isEmpty ?? true) return;

    // Check if user is authenticated before proceeding with checkout
    final appBloc = getIt<AppBloc>();
    if (!appBloc.isAuthenticated) {
      _showLoginBottomSheet(context);
      return;
    }

    // Track go to payment button clicked analytics
    final cartData = _getCartDataForAnalytics();
    PaymentAnalyticsEvents().trackGoToPaymentButtonClicked(
      numberOfProducts: cartData['numberOfProducts'] ?? 0,
      itemsTotal: cartData['itemsTotal'] ?? '0',
      deliveryCharge: cartData['deliveryCharge'] ?? '0',
      tax: cartData['tax'] ?? '0',
      discount: cartData['discount'] ?? '0',
      grandTotal: cartData['grandTotal'] ?? '0',
      freeProductsAdded: cartData['freeProductsAdded'] ?? 0,
      deliverySlots: cartData['deliverySlots'] ?? 1,
      paymentMethodSelected: _getPaymentMethodDisplayName(_paymentMethod),
      walletBalance: cartData['walletBalance']?.toString() ?? '0',
    );

    // Update payment methods before placing order
    _updatePaymentMethods().then((_) {
      // Dispatch place order event to the BLoC
      _cartBloc.add(CartEvent.placeOrder(
          paymentMethod: _paymentMethod,
          freeBie: _freeProductBloc.state.selectedFreeProducts));
    });
  }

  String _getPaymentMethodDisplayName(String paymentMethod) {
    switch (paymentMethod) {
      case 'cod':
        return 'Cash on Delivery';
      case 'razorpay':
        return 'Online Payment';
      case 'wallet':
        return 'Wallet';
      default:
        return paymentMethod;
    }
  }
}

class BillDetailRow extends StatelessWidget {
  const BillDetailRow({
    super.key,
    required this.logo,
    required this.title,
    this.customTag,
    required this.originalPrice,
    required this.discountedPrice,
    this.margin,
    this.bottomWidget,
    this.showBaseLine = false,
    this.isSavings = false,
  });

  final String logo;
  final String title;
  final Widget? customTag;
  final num? originalPrice;
  final num? discountedPrice;
  final EdgeInsetsGeometry? margin;
  final Widget? bottomWidget;
  final bool showBaseLine;
  final bool isSavings;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? const EdgeInsets.only(top: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SizedBox.square(
                      dimension: 14.sp,
                      child: Image.asset(
                        logo,
                        color: AppColors.neutral600,
                      ),
                    ),
                    SizedBox(width: 6),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          title,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral600,
                          textHeight: 1.4,
                        ),
                        Visibility(
                          visible: showBaseLine,
                          child: SizedBox(
                            width: 95,
                            child: DottedLine(
                              color: AppColors.neutral600,
                              height: 1.5,
                              dashWidth: 1.5,
                              dashSpace: 2,
                            ),
                          ),
                        )
                      ],
                    ),
                    SizedBox(width: 6),
                    customTag ?? SizedBox(),
                  ],
                ),
                bottomWidget ?? SizedBox()
              ],
            ),
          ),
          SizedBox(width: 12),
          Row(
            children: [
              Visibility(
                visible: (originalPrice ?? 0) > (discountedPrice ?? 0),
                child: IntrinsicWidth(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CustomText(
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        '₹${formattedPrice(originalPrice)}',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textHeight: 1.4,
                        color: AppColors.neutral400,
                      ),
                      Transform.rotate(
                        angle: 2.9,
                        child: Container(
                          height: 1,
                          width: double.infinity,
                          color: AppColors.neutral400,
                        ),
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(width: 6),
              CustomText(
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                isSavings
                    ? '- ₹${formattedPrice(discountedPrice)}'
                    : '₹${formattedPrice(discountedPrice)}',
                fontSize: 14,
                fontWeight: FontWeight.w700,
                textHeight: 1.4,
                color: AppColors.neutral700,
              ),
              SizedBox(width: 2),
            ],
          ),
        ],
      ),
    );
  }
}

String formattedPrice(num? price) {
  String formattedPrice;
  if (price != null) {
    // Always format to two decimal places first
    String priceString = price.toStringAsFixed(2);

    // Check if the formatted string ends with ".00"
    if (priceString.endsWith('.00')) {
      // If it does, remove the ".00" part
      formattedPrice = priceString.substring(0, priceString.length - 3);
    } else {
      // Otherwise, use the original formatted string
      formattedPrice = priceString;
    }
  } else {
    formattedPrice = '0'; // Or some default value
  }

  return formattedPrice;
}

String capitalizeFirstLetter(String text) {
  if (text.isEmpty) {
    return text;
  }
  return text[0].toUpperCase() + text.substring(1);
}
