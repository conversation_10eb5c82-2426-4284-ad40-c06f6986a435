export 'home_event.dart';
export 'home_state.dart';

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/services/analytics_events/home_analytics_events.dart';
import 'package:rozana/core/services/appflyer_services/app_flyer_deeplink.dart';
import 'package:rozana/data/mappers/product_mapper.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/domain/usecases/get_categories_usecase.dart';
import '../../../../app/bloc/app_bloc.dart';
import '../../../../core/services/appflyer_services/app_flyers_services.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../domain/usecases/get_banners_usecase.dart';
import '../../../../domain/usecases/get_order_history_usecase.dart';
import '../../../../domain/usecases/get_products_usecase.dart';
import '../../../../domain/usecases/get_sub_categories_usecase.dart';

import '../../../search/services/typesense_service.dart';
import 'home_event.dart';
import 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetSubCategoriesUseCase _getSubCategoriesUseCase;
  final GetProductsUseCase _getProductsUseCase;
  final GetBannersUseCase _getBannersUseCase;
  final GetCategoriesUseCase _getcategoriesUseCase;
  final GetOrderHistoryUseCase _getOrderHistoryUseCase;


  static ScrollController? scrollController;

  static List<GlobalKey>? categoryKeys;

  static int dashboardPage = 1;
  static int ondcPage = 1;
  static bool hasMoreDashboardData = true;
  static bool hasMoreOndcData = true;
  static bool isPaginationLoading = false;

  HomeBloc({
    required GetSubCategoriesUseCase getSubCategoriesUseCase,
    required GetProductsUseCase getProductsUseCase,
    required GetBannersUseCase getBannersUseCase,
    required GetCategoriesUseCase categoriesUseCase,
    required GetOrderHistoryUseCase getOrderHistoryUseCase,
  })  : _getSubCategoriesUseCase = getSubCategoriesUseCase,
        _getProductsUseCase = getProductsUseCase,
        _getBannersUseCase = getBannersUseCase,
        _getcategoriesUseCase = categoriesUseCase,
        _getOrderHistoryUseCase = getOrderHistoryUseCase,
        super(const HomeState.initial()) {
    scrollController = ScrollController();
    // Initial state is now a const factory constructor
    on<InitHome>(_onInitHome);
    on<UpdateScroll>(_onUpdateScroll);
    on<LoadHomeData>(_onLoadHomeData);
    on<UpdateHomeList>(_onUpdateHomeList);
    on<LoadDeepLink>(_onDeepLinkFound);
    on<ScrollDirectionChanged>(_onBottomScrollChanged);
    on<SwitchCategory>(_onSwitchCategory);
    on<LoadMoreData>(_onLoadMoreData);
  }

  void _onInitHome(InitHome event, Emitter<HomeState> emit) async {
    try {
      _handleDeepLink();
      scrollController?.addListener(() {
        final scrolled = (scrollController?.offset ?? 0) > 350;
        // if (scrolled != state.isScrolled) {
        add(UpdateScroll(scrolled, scrollController?.offset ?? 0));
        if (!isPaginationLoading) {
          if ((scrollController?.offset ?? 0) >=
              ((scrollController?.position.maxScrollExtent ?? 0) - 200)) {
            isPaginationLoading = true;
            if (state.selectedIndex == 0 ||
                (state.maybeMap(
                  orElse: () => false,
                  loaded: (value) =>
                      state.selectedIndex >
                      ((value.categorySections?.length ?? 0) - 1),
                ))) {
              add(HomeEvent.loadMoreData(state.selectedIndex));
            }
          }
        }
        // }
      });
      add(HomeEvent.loadHomeData());

      // Analytics tracking is handled when user actually selects a category
    } catch (_) {}
  }

  void _onDeepLinkFound(LoadDeepLink event, Emitter<HomeState> emit) async {
    try {
      emit(HomeState.deepLink(
          isScrolled: state.isScrolled, route: event.route, args: event.args));
    } catch (_) {}
  }

  void _onUpdateScroll(UpdateScroll event, Emitter<HomeState> emit) {
    emit(state.copyWith(isScrolled: event.scroll, scrollOffset: event.offset));
  }

  void _onSwitchCategory(SwitchCategory event, Emitter<HomeState> emit) {
    // Track landing page selected
    HomeAnalyticsEvents().trackLandingPageSelected(
      homepageSection: event.category?.name ?? 'My Deals',
    );

    if (state is HomeLoaded) {
      HomeLoaded loadedState = state as HomeLoaded;
      emit(loadedState.copyWith(
          selectedCategory: event.category ??
              CategoryEntity(
                  id: 'My Deals',
                  categoryId: 'My Deals',
                  name: 'My Deals',
                  collectionId: 'My Deals'),
          selectedIndex: event.index,
          banners: null));
      _getBannersUseCase
          .execute(level: event.category?.name ?? 'My Deals')
          .then((value) {
        add(HomeEvent.updateLoadedList(banners: value));
      });


    } else {
      emit(state.copyWith(
          selectedCategory: event.category ??
              CategoryEntity(
                  id: 'My Deals',
                  categoryId: 'My Deals',
                  name: 'My Deals',
                  collectionId: 'My Deals'),
          selectedIndex: event.index));

      // Analytics tracking is handled in the if branch above
    }
  }



  void _onBottomScrollChanged(
      ScrollDirectionChanged event, Emitter<HomeState> emit) {
    final currentVisibility = state.showBottomNavBar;
    if (event.direction == ScrollDirection.reverse && currentVisibility) {
      emit(state.copyWith(showBottomNavBar: false));
    } else if (event.direction == ScrollDirection.forward &&
        !currentVisibility) {
      emit(state.copyWith(showBottomNavBar: true));
    }
  }

  Future<void> _onLoadHomeData(
    LoadHomeData event, // LoadHomeData is now a class, not just a type
    Emitter<HomeState> emit,
  ) async {
    try {
      // Start all futures without awaiting
      final categoriesSectionFuture =
          _getcategoriesUseCase.execute(secComponent: 'category');
      final categoriesFuture =
          _getSubCategoriesUseCase.execute(level: 'category', pageSize: 100);
      final subCategoriesFuture =
          _getcategoriesUseCase.execute(secComponent: 'BTS');

      final mostBoughtFuture = _getProductsUseCase.execute(
          query: '', sectionType: 'bsp', pageSize: 12, page: 1, dynamic: false);
      final bannersFuture =
          _getBannersUseCase.execute(level: state.selectedCategory.name);

      Map<String, dynamic> userData = jsonDecode(
          (AppPreferences.getUserdata()?.isNotEmpty ?? false)
              ? AppPreferences.getUserdata()!
              : '{}');
      bool isDistributor = userData['userType'] == 'distributor';
      final ondcItemsFuture = isDistributor
          ? _getProductsUseCase.execute(
              query: '',
              sectionType: '',
              pageSize: 12,
              page: 1,
              dynamic: false,
              source: 'warehouse',
            )
          : Future.delayed(Duration.zero, () => <ProductEntity>[]);

      // Only call getPreviousOrders if user is authenticated to prevent token refresh errors
      Future<List<dynamic>>? ordersFuture;
      if (getIt<AppBloc>().isAuthenticated) {
        ordersFuture = _getOrderHistoryUseCase.getPreviousOrders();
      } else {
        add(HomeEvent.updateLoadedList(previouslyBought: []));
      }

      // Keep current progressive data
      List<CategoryEntity>? categorySection;
      List<CategoryEntity>? categories;
      List<CategoryEntity>? subCategories;
      List<ProductEntity>? mostBought;
      List<BannerEntity>? banners;
      List<ProductEntity>? ondcItems;

      ordersFuture?.then((value) {
        getIt<TypesenseService>()
            .searchProducts(
          query: '*',
          page: 1,
          pageSize: value.length,
          skus: value.cast<String>(),
        )
            .then((items) {
          List<ProductEntity>? list = items
              .map((e) => ProductMapper.toEntity(ProductModel.fromJson(e)))
              .toList();
          add(HomeEvent.updateLoadedList(previouslyBought: list));
        });
      });

      categoriesSectionFuture.then((value) {
        categorySection = value;
        add(HomeEvent.updateLoadedList(
            categorySections: categorySection ?? []));
      });

      categoriesFuture.then((value) {
        categories = value;

        add(HomeEvent.updateLoadedList(categories: categories ?? []));
      });
      subCategoriesFuture.then((value) {
        subCategories = value;

        add(HomeEvent.updateLoadedList(subCategories: subCategories ?? []));
      });

      mostBoughtFuture.then((value) {
        mostBought = value;
        add(HomeEvent.updateLoadedList(mostBought: mostBought ?? []));
      });

      bannersFuture.then((value) {
        banners = value;
        add(HomeEvent.updateLoadedList(banners: banners ?? []));
      });

      ondcItemsFuture.then((value) {
        ondcItems = value;
        add(HomeEvent.updateLoadedList(ondcItems: ondcItems ?? []));
      });
    } catch (e) {
      emit(HomeState.error(
          message: 'Failed to load home data: $e',
          isScrolled: state.isScrolled)); // Emit const factory constructor
    }
  }

  Future<void> _onLoadMoreData(
    LoadMoreData event, // LoadHomeData is now a class, not just a type
    Emitter<HomeState> emit,
  ) async {
    try {
      if (event.index == 0) {
        dashboardPage++;
        final mostBoughtFuture = _getProductsUseCase.execute(
            query: '',
            sectionType: 'bsp',
            pageSize: 24,
            page: dashboardPage,
            dynamic: false);

        List<ProductEntity>? mostBought;

        mostBoughtFuture.then((value) {
          if (value.isEmpty) {
            hasMoreDashboardData = false;
          } else {
            mostBought = state.maybeMap(
                loaded: (loaded) => [...loaded.mostBought ?? [], ...value],
                orElse: () => []);

            add(HomeEvent.updateLoadedList(mostBought: mostBought ?? []));
          }
        });
      } else {
        ondcPage++;
        final ondcItemsFuture = _getProductsUseCase.execute(
            query: '',
            sectionType: '',
            pageSize: 24,
            page: ondcPage,
            source: 'warehouse',
            dynamic: false);

        List<ProductEntity>? ondcItems;

        ondcItemsFuture.then((value) {
          if (value.isEmpty) {
            hasMoreOndcData = false;
          } else {
            ondcItems = state.maybeMap(
                loaded: (loaded) => [...loaded.ondcItems ?? [], ...value],
                orElse: () => []);

            add(HomeEvent.updateLoadedList(ondcItems: ondcItems ?? []));
          }
        });
      }
    } catch (e) {
      emit(HomeState.error(
          message: 'Failed to load home data: $e',
          isScrolled: state.isScrolled)); // Emit const factory constructor
    }
  }

  void _onUpdateHomeList(UpdateHomeList event, Emitter<HomeState> emit) {
    if (state is HomeLoaded) {
      HomeLoaded loadedState = state as HomeLoaded;
      emit(loadedState.copyWith(
        categorySections:
            event.categorySections ?? loadedState.categorySections,
        categories: event.categories ?? loadedState.categories,
        subCategories: event.subCategories ?? loadedState.subCategories,
        mostBought: event.mostBought ?? loadedState.mostBought,
        previouslyBought:
            event.previouslyBought ?? loadedState.previouslyBought,
        banners: event.banners ?? loadedState.banners,
        ondcItems: event.ondcItems ?? loadedState.ondcItems,
      ));
    } else {
      emit(HomeState.loaded(
        selectedCategory: CategoryEntity(
            id: 'My Deals',
            categoryId: 'My Deals',
            name: 'My Deals',
            collectionId: 'My Deals'),
        selectedIndex: 0,
        categorySections: event.categorySections,
        categories: event.categories,
        subCategories: event.subCategories,
        mostBought: event.mostBought,
        previouslyBought: event.previouslyBought,
        banners: event.banners,
        isScrolled: state.isScrolled,
        scrollOffset: state.scrollOffset,
        ondcItems: event.ondcItems,
      ));
    }
    isPaginationLoading = false;
  }

  void _handleDeepLink() {
    String? pendingLink = AppsFlyerServices.pendingLink;
    if (pendingLink != null) {
      add(HomeEvent.deepLinkFound(
          pendingLink, AppsFlyerServices.pendingLinkArguments ?? {}));
      // Clear the pending link after processing to prevent re-triggering
      AppsFlyerServices.pendingLink = null;
      AppsFlyerServices.pendingLinkArguments = null;
    } else {
      AppFlyerDeeplink.onDeepLink((route, args) {
        add(HomeEvent.deepLinkFound(route, args));
      });
    }
  }

  @override
  Future<void> close() {
    scrollController?.dispose();
    return super.close();
  }
}
