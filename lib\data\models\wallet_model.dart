class WalletModel {
  final String userId;
  final double currentBalance;

  WalletModel({
    required this.userId,
    required this.currentBalance,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      userId: json['user_id'] ?? '',
      currentBalance: _parseBalance(json['current_balance']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'current_balance': currentBalance,
    };
  }

  static double _parseBalance(dynamic balance) {
    if (balance == null) return 0.0;
    
    if (balance is num) {
      return balance.toDouble();
    }
    
    try {
      return double.parse(balance.toString());
    } catch (_) {
      return 0.0;
    }
  }
}
