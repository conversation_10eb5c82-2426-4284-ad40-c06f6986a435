// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_update_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppUpdateState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AppUpdateState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppUpdateState()';
  }
}

/// @nodoc
class $AppUpdateStateCopyWith<$Res> {
  $AppUpdateStateCopyWith(AppUpdateState _, $Res Function(AppUpdateState) __);
}

/// Adds pattern-matching-related methods to [AppUpdateState].
extension AppUpdateStatePatterns on AppUpdateState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AppUpdateInitial value)? initial,
    TResult Function(UpdateAvailable value)? updateAvailable,
    TResult Function(NoUpdate value)? noUpdate,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case AppUpdateInitial() when initial != null:
        return initial(_that);
      case UpdateAvailable() when updateAvailable != null:
        return updateAvailable(_that);
      case NoUpdate() when noUpdate != null:
        return noUpdate(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AppUpdateInitial value) initial,
    required TResult Function(UpdateAvailable value) updateAvailable,
    required TResult Function(NoUpdate value) noUpdate,
  }) {
    final _that = this;
    switch (_that) {
      case AppUpdateInitial():
        return initial(_that);
      case UpdateAvailable():
        return updateAvailable(_that);
      case NoUpdate():
        return noUpdate(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AppUpdateInitial value)? initial,
    TResult? Function(UpdateAvailable value)? updateAvailable,
    TResult? Function(NoUpdate value)? noUpdate,
  }) {
    final _that = this;
    switch (_that) {
      case AppUpdateInitial() when initial != null:
        return initial(_that);
      case UpdateAvailable() when updateAvailable != null:
        return updateAvailable(_that);
      case NoUpdate() when noUpdate != null:
        return noUpdate(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(int currentVersion, int versionCode, String? versionNumber,
            bool forceUpdate)?
        updateAvailable,
    TResult Function(int currentVersion)? noUpdate,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case AppUpdateInitial() when initial != null:
        return initial();
      case UpdateAvailable() when updateAvailable != null:
        return updateAvailable(_that.currentVersion, _that.versionCode,
            _that.versionNumber, _that.forceUpdate);
      case NoUpdate() when noUpdate != null:
        return noUpdate(_that.currentVersion);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(int currentVersion, int versionCode,
            String? versionNumber, bool forceUpdate)
        updateAvailable,
    required TResult Function(int currentVersion) noUpdate,
  }) {
    final _that = this;
    switch (_that) {
      case AppUpdateInitial():
        return initial();
      case UpdateAvailable():
        return updateAvailable(_that.currentVersion, _that.versionCode,
            _that.versionNumber, _that.forceUpdate);
      case NoUpdate():
        return noUpdate(_that.currentVersion);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(int currentVersion, int versionCode,
            String? versionNumber, bool forceUpdate)?
        updateAvailable,
    TResult? Function(int currentVersion)? noUpdate,
  }) {
    final _that = this;
    switch (_that) {
      case AppUpdateInitial() when initial != null:
        return initial();
      case UpdateAvailable() when updateAvailable != null:
        return updateAvailable(_that.currentVersion, _that.versionCode,
            _that.versionNumber, _that.forceUpdate);
      case NoUpdate() when noUpdate != null:
        return noUpdate(_that.currentVersion);
      case _:
        return null;
    }
  }
}

/// @nodoc

class AppUpdateInitial implements AppUpdateState {
  const AppUpdateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AppUpdateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppUpdateState.initial()';
  }
}

/// @nodoc

class UpdateAvailable implements AppUpdateState {
  const UpdateAvailable(
      {required this.currentVersion,
      required this.versionCode,
      this.versionNumber,
      required this.forceUpdate});

  final int currentVersion;
  final int versionCode;
  final String? versionNumber;
  final bool forceUpdate;

  /// Create a copy of AppUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateAvailableCopyWith<UpdateAvailable> get copyWith =>
      _$UpdateAvailableCopyWithImpl<UpdateAvailable>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateAvailable &&
            (identical(other.currentVersion, currentVersion) ||
                other.currentVersion == currentVersion) &&
            (identical(other.versionCode, versionCode) ||
                other.versionCode == versionCode) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.forceUpdate, forceUpdate) ||
                other.forceUpdate == forceUpdate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, currentVersion, versionCode, versionNumber, forceUpdate);

  @override
  String toString() {
    return 'AppUpdateState.updateAvailable(currentVersion: $currentVersion, versionCode: $versionCode, versionNumber: $versionNumber, forceUpdate: $forceUpdate)';
  }
}

/// @nodoc
abstract mixin class $UpdateAvailableCopyWith<$Res>
    implements $AppUpdateStateCopyWith<$Res> {
  factory $UpdateAvailableCopyWith(
          UpdateAvailable value, $Res Function(UpdateAvailable) _then) =
      _$UpdateAvailableCopyWithImpl;
  @useResult
  $Res call(
      {int currentVersion,
      int versionCode,
      String? versionNumber,
      bool forceUpdate});
}

/// @nodoc
class _$UpdateAvailableCopyWithImpl<$Res>
    implements $UpdateAvailableCopyWith<$Res> {
  _$UpdateAvailableCopyWithImpl(this._self, this._then);

  final UpdateAvailable _self;
  final $Res Function(UpdateAvailable) _then;

  /// Create a copy of AppUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentVersion = null,
    Object? versionCode = null,
    Object? versionNumber = freezed,
    Object? forceUpdate = null,
  }) {
    return _then(UpdateAvailable(
      currentVersion: null == currentVersion
          ? _self.currentVersion
          : currentVersion // ignore: cast_nullable_to_non_nullable
              as int,
      versionCode: null == versionCode
          ? _self.versionCode
          : versionCode // ignore: cast_nullable_to_non_nullable
              as int,
      versionNumber: freezed == versionNumber
          ? _self.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      forceUpdate: null == forceUpdate
          ? _self.forceUpdate
          : forceUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class NoUpdate implements AppUpdateState {
  const NoUpdate({required this.currentVersion});

  final int currentVersion;

  /// Create a copy of AppUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NoUpdateCopyWith<NoUpdate> get copyWith =>
      _$NoUpdateCopyWithImpl<NoUpdate>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NoUpdate &&
            (identical(other.currentVersion, currentVersion) ||
                other.currentVersion == currentVersion));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentVersion);

  @override
  String toString() {
    return 'AppUpdateState.noUpdate(currentVersion: $currentVersion)';
  }
}

/// @nodoc
abstract mixin class $NoUpdateCopyWith<$Res>
    implements $AppUpdateStateCopyWith<$Res> {
  factory $NoUpdateCopyWith(NoUpdate value, $Res Function(NoUpdate) _then) =
      _$NoUpdateCopyWithImpl;
  @useResult
  $Res call({int currentVersion});
}

/// @nodoc
class _$NoUpdateCopyWithImpl<$Res> implements $NoUpdateCopyWith<$Res> {
  _$NoUpdateCopyWithImpl(this._self, this._then);

  final NoUpdate _self;
  final $Res Function(NoUpdate) _then;

  /// Create a copy of AppUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentVersion = null,
  }) {
    return _then(NoUpdate(
      currentVersion: null == currentVersion
          ? _self.currentVersion
          : currentVersion // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
