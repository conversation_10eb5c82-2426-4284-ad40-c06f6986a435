import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/services/user_profile_service.dart';
import 'package:rozana/features/profile/presentation/widgets/profile_setup_modal.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';
import '../../../../widgets/custom_text.dart';
import '../../bloc/login_bloc/login_bloc.dart';

class OtpScreen extends StatefulWidget {
  final String mobileNumber;
  final String? returnRoute;

  const OtpScreen({
    super.key,
    required this.mobileNumber,
    this.returnRoute,
  });

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  final pinController = TextEditingController();
  final focusNode = FocusNode();

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  Future<void> _checkAndNavigate() async {
    final userProfileService = UserProfileService();
    final isProfileComplete = await userProfileService.isProfileComplete();

    if (!mounted) return;

    if (!isProfileComplete) {
      await showProfileSetupModal(context);
    }

    if (!mounted) return;

    // Navigate to destination
    if (widget.returnRoute != null) {
      context.go(widget.returnRoute!);
    } else {
      context.go(RouteNames.home);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
        fontSize: 22,
        color: AppColors.neutral700,
        fontWeight: FontWeight.w600,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.neutral200),
      ),
    );

    return Scaffold(
      appBar: AppBar(
        leading: BackButton(
          onPressed: () {
            context.read<LoginBloc>().add(const LoginEvent.loginFailed());
            // Use canPop check to avoid "nothing to pop" error
            if (context.canPop()) {
              context.pop();
            } else {
              context.go(RouteNames.home);
            }
          },
        ),
        title: const Text('Verify OTP'),
        backgroundColor: theme.colorScheme.surface,
        scrolledUnderElevation: 0,
      ),
      body: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) {
          state.mapOrNull(
            initial: (_) {
              // This state means login succeeded and we are authenticated
              final isAuthenticated = context.read<AppBloc>().isAuthenticated;
              if (isAuthenticated) {
                _checkAndNavigate();
              }
            },
            // error: (errorState) {
            //   ScaffoldMessenger.of(context)
            //     ..hideCurrentSnackBar()
            //     ..showSnackBar(
            //       SnackBar(
            //         content: Text(errorState.message),
            //         backgroundColor: AppColors.error,
            //       ),
            //     );
            // },
          );
        },
        builder: (context, state) {
          final isLoading = state.maybeMap(
            // loading: (_) => true,
            orElse: () => false,
          );

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 24.h),
                CustomText(
                  'Enter Verification Code',
                  fontSize: 24,
                  fontWeight: FontWeight.w800,
                  color: AppColors.primary700,
                ),
                SizedBox(height: 8.h),
                CustomText(
                  'Enter the 6-digit code sent to +91 ${widget.mobileNumber}',
                  textAlign: TextAlign.center,
                  color: AppColors.neutral500,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(height: 32.h),
                Pinput(
                  length: 6,
                  controller: pinController,
                  focusNode: focusNode,
                  autofocus: true,
                  defaultPinTheme: defaultPinTheme,
                  focusedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      border: Border.all(color: AppColors.primary500),
                    ),
                  ),
                  errorPinTheme: defaultPinTheme.copyBorderWith(
                    border: Border.all(color: AppColors.error),
                  ),
                  onCompleted: (pin) {
                    context.read<LoginBloc>().add(LoginEvent.submitOTP());
                  },
                ),
                SizedBox(height: 24.h),
                state.maybeMap(
                  otp: (otpState) => TextButton(
                    onPressed: otpState.canResend
                        ? () => context
                            .read<LoginBloc>()
                            .add(const LoginEvent.resendOTP())
                        : null,
                    child: Text(
                      otpState.canResend
                          ? 'Resend OTP'
                          : 'Resend OTP in ${otpState.resendSeconds}s',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: otpState.canResend
                            ? theme.colorScheme.primary
                            : AppColors.neutral400,
                      ),
                    ),
                  ),
                  orElse: () => const SizedBox(),
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  height: 48.sp,
                  child: CustomButton(
                    isLoading: isLoading,
                    backgroundColor: AppColors.primary500,
                    onPressed: () {
                      if (pinController.text.length == 6) {
                        context.read<LoginBloc>().add(LoginEvent.submitOTP());
                      }
                    },
                    text: 'Verify & Proceed',
                  ),
                ),
                SizedBox(height: 40.h),
              ],
            ),
          );
        },
      ),
    );
  }
}
