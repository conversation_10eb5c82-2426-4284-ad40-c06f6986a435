import 'package:geocoding/geocoding.dart';

import '../../../core/network/google_api_client.dart';
import '../../../core/utils/logger.dart';
import 'platform/places_service_factory.dart';
import 'platform/places_service_interface.dart';

class GooglePlacesService {
  static final GooglePlacesService _instance = GooglePlacesService._internal();
  factory GooglePlacesService() => _instance;
  GooglePlacesService._internal() {
    // Initialize the platform-specific service
    _platformService = PlacesServiceFactory.createPlacesService();
    _initService();
  }
  
  // Platform-specific implementation
  late final PlacesServiceInterface _platformService;
  String? _sessionToken;
  
  // Initialize the service
  Future<void> _initService() async {
    await _platformService.initPlacesService();
    _refreshSessionToken();
  }
  
  // Generate a new session token
  Future<void> _refreshSessionToken() async {
    _sessionToken = await _platformService.generateSessionToken();
    LogMessage.p('Generated new session token: $_sessionToken', subTag: 'GooglePlacesService');
  }
  
  // Refresh the session token after each place selection
  Future<void> refreshSessionToken() async {
    await _refreshSessionToken();
  }
  
  // For backward compatibility with existing code
  final GoogleApiClient _apiClient = GoogleApiClient();

  // Get place details by place ID
  Future<Map<String, dynamic>?> getPlaceDetailsById(String placeId) async {
    try {
      LogMessage.p('Getting place details for place_id: $placeId',
          subTag: 'GooglePlacesService');
      
      // Use platform-specific implementation
      final result = await _platformService.getPlaceDetailsById(placeId);
      
      if (result != null) {
        LogMessage.p('Successfully retrieved place details',
            subTag: 'GooglePlacesService');
      } else {
        LogMessage.p('No place details found for ID: $placeId',
            subTag: 'GooglePlacesService');
      }
      
      return result;
    } catch (e) {
      LogMessage.p('Error getting place details by ID: $e',
          subTag: 'GooglePlacesService');
      return null;
    }
  }

  // Get place autocomplete suggestions using Google Places API
  Future<List<dynamic>> getPlaceAutocomplete({
    required String input,
    String? sessionToken,
    Map<String, dynamic>? options,
  }) async {
    try {
      LogMessage.p('GooglePlacesService: Requesting autocomplete for "$input"',
          subTag: 'GooglePlacesService');

      // Get or generate a session token if not provided
      final token = sessionToken ?? await _platformService.generateSessionToken();
      
      // Use platform-specific implementation
      final predictions = await _platformService.getPlaceAutocomplete(
        input: input,
        sessionToken: token,
        options: options,
      );

      LogMessage.p(
          'GooglePlacesService: Success - received ${predictions.length} predictions',
          subTag: 'GooglePlacesService');
      return predictions;
    } catch (e) {
      LogMessage.p('Error getting place autocomplete: $e',
          subTag: 'GooglePlacesService');
      return [];
    }
  }

  // Get place details including building name using Google Places API
  Future<Map<String, dynamic>?> getPlaceDetails(
      double latitude, double longitude) async {
    try {
      // First, try to find nearby places using Nearby Search
      final nearbyResponse = await _apiClient.getNearbyPlaces(
        latitude: latitude,
        longitude: longitude,
        radius: 10,
      );

      return nearbyResponse.when(
        success: (results) async {
          if (results.isNotEmpty) {
            // Find the best place (filter out broad location types)
            final bestPlace = _findBestPlace(results);
            if (bestPlace == null) return null;

            final closestPlace = bestPlace;

            // If we have a place_id, get detailed information
            final placeId = closestPlace['place_id'] as String?;
            if (placeId != null) {
              final detailsResponse = await _apiClient.getPlaceDetails(
                placeId: placeId,
                fields: 'name,formatted_address,types',
              );

              return detailsResponse.when(
                success: (result) => {
                  'name': result['name'] ?? '',
                  'formatted_address': result['formatted_address'] ?? '',
                  'types': result['types'] ?? [],
                  'place_id': placeId,
                },
                error: (_) => {
                  // Fallback to nearby search result if details API fails
                  'name': closestPlace['name'] ?? '',
                  'formatted_address': closestPlace['vicinity'] ?? '',
                  'types': closestPlace['types'] ?? [],
                  'place_id': closestPlace['place_id'] ?? '',
                },
              );
            }

            // Fallback to nearby search result if no place_id
            return {
              'name': closestPlace['name'] ?? '',
              'formatted_address': closestPlace['vicinity'] ?? '',
              'types': closestPlace['types'] ?? [],
              'place_id': closestPlace['place_id'] ?? '',
            };
          }
          return null;
        },
        error: (message) {
          LogMessage.p('Error getting nearby places: $message',
              subTag: 'GooglePlacesService');
          return null;
        },
      );
    } catch (e) {
      LogMessage.p('Error getting place details: $e',
          subTag: 'GooglePlacesService');
      return null;
    }
  }

  // Enhanced method to get address with building name
  Future<String> getEnhancedAddress(double latitude, double longitude) async {
    try {
      // Always get geocoding data for complete address information
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      String fallbackAddress = 'Address not found';

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        fallbackAddress = _buildCompleteAddressString(placemark);
      }

      // Try to get place details for building/establishment names
      final placeDetails = await getPlaceDetails(latitude, longitude);

      if (placeDetails != null &&
          placeDetails['name'] != null &&
          placeDetails['name'].toString().isNotEmpty) {
        final placeName = placeDetails['name'].toString();
        final formattedAddress =
            placeDetails['formatted_address']?.toString() ?? '';

        // If we have a meaningful place name (not just an address), use it
        if (placeName.isNotEmpty && !_isAddressLikeName(placeName)) {
          // For buildings/establishments, combine place name with complete address
          if (placemarks.isNotEmpty) {
            final placemark = placemarks.first;
            final completeAddress = _buildCompleteAddressString(placemark);

            // If the place name is not already in the address, prepend it
            if (!completeAddress
                .toLowerCase()
                .contains(placeName.toLowerCase())) {
              return '$placeName, $completeAddress';
            } else {
              return completeAddress;
            }
          }

          // Fallback to formatted address from Places API
          if (formattedAddress.isNotEmpty) {
            return formattedAddress;
          }

          return '$placeName, $fallbackAddress';
        }
      }

      // If geocoding doesn't provide street info, try Google Geocoding API
      if (placemarks.isEmpty ||
          (placemarks.first.street == null ||
              placemarks.first.street!.isEmpty)) {
        final googleGeocodedAddress =
            await _getGoogleGeocodedAddress(latitude, longitude);
        if (googleGeocodedAddress.isNotEmpty) {
          fallbackAddress = googleGeocodedAddress;
        }
      }

      // Return the complete geocoded address (includes road names, area, city, state)
      return fallbackAddress;
    } catch (e) {
      LogMessage.p('Error getting enhanced address: $e',
          subTag: 'GooglePlacesService');
      return 'Failed to get address';
    }
  }

  // Helper method to get address using Google Geocoding API
  Future<String> _getGoogleGeocodedAddress(
      double latitude, double longitude) async {
    try {
      final response = await _apiClient.getGeocodingResults(
        latitude: latitude,
        longitude: longitude,
        resultType: 'street_address|route|intersection',
      );

      return response.when(
        success: (results) {
          if (results.isNotEmpty) {
            final result = results[0];
            final formattedAddress =
                result['formatted_address']?.toString() ?? '';
            return formattedAddress.isNotEmpty ? formattedAddress : '';
          }
          return '';
        },
        error: (message) {
          LogMessage.p('Error getting Google geocoded address: $message',
              subTag: 'GooglePlacesService');
          return '';
        },
      );
    } catch (e) {
      LogMessage.p('Error getting Google geocoded address: $e',
          subTag: 'GooglePlacesService');
      return '';
    }
  }

  // Helper method to check if a place name is just an address
  bool _isAddressLikeName(String name) {
    // Check if the name looks like an address (contains numbers, common address words)
    final addressPatterns = [
      RegExp(r'^\d+'), // Starts with numbers
      RegExp(r'\d+.*\d+'), // Contains multiple numbers
      RegExp(r'(street|road|avenue|lane|drive|way|place|court|circle)',
          caseSensitive: false),
    ];

    return addressPatterns.any((pattern) => pattern.hasMatch(name));
  }

  // Helper method to find the best place from results (filter out broad location types)
  Map<String, dynamic>? _findBestPlace(List<dynamic> results) {
    // Define priority order for place types (higher priority = better)
    final typePriorities = {
      // Specific establishments (highest priority)
      // Religious places
      'church': 90,
      'place_of_worship': 90,
      'hindu_temple': 90,
      'mosque': 90,
      'synagogue': 90,

      // Educational institutions
      'school': 90,
      'university': 90,
      'college': 90,

      // Commercial establishments
      'restaurant': 100,
      'store': 95,
      'shopping_mall': 95,
      'hospital': 90,
      'bank': 90,
      'gas_station': 90,
      'pharmacy': 90,
      'lodging': 85,
      'tourist_attraction': 85,
      'gym': 85,
      'beauty_salon': 85,
      'car_dealer': 85,
      'car_repair': 85,
      'clothing_store': 85,
      'electronics_store': 85,
      'furniture_store': 85,
      'grocery_or_supermarket': 85,
      'hardware_store': 85,
      'home_goods_store': 85,
      'jewelry_store': 85,
      'laundry': 85,
      'meal_delivery': 85,
      'meal_takeaway': 85,
      'movie_theater': 85,
      'night_club': 85,
      'pet_store': 85,
      'shoe_store': 85,
      'spa': 85,
      'storage': 85,
      'supermarket': 85,
      'veterinary_care': 85,

      // General establishments
      'establishment': 80,
      'point_of_interest': 75,
      'food': 70,

      // Buildings and premises
      'premise': 60,
      'subpremise': 55,

      // Broad location types (lower priority)
      'neighborhood': 30,
      'sublocality': 25,
      'sublocality_level_1': 25,
      'sublocality_level_2': 20,
      'locality': 10, // City names
      'political': 5, // Administrative areas
      'administrative_area_level_1': 5,
      'administrative_area_level_2': 5,
      'country': 1,
    };

    Map<String, dynamic>? bestPlace;
    int highestPriority = -1;

    for (final result in results) {
      final place = result as Map<String, dynamic>;
      final types = (place['types'] as List<dynamic>?)?.cast<String>() ?? [];
      final name = place['name']?.toString() ?? '';

      // Skip if no name
      if (name.isEmpty) continue;

      // Calculate priority based on types
      int priority = 0;
      for (final type in types) {
        final typePriority = typePriorities[type] ?? 0;
        if (typePriority > priority) {
          priority = typePriority;
        }
      }

      // Boost priority for non-address-like names
      if (!_isAddressLikeName(name)) {
        priority += 10;
      }

      if (priority > highestPriority) {
        highestPriority = priority;
        bestPlace = place;
      }
    }

    return bestPlace;
  }

  // Helper method to build complete address string from placemark (includes state, country)
  String _buildCompleteAddressString(dynamic placemark) {
    List<String> addressParts = [];

    // Add street/road number and name
    if (placemark.street != null && placemark.street!.isNotEmpty) {
      addressParts.add(placemark.street!);
    }

    // Add sub-locality (area/neighborhood)
    if (placemark.subLocality != null && placemark.subLocality!.isNotEmpty) {
      addressParts.add(placemark.subLocality!);
    }

    // Add locality (city/town)
    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      addressParts.add(placemark.locality!);
    }

    // Add administrative area (state/province)
    if (placemark.administrativeArea != null &&
        placemark.administrativeArea!.isNotEmpty) {
      addressParts.add(placemark.administrativeArea!);
    }

    // Add postal code if available
    if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
      addressParts.add(placemark.postalCode!);
    }

    // Add country
    if (placemark.country != null && placemark.country!.isNotEmpty) {
      addressParts.add(placemark.country!);
    }

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Unknown location';
  }
}