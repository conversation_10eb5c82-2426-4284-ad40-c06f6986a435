// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_permission_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationPermissionEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LocationPermissionEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent()';
  }
}

/// @nodoc
class $LocationPermissionEventCopyWith<$Res> {
  $LocationPermissionEventCopyWith(
      LocationPermissionEvent _, $Res Function(LocationPermissionEvent) __);
}

/// Adds pattern-matching-related methods to [LocationPermissionEvent].
extension LocationPermissionEventPatterns on LocationPermissionEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckPermissions value)? checkPermissions,
    TResult Function(_RequestPermissions value)? requestPermissions,
    TResult Function(_OpenAppSettings value)? openAppSettings,
    TResult Function(_RequestLocationServiceToggle value)?
        requestLocationServiceToggle,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissions() when checkPermissions != null:
        return checkPermissions(_that);
      case _RequestPermissions() when requestPermissions != null:
        return requestPermissions(_that);
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings(_that);
      case _RequestLocationServiceToggle()
          when requestLocationServiceToggle != null:
        return requestLocationServiceToggle(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckPermissions value) checkPermissions,
    required TResult Function(_RequestPermissions value) requestPermissions,
    required TResult Function(_OpenAppSettings value) openAppSettings,
    required TResult Function(_RequestLocationServiceToggle value)
        requestLocationServiceToggle,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissions():
        return checkPermissions(_that);
      case _RequestPermissions():
        return requestPermissions(_that);
      case _OpenAppSettings():
        return openAppSettings(_that);
      case _RequestLocationServiceToggle():
        return requestLocationServiceToggle(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckPermissions value)? checkPermissions,
    TResult? Function(_RequestPermissions value)? requestPermissions,
    TResult? Function(_OpenAppSettings value)? openAppSettings,
    TResult? Function(_RequestLocationServiceToggle value)?
        requestLocationServiceToggle,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissions() when checkPermissions != null:
        return checkPermissions(_that);
      case _RequestPermissions() when requestPermissions != null:
        return requestPermissions(_that);
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings(_that);
      case _RequestLocationServiceToggle()
          when requestLocationServiceToggle != null:
        return requestLocationServiceToggle(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool showLoader)? checkPermissions,
    TResult Function()? requestPermissions,
    TResult Function()? openAppSettings,
    TResult Function()? requestLocationServiceToggle,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissions() when checkPermissions != null:
        return checkPermissions(_that.showLoader);
      case _RequestPermissions() when requestPermissions != null:
        return requestPermissions();
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings();
      case _RequestLocationServiceToggle()
          when requestLocationServiceToggle != null:
        return requestLocationServiceToggle();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool showLoader) checkPermissions,
    required TResult Function() requestPermissions,
    required TResult Function() openAppSettings,
    required TResult Function() requestLocationServiceToggle,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissions():
        return checkPermissions(_that.showLoader);
      case _RequestPermissions():
        return requestPermissions();
      case _OpenAppSettings():
        return openAppSettings();
      case _RequestLocationServiceToggle():
        return requestLocationServiceToggle();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool showLoader)? checkPermissions,
    TResult? Function()? requestPermissions,
    TResult? Function()? openAppSettings,
    TResult? Function()? requestLocationServiceToggle,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissions() when checkPermissions != null:
        return checkPermissions(_that.showLoader);
      case _RequestPermissions() when requestPermissions != null:
        return requestPermissions();
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings();
      case _RequestLocationServiceToggle()
          when requestLocationServiceToggle != null:
        return requestLocationServiceToggle();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _CheckPermissions implements LocationPermissionEvent {
  const _CheckPermissions({this.showLoader = false});

  @JsonKey()
  final bool showLoader;

  /// Create a copy of LocationPermissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CheckPermissionsCopyWith<_CheckPermissions> get copyWith =>
      __$CheckPermissionsCopyWithImpl<_CheckPermissions>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CheckPermissions &&
            (identical(other.showLoader, showLoader) ||
                other.showLoader == showLoader));
  }

  @override
  int get hashCode => Object.hash(runtimeType, showLoader);

  @override
  String toString() {
    return 'LocationPermissionEvent.checkPermissions(showLoader: $showLoader)';
  }
}

/// @nodoc
abstract mixin class _$CheckPermissionsCopyWith<$Res>
    implements $LocationPermissionEventCopyWith<$Res> {
  factory _$CheckPermissionsCopyWith(
          _CheckPermissions value, $Res Function(_CheckPermissions) _then) =
      __$CheckPermissionsCopyWithImpl;
  @useResult
  $Res call({bool showLoader});
}

/// @nodoc
class __$CheckPermissionsCopyWithImpl<$Res>
    implements _$CheckPermissionsCopyWith<$Res> {
  __$CheckPermissionsCopyWithImpl(this._self, this._then);

  final _CheckPermissions _self;
  final $Res Function(_CheckPermissions) _then;

  /// Create a copy of LocationPermissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? showLoader = null,
  }) {
    return _then(_CheckPermissions(
      showLoader: null == showLoader
          ? _self.showLoader
          : showLoader // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _RequestPermissions implements LocationPermissionEvent {
  const _RequestPermissions();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _RequestPermissions);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.requestPermissions()';
  }
}

/// @nodoc

class _OpenAppSettings implements LocationPermissionEvent {
  const _OpenAppSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _OpenAppSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.openAppSettings()';
  }
}

/// @nodoc

class _RequestLocationServiceToggle implements LocationPermissionEvent {
  const _RequestLocationServiceToggle();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RequestLocationServiceToggle);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.requestLocationServiceToggle()';
  }
}

// dart format on
