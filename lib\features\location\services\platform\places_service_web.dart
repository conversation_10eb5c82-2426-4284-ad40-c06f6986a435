import 'dart:async';
import 'dart:convert';
import 'package:js/js_util.dart';
import 'package:js/js.dart';
import '../../../../core/utils/logger.dart';
import 'places_service_interface.dart';

// JS interop annotations for the Google Places API
@JS('window.rozanaPlacesInterop.initPlacesService')
external bool _initPlacesService();

@JS('window.rozanaPlacesInterop.generateSessionToken')
external bool _generateSessionToken();

@JS('window.rozanaPlacesInterop.getSessionToken')
external String _getSessionToken();

@JS('window.rozanaPlacesInterop.getPlaceAutocomplete')
external Object _getPlaceAutocompleteJS(String input, Object? options);

@JS('window.rozanaPlacesInterop.getPlaceDetails')
external Object _getPlaceDetailsJS(String placeId);

/// Web implementation of the Places service interface
class PlacesServiceWeb implements PlacesServiceInterface {
  @override
  Future<bool> initPlacesService() async {
    try {
      return _initPlacesService();
    } catch (e) {
      LogMessage.p('Error initializing Places service: $e', subTag: 'PlacesServiceWeb');
      return false;
    }
  }

  @override
  Future<String> generateSessionToken() async {
    try {
      _generateSessionToken();
      return _getSessionToken();
    } catch (e) {
      LogMessage.p('Error generating session token: $e', subTag: 'PlacesServiceWeb');
      return '';
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getPlaceAutocomplete({
    required String input,
    String? sessionToken,
    Map<String, dynamic>? options,
  }) async {
    try {
      LogMessage.p('Web: Getting place autocomplete for "$input"', subTag: 'PlacesServiceWeb');
      
      // Convert options to JS object if provided
      final jsOptions = options != null ? jsify(options) : null;
      
      // Call the JS function and convert the Promise to a Future
      final jsPromise = _getPlaceAutocompleteJS(input, jsOptions);
      final result = await promiseToFuture<String>(jsPromise);
      
      // Parse the JSON string result
      final List<dynamic> predictions = jsonDecode(result);
      
      // Convert to List<Map<String, dynamic>>
      return predictions.map((item) => Map<String, dynamic>.from(item)).toList();
    } catch (e) {
      LogMessage.p('Error getting place autocomplete: $e', subTag: 'PlacesServiceWeb');
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>?> getPlaceDetailsById(String placeId) async {
    try {
      LogMessage.p('Web: Getting place details for place_id: $placeId', subTag: 'PlacesServiceWeb');
      
      // Call the JS function and convert the Promise to a Future
      final jsPromise = _getPlaceDetailsJS(placeId);
      final result = await promiseToFuture<String>(jsPromise);
      
      // Parse the JSON string result
      final Map<String, dynamic> placeDetails = jsonDecode(result) as Map<String, dynamic>;
      
      // Debug log the structure
      LogMessage.p('Place details structure: ${placeDetails.keys.join(', ')}', subTag: 'PlacesServiceWeb');
      if (placeDetails.containsKey('geometry')) {
        LogMessage.p('Geometry structure: ${(placeDetails['geometry'] as Map).keys.join(', ')}', subTag: 'PlacesServiceWeb');
      }
      
      return placeDetails;
    } catch (e) {
      LogMessage.p('Error getting place details: $e', subTag: 'PlacesServiceWeb');
      return null;
    }
  }
}
