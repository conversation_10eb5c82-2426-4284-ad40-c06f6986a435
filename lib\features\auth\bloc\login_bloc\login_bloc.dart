export 'login_event.dart';
export 'login_state.dart';

import 'dart:async';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:rozana/core/utils/logger.dart';

import '../../../../app/bloc/app_bloc.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/services/analytics_events/analytics_service.dart';
import '../../../../core/services/analytics_events/authentication_analytics_events.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../core/utils/notifier.dart';
import '../../../../core/utils/text_field_manager.dart';
import '../../../../domain/repositories/auth_repository_interface.dart';

import 'login_event.dart';
import 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final IAuthRepository authRepository;
  final AppNotifier notifier;
  final AppBloc appBloc;
  final AnalyticsService analyticsService;

  static TextFieldManager? mobileNumberManager;
  static TextFieldManager? otpManager;
  static List<TextFieldManager> otpManagers = [];
  int mobileAttempt = 0;
  int otpAttempt = 0;

  static StreamSubscription<String>? _otpSubscription;
  Timer? _resendTimer;

  bool _verificationCompleted = false;

  static void cleanupSmsResources() {
    _otpSubscription?.cancel();
    _otpSubscription = null;
    if (!kIsWeb) {
      SmsAutoFill().unregisterListener();
    }
  }

  LoginBloc({
    required this.authRepository,
    required this.notifier,
    required this.appBloc,
    required this.analyticsService,
  }) : super(const LoginState.initial()) {
    if (mobileNumberManager == null || mobileNumberManager!.isDisposed) {
      mobileNumberManager = TextFieldManager();
    }
    on<LoginEvent>(
      (event, emit) => event.map(
        initLogin: (e) async => await _onInit(e.showHint, emit),
        mobileChanged: (e) => _onMobileChanged(e, emit),
        submitLogin: (_) async => await _onSubmitLogin(emit),
        loginFailed: (_) => _onLoginFail(emit),
        initOTP: (e) => _onInitOTP(e.verificationId, e.mobileNumber, emit),
        updateTimer: (e) => _onUpdateTimer(e.second, emit),
        resendOTP: (_) => _onResendOTP(emit),
        otpChanged: (e) => _onOTPChanged(e, emit),
        submitOTP: (_) async => await _onSubmitOTP(emit),
        otpFailed: (_) => _onOTPFail(emit),
        showLoader: (_) => emit(state.copyWith(isLoading: true)),
        userVerified: (e) => _onUserVerified(e.credentials, emit),
      ),
    );
  }

  Future<void> _onInit(bool showHint, Emitter<LoginState> emit) async {
    // Request focus for phone number field
    mobileAttempt = 0;
    otpAttempt = 0;

    if (mobileNumberManager != null) {
      mobileNumberManager?.clear();
      Future.delayed(const Duration(milliseconds: 100), () {
        mobileNumberManager!.focusNode.requestFocus();
      });
    }

    // Track mobile number screen viewed
    AuthenticationAnalyticsEvents().trackMobileNumberScreenViewed();
    await _showPhoneNumberHint();

    mobileNumberManager?.controller.addListener(() {
      add(LoginEvent.mobileChanged(mobileNumberManager?.text ?? ''));
    });
    emit(const LoginState.initial(
      mobile: '',
      showError: false,
      isLoading: false,
      isLoginValid: false,
      isOTPValid: false,
    ));
  }

  Future<void> _showPhoneNumberHint() async {
    try {
      FocusManager.instance.primaryFocus?.unfocus();
      await Future<void>.delayed(const Duration(milliseconds: 100));
      final phoneNumber = await SmsAutoFill().hint;
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
        final last10Digits =
            digits.length > 10 ? digits.substring(digits.length - 10) : digits;
        mobileNumberManager?.text = last10Digits;
        add(LoginEvent.submitLogin());
      }
    } catch (e) {
      debugPrint('Error getting phone hint: $e');
    }
  }

  void _onMobileChanged(LoginEvent event, Emitter<LoginState> emit) {
    event.maybeMap(
      mobileChanged: (e) {
        final mobile = e.value;

        ValidationState validationState =
            AppValidator.mobileNumberValidator(mobile);
        if (mobileAttempt > 0) {
          mobileNumberManager?.throwError(validationState.message ?? '');

          state.maybeMap(
            initial: (currentState) => emit(currentState.copyWith(
              mobile: mobile,
              showError: !validationState.valid,
              isLoginValid: validationState.valid,
            )),
            orElse: () {},
          );
        } else {
          state.maybeMap(
            initial: (currentState) => emit(currentState.copyWith(
              mobile: mobile,
              isLoginValid: validationState.valid,
            )),
            orElse: () {},
          );
        }
      },
      orElse: () {},
    );
  }

  Future<void> _onSubmitLogin(Emitter<LoginState> emit) async {
    // Reset verification flag when starting a new verification process
    _verificationCompleted = false;

    mobileAttempt++;
    final mobileText = mobileNumberManager?.text ?? '';
    if (mobileText != state.mobile) {
      emit(state.copyWith(mobile: mobileText));
    }
    ValidationState validationState =
        AppValidator.mobileNumberValidator(mobileText);

    if (!validationState.valid) {
      mobileNumberManager?.throwError(validationState.message ?? '');
      emit(state.copyWith(
        showError: true,
      ));
      return;
    }

    // Track mobile number continue button clicked
    AuthenticationAnalyticsEvents().trackMobileNumberContinueClicked();
    emit(state.copyWith(isLoading: true, showError: false));

    if (!kIsWeb) {
      _otpSubscription?.cancel();
      _otpSubscription = null;
      SmsAutoFill().listenForCode();

      _otpSubscription = SmsAutoFill().code.listen((code) {
        state.mapOrNull(otp: (_) {
          if (code.isNotEmpty) {
            for (int i = 0; i < otpManagers.length; i++) {
              if (i < code.length) {
                otpManagers[i].text = code[i];
              }
            }
            otpManager?.text = code;
            add(LoginEvent.showLoader());
          }
        });
      });
    }
    try {
      await authRepository.verifyMobileNumber(
        "+91${mobileNumberManager?.text}",
        verificationCompleted: (UserCredential credential) async {
          _verificationCompleted = true;
          add(LoginEvent.showLoader());
          await Future.delayed(const Duration(milliseconds: 300));
          add(LoginEvent.userVerified(credential));
        },
        verificationFailed: (Exception e) {
          add(LoginEvent.loginFailed());
          notifier.showSnackBar(
            'Verification failed: ${e.toString()}',
            isError: true,
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          add(LoginEvent.initOTP(
              verificationId, mobileNumberManager?.text ?? ''));
        },
      );
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      notifier.showSnackBar(
        e.toString(),
        isError: true,
      );
    }
  }

  void _onLoginFail(Emitter<LoginState> emit) {
    emit(LoginState.initial(
        isLoading: false, mobile: state.mobile, showError: state.showError));
  }

  //OTP Logic
  void _onInitOTP(
      String verificationId, String mobileNumber, Emitter<LoginState> emit) {
    // Always create a new TextFieldManager for OTP to avoid using disposed resources
    if (otpManager != null && !otpManager!.isDisposed) {
      otpManager?.dispose();
    }
    otpManager = TextFieldManager();
    otpManager?.text = '      ';

    if (otpManagers.isNotEmpty) {
      otpManagers.map((e) => e.dispose());
    }

    otpManagers = List.generate(6, (index) => TextFieldManager());

    for (int i = 0; i < otpManagers.length; i++) {
      if (i > 0) {
        otpManagers[i].controller.text = ' ';
      }
      otpManagers[i].focusNode.addListener(() {
        bool isFocused = otpManagers[i].focusNode.hasFocus;
        if (isFocused) {
          if ((otpManagers[i].text.isEmpty) && (i > 0)) {
            otpManagers[i].text = ' ';
          }
        }
      });
    }

    otpManager!.controller.addListener(() {
      add(LoginEvent.otpChanged(otpManager?.text ?? ''));
    });

    // Auto-focus the OTP field when transitioning to OTP screen
    Future.delayed(const Duration(milliseconds: 300), () {
      if (otpManager != null && !otpManager!.isDisposed) {
        otpManager!.focusNode.requestFocus();
      }
      if (otpManagers.isNotEmpty) {
        otpManagers[0].focusNode.requestFocus();
      }
    });

    // Track OTP verification screen viewed
    AuthenticationAnalyticsEvents().trackOTPVerificationScreenViewed();

    emit(LoginState.otp(mobile: mobileNumber, verificationId: verificationId));
    _startNewOTPTimer();
  }

  Future<void> _startNewOTPTimer() async {
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      state.mapOrNull(otp: (otpState) {
        if (otpState.resendSeconds > 0) {
          int second = otpState.resendSeconds - 1;
          add(LoginEvent.updateTimer(second));
        } else {
          add(LoginEvent.updateTimer(0));
          timer.cancel();
        }
      });
    });
  }

  void _onUpdateTimer(int second, Emitter<LoginState> emit) {
    state.mapOrNull(otp: (otpState) {
      emit(otpState.copyWith(resendSeconds: second, canResend: second <= 0));
    });
  }

  Future<void> _onResendOTP(Emitter<LoginState> emit) async {
    try {
      // Reset verification flag when resending OTP
      _verificationCompleted = false;

      // Track OTP resend requested
      AuthenticationAnalyticsEvents().trackOTPResendRequested();
      state.mapOrNull(otp: (otpState) {
        emit(otpState.copyWith(canResend: false, resendSeconds: 30));
      });

      _startNewOTPTimer();
      await authRepository.verifyMobileNumber(
        "+91${state.mobile}",
        verificationCompleted: (UserCredential credential) async {
          add(LoginEvent.userVerified(credential));
        },
        verificationFailed: (Exception e) {
          add(LoginEvent.otpFailed());
          notifier.showSnackBar(
            'Resend otp failed: ${e.toString()}',
            isError: true,
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          add(LoginEvent.initOTP(
              verificationId, mobileNumberManager?.text ?? ''));
        },
      );
    } catch (e) {
      notifier.showSnackBar(
        e.toString(),
        isError: true,
      );
    }
  }

  void _onOTPChanged(LoginEvent event, Emitter<LoginState> emit) {
    event.maybeMap(
      otpChanged: (e) {
        final otp = e.value.trim();
        ValidationState validationState = AppValidator.otpValidator(otp);
        if (otpAttempt > 0) {
          otpManager?.throwError(validationState.message ?? '');
          state.mapOrNull(otp: (otpState) {
            emit(otpState.copyWith(
                otp: otp,
                showError: !validationState.valid,
                isOTPValid: validationState.valid));
          });
        } else {
          state.mapOrNull(otp: (otpState) {
            emit(
                otpState.copyWith(otp: otp, isOTPValid: validationState.valid));
          });
        }
      },
      orElse: () {},
    );
  }

  Future<void> _onSubmitOTP(Emitter<LoginState> emit) async {
    // Skip verification if it's already completed
    if (_verificationCompleted) {
      LogMessage.p(
          'Skipping manual OTP verification as it was already completed');
      return;
    }

    otpAttempt++;
    ValidationState validationState =
        AppValidator.otpValidator(otpManager?.text.trim());

    if (!validationState.valid) {
      otpManager?.throwError(validationState.message ?? '');
      emit(state.copyWith(
        showError: true,
      ));
      return;
    }

    emit(state.copyWith(isLoading: true, showError: false));

    try {
      getIt<AppNotifier>().showLoading();

      UserCredential? cred = await authRepository.validateOTP(
          state.mapOrNull(otp: (s) => s.verificationId) ?? '',
          otpManager?.text ?? '');
      // Mark verification as completed to prevent duplicate attempts
      _verificationCompleted = true;
      add(LoginEvent.userVerified(cred));
    } catch (e) {
      getIt<AppNotifier>().hideLoading();
      emit(state.copyWith(isLoading: false, showError: true));

      // Track OTP incorrect
      AuthenticationAnalyticsEvents().trackOTPIncorrect();

      notifier.showSnackBar(
        e.toString(),
        isError: true,
      );
    }
  }

  void _onOTPFail(Emitter<LoginState> emit) {
    // Track OTP failure
    AuthenticationAnalyticsEvents().trackOTPFailure();

    emit(LoginState.otp(
        isLoading: false, mobile: state.mobile, showError: state.showError));
  }

  void _onUserVerified(
      UserCredential credential, Emitter<LoginState> emit) async {
    try {
      String token = await credential.user?.getIdToken() ?? '';
      await AppPreferences.setToken(token);
      try {
        final userId = credential.user?.uid;
        if (userId != null) {
          await analyticsService.setUserId(userId);
        }
      } catch (_) {}
      AuthenticationAnalyticsEvents().trackOTPSuccess();

      Response? kaptureResponse = await ApiClient.sendHttpRequest(
        baseURL: 'https://oms-uat.rozana.tech',
        endUrl: '/app/v1/encrypt_customer_code',
        method: HttpMethod.post,
        data: {
          "customer_code": credential.user?.uid ?? '',
        },
        tag: 'Kapture:::',
      );

      final encryptedCustomerCode =
          kaptureResponse?.data['encrypted_customer_code'] ?? '';
      final iv = kaptureResponse?.data['iv'] ?? '';

      Map<String, dynamic> user = {
        'uid': credential.user?.uid,
        'phoneNumber': credential.user?.phoneNumber,
        'displayName': credential.user?.displayName,
        'email': credential.user?.email,
        'photoURL': credential.user?.photoURL,
        'lastLogin': DateTime.now().toIso8601String(),
      };
      if (encryptedCustomerCode != null) {
        user['enc_cus_code'] = encryptedCustomerCode;
        user['iv'] = iv;
      }
      getIt<AppNotifier>().hideLoading();
      appBloc.add(AppEvent.loginWithProfileCheck(token, user: user));
    } catch (e) {
      LogMessage.p('Failed to complete user verification');
    }
  }

  @override
  Future<void> close() {
    if (mobileNumberManager != null && !mobileNumberManager!.isDisposed) {
      mobileNumberManager?.dispose();
    }
    if (otpManager != null && !otpManager!.isDisposed) {
      otpManager?.dispose();
    }
    if (otpManagers.isNotEmpty) {
      otpManagers.map((e) => e.dispose());
    }
    _otpSubscription?.cancel();
    _otpSubscription = null;
    _resendTimer?.cancel();
    cleanupSmsResources();
    _verificationCompleted = false;
    return super.close();
  }
}
