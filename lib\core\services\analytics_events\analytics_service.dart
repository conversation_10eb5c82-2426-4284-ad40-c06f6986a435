import 'dart:io';

import 'package:amplitude_flutter/amplitude.dart';
import 'package:amplitude_flutter/configuration.dart';
import 'package:amplitude_flutter/events/base_event.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'analytics_data_service.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  late Amplitude amplitude;
  late FirebaseAnalytics _firebaseAnalytics;
  bool _isInitialized = false;
  String? _country;
  String? _appVersion;
  String? _deviceId;
  String? _deviceName;
  String? _platform;
  String? _os;

  bool get isInitialized => _isInitialized;
  Future<void> initialize() async {
    amplitude =
        Amplitude(Configuration(apiKey: '802688fb6093fa50671e018afbe1f7df'));
    _firebaseAnalytics = FirebaseAnalytics.instance;
    await _initializeDeviceInfo();
    await _initializeCountryAndVersion();
    _isInitialized = true;
  }

  void initializeAsync() {
    Future.microtask(() async {
      amplitude =
          Amplitude(Configuration(apiKey: '802688fb6093fa50671e018afbe1f7df'));
      _firebaseAnalytics = FirebaseAnalytics.instance;
      await _initializeDeviceInfo();
      await _initializeCountryAndVersion();

      _isInitialized = true;
    });
  }

  /// Initialize device information
  Future<void> _initializeDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      _deviceId = androidInfo.id;
      _deviceName = androidInfo.model;
      _platform = 'Android';
      _os = 'Android ${androidInfo.version.release}';
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      _deviceId = iosInfo.identifierForVendor;
      _deviceName = iosInfo.model;
      _platform = 'iOS';
      _os = 'iOS ${iosInfo.systemVersion}';
    }
  }

  Future<void> _initializeCountryAndVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion = packageInfo.version;
      _country = 'India';
      _fetchCountryInBackground();
    } catch (e) {
      _appVersion = 'unknown';
      _country = 'unknown';
    }
  }

  void _fetchCountryInBackground() {
    Future.delayed(const Duration(seconds: 2), () async {
      final locationCountry = await _getCountryFromLocation();
      _country = locationCountry;
    });
  }

  Future<String> _getCountryFromLocation() async {
    final position = await Geolocator.getCurrentPosition(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
      ),
    );
    final response = await http.get(
      Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=${position.latitude},${position.longitude}&key=YOUR_API_KEY',
      ),
    );

    if (response.statusCode == 200) {
      return 'India';
    }
    return 'India';
  }

  Future<Map<String, Object>> _getBaseProperties() async {
    try {
      final analyticsDataService = getIt<AnalyticsDataService>();
      final analyticsData =
          await analyticsDataService.getAnalyticsDataWithFallbacks();

      return {
        'device_id': _deviceId ?? 'unknown',
        'device_name': _deviceName ?? 'unknown',
        'platform': _platform ?? 'unknown',
        'os': _os ?? 'unknown',
        'country': _country ?? 'unknown',
        'app_version': _appVersion ?? 'unknown',
        'account_creation_date': DateTime.now().toString(),
        'geolocation': analyticsData['geolocation'] ?? 'unknown',
        // 'duration_text': analyticsData['durationText'] ?? 'unknown',
      };
    } catch (e) {
      return {
        'device_id': _deviceId ?? 'unknown',
        'device_name': _deviceName ?? 'unknown',
        'platform': _platform ?? 'unknown',
        'os': _os ?? 'unknown',
        'country': _country ?? 'unknown',
        'app_version': _appVersion ?? 'unknown',
        'account_creation_date': DateTime.now().toString(),
        'geolocation': 'unknown',
        'duration_text': 'unknown',
      };
    }
  }

  /// Track when user views mobile number screen
  Future<void> logMobileNumberScreenViewed(String? mobileNumber) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
    };
    await amplitude.track(
        BaseEvent('mobile number screen viewed', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'mobile_number_screen_viewed',
      parameters: properties,
    );
  }

  /// Track when user clicks continue button on mobile number screen
  Future<void> logMobileNumberContinueClicked(String? mobileNumber) async {
    final baseProperties = await _getBaseProperties();
    // Remove geolocation and duration_text for mobile number screen events
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'cta': 'Continue',
    };
    await amplitude.track(BaseEvent(
        'mobile number screen continue button clicked',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'mobile_number_screen_continue_button_clicked',
      parameters: properties,
    );
  }

  /// Track when user lands on OTP verification screen
  Future<void> logOTPVerificationScreenViewed(String? mobileNumber) async {
    final baseProperties = await _getBaseProperties();
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'otp_triggered': 'Yes',
    };
    await amplitude.track(BaseEvent('otp verification screen viewed',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'otp_verification_screen_viewed',
      parameters: properties,
    );
  }

  /// Track when user clicks resend OTP button
  Future<void> logOTPResendRequested(String? mobileNumber) async {
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'otp_triggered': 'Yes',
    };
    await amplitude
        .track(BaseEvent('otp resend requested', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'otp_resend_requested',
      parameters: properties,
    );
  }

  // Track when user selects a promotional banner
  Future<void> logPromotionalBannerSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String homepageSection,
    required String bannerLocation,
    required String bannerId,
    required String redirectionPath,
  }) async {
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'homepage_section': homepageSection,
      'banner_location': bannerLocation,
      'banner_id': bannerId,
      'redirection_path': redirectionPath,
    };
    await amplitude.track(
        BaseEvent('promotional banner selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'promotional_banner_selected',
      parameters: properties,
    );
  }

  // Track when user selects a landing page
  Future<void> logLandingPageSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String homepageSection,
  }) async {
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'homepage_section': homepageSection,
    };
    String eventName;
    String firebaseEventName;
    switch (homepageSection.toLowerCase()) {
      case 'my deals':
      case 'all deals':
        eventName = 'all deals landing page selected';
        firebaseEventName = 'all_deals_landing_page_selected';
        break;
      case 'grocery':
      case 'grocery&staple':
      case 'grocery & staple':
        eventName = 'grocery landing page selected';
        firebaseEventName = 'grocery_landing_page_selected';
        break;
      case 'fashion':
        eventName = 'fashion landing page selected';
        firebaseEventName = 'fashion_landing_page_selected';
        break;
      case 'footwear':
        eventName = 'footwear landing page selected';
        firebaseEventName = 'footwear_landing_page_selected';
        break;
      default:
        eventName = 'landing page selected';
        firebaseEventName = 'landing_page_selected';
    }

    await amplitude.track(BaseEvent(eventName, eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: firebaseEventName,
      parameters: properties,
    );
  }

  // Track when user selects a category
  Future<void> logCategorySelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String categoryName,
    required String categoryPath,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'category_name': categoryName,
      'category_path': categoryPath,
    };
    await amplitude
        .track(BaseEvent('category selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'category_selected',
      parameters: properties,
    );
  }

  // Track when user selects a sub-category
  Future<void> logSubCategorySelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String subCategoryName,
    required String categoryName,
    required String categoryPath,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'sub_category_name': subCategoryName,
      'category_name': categoryName,
      'category_path': categoryPath,
    };
    await amplitude
        .track(BaseEvent('sub category selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'sub_category_selected',
      parameters: properties,
    );
  }

  // Track when user selects a sub-sub-category
  Future<void> logSubSubCategorySelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String subSubCategoryName,
    required String subCategoryName,
    required String categoryName,
    required String categoryPath,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'sub_sub_category_name': subSubCategoryName,
      'sub_category_name': subCategoryName,
      'category_name': categoryName,
      'category_path': categoryPath,
    };
    await amplitude.track(
        BaseEvent('sub sub category selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'sub_sub_category_selected',
      parameters: properties,
    );
  }

  // Track when user adds a product to cart
  Future<void> logAddToCart({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String productSkuId,
    required String productName,
    required String screenName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required Map<String, dynamic> allAttributes,
    required String categoryName,
    // required String subCategoryName,
    // required String subSubCategoryName,
    required String variantBottomSheet,
    required String variantBottomSheetValue,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'screen_name': screenName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'discount': discount,
      'category_name': categoryName,
      ...allAttributes, // Add all product attributes
    };
    await amplitude
        .track(BaseEvent('add to cart', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'add_to_cart',
      parameters: properties,
    );
  }

  // Track when user opens a product display page (PDP)
  Future<void> logPDPOpened({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String pdpLocation,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    if (ipAddress != null && ipAddress.isNotEmpty) {
      baseProperties['ip_address'] = ipAddress;
    } else {
      baseProperties['ip_address'] = 'unknown';
    }

    if (latitude != null && longitude != null) {
      baseProperties['lat_long'] =
          '${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}';
    } else {
      baseProperties['lat_long'] = 'unknown';
    }

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'discount': discount,
      'category_name': categoryName,
      'sub_category_name': subCategoryName,
      'pdp_location': pdpLocation,
    };
    await amplitude.track(BaseEvent('PDP Opened', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'pdp_opened',
      parameters: properties,
    );
  }

  // Track when user selects a size on PDP page
  Future<void> logPDPSizeSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String productSkuId,
    required String productName,
    required String size,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String pdpLocation,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    if (ipAddress != null && ipAddress.isNotEmpty) {
      baseProperties['ip_address'] = ipAddress;
    } else {
      baseProperties['ip_address'] = 'unknown';
    }

    if (latitude != null && longitude != null) {
      baseProperties['lat_long'] =
          '${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}';
    } else {
      baseProperties['lat_long'] = 'unknown';
    }

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'size': size,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'discount': discount,
      'category_name': categoryName,
      'sub_category_name': subCategoryName,
      'pdp_location': pdpLocation,
    };
    await amplitude
        .track(BaseEvent('PDP Size Selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'pdp_size_selected',
      parameters: properties,
    );
  }

  // Track when user scrolls PDP images
  Future<void> logPDPImageScroll({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String subSubCategoryName,
    required String pdpLocation,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    // Get geolocation and delivery SLA from analytics data
    final analyticsDataService = getIt<AnalyticsDataService>();
    final analyticsData =
        await analyticsDataService.getAnalyticsDataWithFallbacks();
    final geolocation = analyticsData['geolocation'] ?? 'unknown';
    final deliverySLA = analyticsData['durationText'] ?? 'unknown';

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'discount': discount,
      'category_name': categoryName,
      'sub_category_name': subCategoryName,
      'sub_sub_category_name': subSubCategoryName,
      'pdp_location': pdpLocation,
    };

    await amplitude
        .track(BaseEvent('PDP Image Scroll', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'pdp_image_scroll',
      parameters: properties,
    );
  }

  // Track when user selects view similar in PDP
  Future<void> logPDPViewSimilarSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required String categoryName,
    required String subCategoryName,
    required String subSubCategoryName,
    required String pdpLocation,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();
    if (ipAddress != null && ipAddress.isNotEmpty) {
      baseProperties['ip_address'] = ipAddress;
    } else {
      baseProperties['ip_address'] = 'unknown';
    }

    if (latitude != null && longitude != null) {
      baseProperties['lat_long'] =
          '${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}';
    } else {
      baseProperties['lat_long'] = 'unknown';
    }

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'discount': discount,
      'category_name': categoryName,
      'sub_category_name': subCategoryName,
      'sub_sub_category_name': subSubCategoryName,
      'pdp_location': pdpLocation,
    };
    await amplitude.track(
        BaseEvent('pdp_view_similar_selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'pdp_view_similar_selected',
      parameters: properties,
    );
  }

  // Track when user lands on My Bag (Cart) landing page
  Future<void> logMyBagLandingPage({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required int deliverySlots,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'delivery_slots': deliverySlots,
    };
    await amplitude
        .track(BaseEvent('my bag landing page', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'my_bag_landing_page',
      parameters: properties,
    );
  }

  // Track when user adds a product to My Bag (Cart)
  Future<void> logMyBagProductAdded({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String productDiscount,
    // required String categoryName,
    // required String subCategoryName,
    // required String subSubCategoryName,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'product_discount': productDiscount,
    };
    await amplitude
        .track(BaseEvent('my bag product added', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'my_bag_product_added',
      parameters: properties,
    );
  }

  // Track when user removes a product from My Bag (Cart)
  Future<void> logMyBagProductRemoved({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String productDiscount,
    // required String categoryName,
    // required String subCategoryName,
    // required String subSubCategoryName,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'product_discount': productDiscount,
      // 'category_name': categoryName,
      // 'sub_category_name': subCategoryName,
      // 'sub_sub_category_name': subSubCategoryName,
    };
    await amplitude.track(
        BaseEvent('my bag product removed', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'my_bag_product_removed',
      parameters: properties,
    );
  }

  // Track when user deletes a product from My Bag (Cart)
  Future<void> logMyBagProductDeleted({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String productDiscount,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'product_discount': productDiscount,
    };
    await amplitude.track(
        BaseEvent('my bag product deleted', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'my_bag_product_deleted',
      parameters: properties,
    );
  }

  // Track when user selects payment method in Go to Payment
  Future<void> logGoToPaymentMethodSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required int deliverySlots,
    required String paymentMethodSelected,
    required String walletBalance,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'delivery_slots': deliverySlots,
      'payment_method_selected': paymentMethodSelected,
      'wallet_balance': walletBalance,
    };
    await amplitude.track(BaseEvent('go to payment payment method selected',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'go_to_payment_payment_method_selected',
      parameters: properties,
    );
  }

  // Track when user selects change address in Go to Payment
  Future<void> logGoToPaymentChangeAddressSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required String currentAddress,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'current_address': currentAddress,
    };
    await amplitude.track(BaseEvent('go to payment change address selected',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'go_to_payment_change_address_selected',
      parameters: properties,
    );
  }

  // Track when user clicks payment button in Go to Payment
  Future<void> logGoToPaymentButtonClicked({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required int deliverySlots,
    required String paymentMethodSelected,
    required String walletBalance,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'delivery_slots': deliverySlots,
      'payment_method_selected': paymentMethodSelected,
      'wallet_balance': walletBalance,
    };
    await amplitude.track(BaseEvent('go to payment payment button clicked',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'go_to_payment_payment_button_clicked',
      parameters: properties,
    );
  }

  /// Track when user launches the app
  Future<void> logAppLaunched({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String currentState,
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'current_state': currentState,
    };
    await amplitude
        .track(BaseEvent('app launched', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'app_launched',
      parameters: properties,
    );
  }

  /// Track when user shares a product
  Future<void> logProductShared({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String productSkuId,
    required String productName,
    required String mrp,
    required String sellingPrice,
    required String discount,
    required Map<String, dynamic> allAttributes,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'product_sku_id': productSkuId,
      'product_name': productName,
      'mrp': mrp,
      'selling_price': sellingPrice,
      'discount': discount,
      ...allAttributes, // Add all product attributes
    };
    await amplitude
        .track(BaseEvent('product shared', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'product_shared',
      parameters: properties,
    );
  }

  /// Track when user closes the app
  Future<void> logAppClosed({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String currentState,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'current_state': currentState,
    };
    await amplitude.track(BaseEvent('app closed', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'app_closed',
      parameters: properties,
    );
  }

  // Track when user clicks on Address CTA on Homepage
  Future<void> logHomepageAddressBarClicked({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String homepageSection,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'homepage_section': homepageSection,
    };
    await amplitude.track(
        BaseEvent('homepage address bar clicked', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'homepage_address_bar_clicked',
      parameters: properties,
    );
  }

  // Track when user edits address
  Future<void> logAddressEdited({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String savedAddressAs,
    required String selectLocation,
    required String block,
    required String gramPanchayat,
    required String villagePurwa,
    required String district,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'saved_address_as': savedAddressAs,
      'select_location': selectLocation,
      'block': block,
      'gram_panchayat': gramPanchayat,
      'village_purwa': villagePurwa,
      'district': district,
    };
    await amplitude
        .track(BaseEvent('address edited', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'address_edited',
      parameters: properties,
    );
  }

  // Track when user opens Google Map
  Future<void> logAddressMapLandingPage({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String savedAddressAs,
    required String selectLocation,
    required String block,
    required String gramPanchayat,
    required String villagePurwa,
    required String district,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'saved_address_as': savedAddressAs,
      'select_location': selectLocation,
      'block': block,
      'gram_panchayat': gramPanchayat,
      'village_purwa': villagePurwa,
      'district': district,
    };
    await amplitude.track(
        BaseEvent('address map landing page', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'address_map_landing_page',
      parameters: properties,
    );
  }

  // Track when user makes changes to Google Map
  Future<void> logAddressMappedLocationSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String pinDropped,
    required String pinDroppedYesNo,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'pin_dropped': pinDropped,
      'pin_dropped_yes_no': pinDroppedYesNo,
    };
    await amplitude.track(BaseEvent('address mapped location selected',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'address_mapped_location_selected',
      parameters: properties,
    );
  }

  // Track when user selects search box
  Future<void> logUserSearchBoxSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String screenName,
    required String searchText,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'screen_name': screenName,
      'search_text': searchText,
    };

    debugPrint(
        'AnalyticsService: logUserSearchBoxSelected - mobileNumber: $mobileNumber');
    debugPrint(
        'AnalyticsService: logUserSearchBoxSelected - phone_number property: ${properties['phone_number']}');
    await amplitude.track(
        BaseEvent('user search box selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'user_search_box_selected',
      parameters: properties,
    );
  }

  // Track when user saves an address
  Future<void> logAddressSaved({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String savedAddressAs,
    required String selectLocation,
    required String block,
    required String gramPanchayat,
    required String villagePurwa,
    required String district,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'saved_address_as': savedAddressAs,
      'select_location': selectLocation,
      'block': block,
      'gram_panchayat': gramPanchayat,
      'village_purwa': villagePurwa,
      'district': district,
    };
    await amplitude
        .track(BaseEvent('address saved', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'address_saved',
      parameters: properties,
    );
  }

  // Track when user lands on Buy Again page
  Future<void> logBuyAgainPageLanded({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
  }) async {
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
    };
    await amplitude.track(BaseEvent('buy again landing page opened',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'buy_again_landing_page_opened',
      parameters: properties,
    );
  }

  // Track when user lands on Order History page
  Future<void> logOrderHistoryPageLanded({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
  }) async {
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
    };
    await amplitude.track(
        BaseEvent('order history page landed', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'order_history_page_landed',
      parameters: properties,
    );
  }

  // Track when user selects a product from "Your previous orders" on Homepage
  Future<void> logHomepagePreviousOrderProductSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String homepageSection,
    required String tileName,
    required String label,
    String source = 'Home_page',
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'homepage_section': homepageSection,
      'tile_name': tileName,
      'label': label,
      'source': source,
    };

    await amplitude.track(BaseEvent(
      'your previous order homepage product selected',
      eventProperties: properties,
    ));
    await _firebaseAnalytics.logEvent(
      name: 'your_previous_order_homepage_product_selected',
      parameters: properties,
    );
  }

  // Track onboarding OTP failure when user enters incorrect OTP
  Future<void> logOTPFailureIncorrect({
    String? mobileNumber,
    required String errorType,
    required bool retryAttempted,
    required int attemptNumber,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');
    if (ipAddress != null && ipAddress.isNotEmpty) {
      baseProperties['ip_address'] = ipAddress;
    } else {
      baseProperties['ip_address'] = 'unknown';
    }

    if (latitude != null && longitude != null) {
      baseProperties['lat_long'] =
          '${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}';
    } else {
      baseProperties['lat_long'] = 'unknown';
    }

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'error_type': errorType,
      'retry_attempted': retryAttempted,
      'attempt_number': attemptNumber,
    };

    await amplitude
        .track(BaseEvent('Otp Failure Incorrect', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'otp_failure_incorrect',
      parameters: properties,
    );
  }

  // Track onboarding OTP success when user enters correct OTP
  Future<void> logOTPSuccess({
    String? mobileNumber,
    required String authMethod,
    required int signupFlowDuration,
    required String userType,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'auth_method': authMethod,
      'signup_flow_duration': signupFlowDuration,
      'user_type': userType,
    };

    await amplitude
        .track(BaseEvent('Otp Success', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'otp_success',
      parameters: properties,
    );
  }

  // Track onboarding OTP incorrect when user enters wrong OTP
  Future<void> logOTPIncorrect({
    String? mobileNumber,
    required String authMethod,
    required int signupFlowDuration,
    required String userType,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'auth_method': authMethod,
      'signup_flow_duration': signupFlowDuration,
      'user_type': userType,
    };

    await amplitude
        .track(BaseEvent('Otp Incorrect', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'otp_incorrect',
      parameters: properties,
    );
  }

  // Track when user selects "Take me to Home" CTA
  Future<void> logTakeMeToHomeCTA({
    String? mobileNumber,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    baseProperties.remove('geolocation');
    baseProperties.remove('duration_text');

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
    };

    await amplitude
        .track(BaseEvent('Take Me To Home', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'take_me_to_home_cta',
      parameters: properties,
    );
  }

  // Track when user selects language during onboarding
  Future<void> logLanguageSelected({
    String? mobileNumber,
    required String languageSelected,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'language_selected': languageSelected,
    };

    await amplitude
        .track(BaseEvent('language_selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'language_selected',
      parameters: properties,
    );
  }

  // Set user ID for Amplitude and Firebase Analytics
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) {
      return;
    }
    try {
      await amplitude.setUserId(userId);
      await _firebaseAnalytics.setUserId(id: userId);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting user ID: $e');
      }
    }
  }

  Future<void> clearUserId() async {
    if (!_isInitialized) {
      return;
    }
    try {
      await amplitude.setUserId(null);
      await _firebaseAnalytics.setUserId(id: null);
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing user ID: $e');
      }
    }
  }

  // Track when user selects a product from "Frequently bought" section on homepage
  Future<void> logHomepageFrequentlyBoughtProductSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String categoryName,
    required String categoryPath,
    String? subCategoryName,
    String? subSubCategoryName,
    String? productSkuId,
    String? productName,
    String screenName = 'home_page',
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'category_name': categoryName,
      'category_path': categoryPath,
      'sub_category_name': subCategoryName ?? '',
      'sub_sub_category_name': subSubCategoryName ?? '',
      'screen_name': screenName,
      if (productSkuId != null) 'product_sku_id': productSkuId,
      if (productName != null) 'product_name': productName,
    };

    await amplitude.track(BaseEvent(
        'homepage_frequently_bought_product_selected',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'homepage_frequently_bought_product_selected',
      parameters: properties,
    );
  }

  // Track when user clicks CTA on "Frequently bought" section on homepage
  Future<void> logHomepageFrequentlyBoughtCTA({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    String? subCategoryName,
    required String categoryName,
    required String categoryPath,
    String screenName = 'home_page',
    String? productSkuId,
    String? productName,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'sub_category_name': subCategoryName ?? '',
      'category_name': categoryName,
      'category_path': categoryPath,
      'screen_name': screenName,
      if (productSkuId != null) 'product_sku_id': productSkuId,
      if (productName != null) 'product_name': productName,
    };

    await amplitude.track(BaseEvent('cta_on_frequently_bought_homepage',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'cta_on_frequently_bought_homepage',
      parameters: properties,
    );
  }

  // Track when user clicks "Track Order" from Order List
  Future<void> logOrderListTrackOrderClicked({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String orderId,
    String? utmSource,
    String? utmMedium,
    String? utmCampaign,
    String? referrerId,
    String? installSource,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'order_id': orderId,
      'utm_source': utmSource ?? 'unknown',
      'utm_medium': utmMedium ?? 'unknown',
      'utm_campaign': utmCampaign ?? 'unknown',
      'referrer_id': referrerId ?? 'unknown',
      'install_source': installSource ?? 'unknown',
    };

    await amplitude.track(BaseEvent('Order List Track Order Clicked',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'order_list_track_order_clicked',
      parameters: properties,
    );
  }

  // Track when user clicks "Order Again" from Order List
  Future<void> logOrderListOrderAgainClicked({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String orderId,
    String? utmSource,
    String? utmMedium,
    String? utmCampaign,
    String? referrerId,
    String? installSource,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'order_id': orderId,
      'utm_source': utmSource ?? 'unknown',
      'utm_medium': utmMedium ?? 'unknown',
      'utm_campaign': utmCampaign ?? 'unknown',
      'referrer_id': referrerId ?? 'unknown',
      'install_source': installSource ?? 'unknown',
    };

    await amplitude.track(BaseEvent('Order List Order Again Clicked',
        eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'order_list_order_again_clicked',
      parameters: properties,
    );
  }

  // Track when user enters search text
  Future<void> logUserSearchTextEntered({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required String searchText,
    required String screenName,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }
    final baseProperties = await _getBaseProperties();
    if (ipAddress != null && ipAddress.isNotEmpty) {
      baseProperties['ip_address'] = ipAddress;
    } else {
      baseProperties['ip_address'] = 'unknown';
    }

    if (latitude != null && longitude != null) {
      baseProperties['lat_long'] =
          '${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}';
    } else {
      baseProperties['lat_long'] = 'unknown';
    }

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'search_text': searchText,
      'screen_name': screenName,
    };

    await amplitude.track(
        BaseEvent('user_search_text_entered', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'user_search_text_entered',
      parameters: properties,
    );
  }

  // Track when order is successfully paid (Order Success Screen)
  Future<void> logOrderSuccessScreen({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required String paymentMethodSelected,
    required String paymentStatus,
    required String orderStatus,
    required String orderId,
    String? orderFailReason,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();
    if (ipAddress != null && ipAddress.isNotEmpty) {
      baseProperties['ip_address'] = ipAddress;
    } else {
      baseProperties['ip_address'] = 'unknown';
    }

    if (latitude != null && longitude != null) {
      baseProperties['lat_long'] =
          '${latitude.toStringAsFixed(6)},${longitude.toStringAsFixed(6)}';
    } else {
      baseProperties['lat_long'] = 'unknown';
    }

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'payment_method_selected': paymentMethodSelected,
      'payment_status': paymentStatus,
      'order_status': orderStatus,
      'order_id': orderId,
      if (orderFailReason != null) 'order_fail_reason': orderFailReason,
    };

    await amplitude
        .track(BaseEvent('order_success_screen', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'order_success_screen',
      parameters: properties,
    );
  }

  // Track when user selects to cancel order
  Future<void> logOrderCancelSelected({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required String paymentMethodSelected,
    required String paymentStatus,
    required String orderStatus,
    required String orderId,
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();
    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'payment_method_selected': paymentMethodSelected,
      'payment_status': paymentStatus,
      'order_status': orderStatus,
      'order_id': orderId,
    };

    await amplitude
        .track(BaseEvent('order_cancel_selected', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'order_cancel_selected',
      parameters: properties,
    );
  }

  // Track when order cancellation is successful
  Future<void> logOrderCancelSuccessful({
    String? mobileNumber,
    required String geolocation,
    required String deliverySLA,
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required int deliverySlots,
    required String paymentMethodSelected,
    required String paymentStatus,
    required String orderStatus,
    required String orderId,
    required String cancellationReason,
    String? deliveryETA,
    String? surchargeTag,
  }) async {
    if (!_isInitialized) {
      return;
    }

    final baseProperties = await _getBaseProperties();

    final properties = <String, Object>{
      ...baseProperties,
      'phone_number': mobileNumber ?? '',
      'geolocation': geolocation,
      'delivery_sla': deliverySLA,
      'number_of_products': numberOfProducts,
      'items_total': itemsTotal,
      'delivery_charge': deliveryCharge,
      'tax': tax,
      'discount': discount,
      'grand_total': grandTotal,
      'free_products_added': freeProductsAdded,
      'delivery_slots': deliverySlots,
      'payment_method_selected': paymentMethodSelected,
      'payment_status': paymentStatus,
      'order_status': orderStatus,
      'order_id': orderId,
      'cancellation_reason': cancellationReason,
      if (deliveryETA != null) 'delivery_eta': deliveryETA,
      if (surchargeTag != null) 'surcharge_tag': surchargeTag,
    };

    await amplitude.track(
        BaseEvent('order_cancel_successful', eventProperties: properties));
    await _firebaseAnalytics.logEvent(
      name: 'order_cancel_successful',
      parameters: properties,
    );
  }

  Amplitude get amplitudeInstance => amplitude;
  FirebaseAnalytics get firebaseAnalytics => _firebaseAnalytics;
}
