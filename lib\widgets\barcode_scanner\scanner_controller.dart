import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'barcode_scanner_widget.dart';

/// A controller class that provides a simple interface for using the barcode scanner widget.
/// This allows for easy integration of the scanner into any part of the app.
class ScannerController {
  static Future<String?> scan({
    required BuildContext context,
    String? title,
    String? description,
    Color? overlayColor,
    Color? borderColor,
    Color? successColor,
  }) async {
    final completer = Completer<String?>();

    if (Platform.isIOS) {
      await _showScanner(context, completer, title, description, overlayColor,
          borderColor, successColor);
    } else {
      final status = await Permission.camera.status;
      if (status.isGranted) {
        await _showScanner(context, completer, title, description, overlayColor,
            borderColor, successColor);
      } else {
        final result = await Permission.camera.request();
        if (result.isGranted) {
          await _showScanner(context, completer, title, description,
              overlayColor, borderColor, successColor);
        } else {
          await _showPermissionDeniedDialog(context);
          completer.complete(null);
        }
      }
    }

    return completer.future;
  }

  /// Shows the barcode scanner widget
  static Future<void> _showScanner(
    BuildContext context,
    Completer<String?> completer,
    String? title,
    String? description,
    Color? overlayColor,
    Color? borderColor,
    Color? successColor,
  ) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) => BarcodeScannerWidget(
          title: title,
          description: description,
          overlayColor: overlayColor,
          borderColor: borderColor,
          successColor: successColor,
          onBarcodeDetected: (value) {
            // Complete the future with the scanned value
            if (!completer.isCompleted) {
              completer.complete(value);

              // Close the scanner after a short delay
              Future.delayed(const Duration(milliseconds: 500), () {
                Navigator.of(context).pop();
              });
            }
          },
        ),
      ),
    );

    // If the scanner was closed without scanning, complete with null
    if (!completer.isCompleted) {
      completer.complete(null);
    }
  }

  /// Shows a dialog when camera permission is denied
  static Future<void> _showPermissionDeniedDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera Permission Required'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('Camera permission is required to scan barcodes.'),
                Text('\nPlease enable camera access in app settings.'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }
}
