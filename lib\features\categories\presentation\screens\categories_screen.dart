import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/services/remote_config_service.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/features/location/presentation/widgets/location_state_handler.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/utils/app_dimensions.dart';
import '../../../../core/utils/color_utils.dart';
import '../../../home/<USER>/screens/dashboard_screen.dart';
import '../../../home/<USER>/widgets/animated_search_placeholder.dart';
import '../../../home/<USER>/widgets/custom_search_appbar.dart';
import '../../bloc/categories_bloc.dart';
import '../widgets/subcategories_section.dart';

// ignore: must_be_immutable
class CategoriesScreen extends StatelessWidget {
  CategoriesScreen({super.key, this.scrollItem});

  final CategoryEntity? scrollItem;

  Map<String, dynamic> themeSettings = {
    "background_color": "#FFFFFF",
    "topbar_color": "#FFFFFF",
    "icon_primary_color": "#242424",
    "icon_secondary_color": "#6D6D6D",
    "border_color": "#F0F0F6",
    "highlight_color": {
      "Beverages": "#E7F4FE",
      "Apparel": "#EDF7EE",
      "Personal Care": "#FFF9E5",
    }
  };

  static Color iconPrimaryColor = AppColors.neutral100;
  static Color iconSecondaryColor = AppColors.neutral300;

  @override
  Widget build(BuildContext context) {
    themeSettings =
        ((RemoteConfigService().getThemeConfig['category_screen']?.isNotEmpty ??
                    false) &&
                RemoteConfigService().getThemeConfig['category_screen']
                        ["border_color"] !=
                    null)
            ? RemoteConfigService().getThemeConfig['category_screen']
            : themeSettings;
    final ItemScrollController itemScrollController = ItemScrollController();
    final ScrollOffsetController scrollOffsetController =
        ScrollOffsetController();
    final ItemPositionsListener itemPositionsListener =
        ItemPositionsListener.create();
    final ScrollOffsetListener scrollOffsetListener =
        ScrollOffsetListener.create();
    return Scaffold(
      backgroundColor: Colors.white,
      body: LocationStateHandler(
        child: BlocBuilder<CategoriesBloc, CategoriesState>(
          builder: (context, state) {
            return state.map(initial: (_) {
              return const Center(child: CircularProgressIndicator());
            }, loaded: (value) {
              if (scrollItem != null &&
                  (value.categories?.isNotEmpty ?? false)) {
                CategoryEntity item = (value.categories ?? []).firstWhere(
                    (e) => ((e.collectionId == scrollItem?.collectionId) ||
                        (e.name == scrollItem?.name) ||
                        (e.id == scrollItem?.id)),
                    orElse: () => value.categories!.first);

                int index = (value.categories ?? []).indexOf(item);

                if (index > 1) {
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    itemScrollController.scrollTo(
                      index: index,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOutCubic,
                      alignment: 0.06,
                    );
                  });
                }
              }

              return LayoutBuilder(builder: (context, constraints) {
                return SafeArea(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      context
                          .read<CategoriesBloc>()
                          .add(CategoriesEvent.fetchCategories());
                    },
                    child: (value.categories?.isEmpty ?? false)
                        ? const Center(child: Text('No categories found'))
                        : NestedScrollView(
                            floatHeaderSlivers: true,
                            headerSliverBuilder: (BuildContext context,
                                bool innerBoxIsScrolled) {
                              return <Widget>[
                                SliverAppBar(
                                  scrolledUnderElevation: 0,
                                  leadingWidth: 0,
                                  titleSpacing: 0,
                                  collapsedHeight: 60,
                                  toolbarHeight: 60,
                                  expandedHeight: 60,
                                  floating: true,
                                  pinned:
                                      true, // Use pinned: true to keep the app bar visible
                                  stretch: false,
                                  snap: true,
                                  leading: const SizedBox(),
                                  backgroundColor: ColorUtils.hexToColor(
                                          themeSettings['topbar_color']) ??
                                      AppColors.neutral100,
                                  title: CustomSliverAppBarContent(
                                    iconPrimaryColor: ColorUtils.hexToColor(
                                            themeSettings[
                                                'icon_primary_color']) ??
                                        iconPrimaryColor,
                                    iconSecondaryColor: ColorUtils.hexToColor(
                                            themeSettings[
                                                'icon_secondary_color']) ??
                                        iconSecondaryColor,
                                  ),
                                ),
                                SliverPersistentHeader(
                                  pinned: true,
                                  delegate: SliverAppBarDelegate(
                                    minHeight: (constraints.maxWidth /
                                        (constraints.maxWidth * 0.014)),
                                    maxHeight: (constraints.maxWidth /
                                        (constraints.maxWidth * 0.014)),
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color: ColorUtils.hexToColor(
                                                  themeSettings[
                                                      'topbar_color']) ??
                                              AppColors.neutral100,
                                          border: Border(
                                            bottom: BorderSide(
                                              color: ColorUtils.hexToColor(
                                                      themeSettings[
                                                              'border_color'] ??
                                                          '') ??
                                                  AppColors.neutral600,
                                            ),
                                          )),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              context.push(
                                                  '${RouteNames.search}?initialQuery=');
                                            },
                                            child: Container(
                                              margin: EdgeInsets.symmetric(
                                                  vertical: 10,
                                                  horizontal: AppDimensions
                                                      .screenHzPadding),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 6,
                                                      vertical: 6),
                                              decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  border: Border.all(
                                                    color: AppColors.primary100,
                                                    width: 0.5,
                                                  )),
                                              child: Row(
                                                children: [
                                                  SizedBox.square(
                                                    dimension: 36,
                                                    child: Center(
                                                      child: Image.asset(
                                                        'assets/new/icons/search.png',
                                                        width: 24,
                                                        height: 24,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  Expanded(
                                                    child: SizedBox(
                                                      height: 36,
                                                      child: Row(
                                                        children: [
                                                          CustomText(
                                                            'Search ',
                                                            color: AppColors
                                                                .primary300,
                                                            fontSize: 16,
                                                            maxLines: 1,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            textHeight: 0.9,
                                                          ),
                                                          AnimatedSearchPlaceholder(),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ];
                            },
                            body: ScrollablePositionedList.builder(
                              padding: const EdgeInsets.only(bottom: 150),
                              itemCount: value.categories?.length ?? 0,
                              itemBuilder: (context, index) {
                                CategoryEntity? category =
                                    value.categories?[index];
                                return CategorySectionWidget(
                                  category: category,
                                  index: index,
                                  themeConfig: themeSettings,
                                );
                              },
                              itemScrollController: itemScrollController,
                              scrollOffsetController: scrollOffsetController,
                              itemPositionsListener: itemPositionsListener,
                              scrollOffsetListener: scrollOffsetListener,
                            ),
                          ),
                  ),
                );
              });
            }, error: (value) {
              return Center(
                child: CustomText(value.message),
              );
            });
          },
        ),
      ),
    );
  }
}

class CategorySectionWidget extends StatelessWidget {
  const CategorySectionWidget({
    super.key,
    this.category,
    required this.index,
    required this.themeConfig,
  });
  final CategoryEntity? category;
  final int index;
  final Map<String, dynamic> themeConfig;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.only(top: 12, bottom: 6),
          decoration: BoxDecoration(
            color: ColorUtils.hexToColor(
                themeConfig['highlight_color']?[category?.name] ?? ''),
          ),
          child: SubCategoriesSection(
            preloadData: true,
            parentCategory: category,
            gridChildAspectRatio: 0.62,
            level: 'sub_category',
            onSeeAllTap: () async {
              context.push(RouteNames.products, extra: {
                'category': category,
              });
              // Log category view event to AppsFlyer
              await AppsFlyerEvents.categoryView(
                category?.id ?? '',
                category?.name ?? '',
              );
            },
          ),
        ),
      ],
    );
  }
}
