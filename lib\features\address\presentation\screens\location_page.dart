import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../data/models/place_suggestion_model.dart';
import '../../../../widgets/custom_text.dart';
import '../../../../widgets/custom_textfield.dart';
import '../../bloc/address_bloc.dart';
import 'address_form_view.dart';
import 'address_list_view.dart';
import 'address_search_view.dart';
import 'location_map_view.dart';

// ignore: must_be_immutable
class LocationPage extends StatelessWidget {
  const LocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        getIt<AddressBloc>().state.maybeMap(
          orElse: () {
            // ❌ Don't pop blindly here
            if (!didPop && context.mounted && context.canPop()) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (context.mounted && context.canPop()) {
                  context.pop();
                }
              });
            }
          },
          addressForm: (value) {
            if (value.isEdit) {
              getIt<AddressBloc>().add(AddressEvent.fetchAddresses());
            } else {
              getIt<AddressBloc>().add(AddressEvent.initMap());
            }
          },
          mapSelection: (_) {
            if (getIt<AppBloc>().isAuthenticated) {
              getIt<AddressBloc>().add(AddressEvent.fetchAddresses());
            } else {
              if (!didPop && context.mounted && context.canPop()) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (context.mounted && context.canPop()) {
                    context.pop();
                  }
                });
              }
            }
          },
          searchScreen: (_) {
            getIt<AddressBloc>().add(AddressEvent.initMap());
          },
        );
      },
      child: SafeArea(
        top: false,
        child: BlocBuilder<AddressBloc, AddressState>(
          buildWhen: (previous, current) =>
              (previous.runtimeType != current.runtimeType) &&
              ((previous.maybeMap(
                    orElse: () => false,
                    searchScreen: (_) => true,
                  )) ||
                  (current.maybeMap(
                    orElse: () => false,
                    searchScreen: (_) => true,
                  ))),
          builder: (context, currentState) {
            return Scaffold(
              backgroundColor: AppColors.neutral150,
              appBar: AppBar(
                scrolledUnderElevation: 0,
                backgroundColor: AppColors.neutral100,
                elevation: 0,
                centerTitle: false,
                titleSpacing: 0,
                leadingWidth: 0,
                toolbarHeight: currentState.maybeMap(
                  orElse: () => 60,
                  searchScreen: (_) => 75,
                ),
                automaticallyImplyLeading: false,
                title: BlocBuilder<AddressBloc, AddressState>(
                  builder: (context, state) {
                    return state.maybeMap(
                      orElse: () => Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          InkWell(
                            onTap: () {
                              getIt<AddressBloc>().state.maybeMap(
                                orElse: () {
                                  context.pop();
                                },
                                addressForm: (value) {
                                  if (value.isEdit) {
                                    getIt<AddressBloc>()
                                        .add(AddressEvent.fetchAddresses());
                                  } else {
                                    getIt<AddressBloc>()
                                        .add(AddressEvent.initMap());
                                  }
                                },
                                mapSelection: (_) {
                                  if (getIt<AppBloc>().isAuthenticated) {
                                    getIt<AddressBloc>()
                                        .add(AddressEvent.fetchAddresses());
                                  } else {
                                    context.pop();
                                  }
                                },
                                searchScreen: (_) {
                                  getIt<AddressBloc>()
                                      .add(AddressEvent.initMap());
                                },
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                              child: Image.asset(
                                'assets/new/icons/chevron_left.png',
                                height: 26,
                                width: 26,
                                color: AppColors.primary600,
                              ),
                            ),
                          ),
                          SizedBox(width: 12),
                          CustomText(
                            state.map(
                                initial: (_) => 'Fetching address...',
                                loading: (_) => 'Fetching address...',
                                addressForm: (_) => 'Add more address details',
                                addressList: (_) => 'Saved addresses',
                                error: (_) => 'Select address',
                                searchScreen: (_) => '',
                                mapSelection: (_) =>
                                    'Confirm map pin location'), // Fallback while initial
                            color: AppColors.primary700,
                            fontSize: 20,
                            fontWeight: FontWeight.w800,
                          ),
                        ],
                      ),
                      searchScreen: (value) => Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 10),
                        child: SearchTextField(
                          controller: AddressBloc.searchController,
                          hintText: 'Search for area, street name...',
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[a-zA-Z0-9\s]')),
                          ],
                          autofocus: true,
                          onChanged: (query) {
                            getIt<AddressBloc>()
                                .add(AddressEvent.searchPlaces(query));
                          },
                          onClear: () {
                            AddressBloc.searchController?.clear();
                            // Dispatch an empty query to clear results
                            getIt<AddressBloc>()
                                .add(const AddressEvent.searchPlaces(''));
                          },
                          // onTap: () {
                          //   // getIt<AddressBloc>().add(const AddressEvent.initSearch());
                          // },
                          onBackPressed: () {
                            AddressBloc.searchController?.clear();
                            getIt<AddressBloc>().add(
                              const AddressEvent.initMap(),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              body: BlocConsumer<AddressBloc, AddressState>(
                listener: (context, state) {
                  state.mapOrNull(
                    error: (value) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(value.message)),
                      );
                    },
                  );
                },
                buildWhen: (previous, current) =>
                    (current.runtimeType != previous.runtimeType) ||
                    (current.maybeMap(
                        orElse: () => false, addressList: (_) => true)) ||
                    (current.maybeMap(
                        orElse: () => false, searchScreen: (_) => true)),
                builder: (context, state) {
                  return state.map(
                    initial: (_) =>
                        const Center(child: CircularProgressIndicator()),
                    loading: (_) =>
                        const Center(child: CircularProgressIndicator()),
                    mapSelection: (value) {
                      AddressModel? temporaryAddress = value.temporaryAddress;

                      return LocationMapView(
                        initialAddress: temporaryAddress,
                      );
                    },
                    searchScreen: (value) {
                      bool isSearching = value.isSearching;
                      List<PlaceSuggestionModel>? searchResults =
                          value.searchResults;
                      if (isSearching) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      return AddressSearchView(
                        suggestions: searchResults ?? [],
                      );
                    },
                    addressForm: (value) => AddressFormView(
                      address: value.temporaryAddress,
                      isEdit: value.isEdit,
                    ),
                    addressList: (value) {
                      bool isLoading = value.isLoading;
                      bool hasError = value.hasError;
                      List<AddressModel> addresses = value.addresses ?? [];
                      AddressModel? selectedAddress = value.selectedAddress;
                      bool applyFilter = value.applyFilter;

                      if (isLoading) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      if (addresses.isEmpty && !hasError) {
                        return _buildEmptyAddressState(context);
                      }
                      return AddressListView(
                        addresses: addresses,
                        selectedAddress: selectedAddress,
                        filterServiceStatus: applyFilter,
                      );
                    },
                    error: (value) => _buildErrorState(context, value.message),
                  );
                },
              ),
              bottomSheet: BlocBuilder<AddressBloc, AddressState>(
                builder: (context, state) {
                  return state.maybeMap(
                    orElse: () => SizedBox(),
                    addressList: (value) {
                      return InkWell(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          getIt<AddressBloc>().add(AddressEvent.initMap());
                        },
                        child: Container(
                            color: AppColors.neutral100,
                            padding: const EdgeInsets.fromLTRB(16, 20, 16, 20),
                            child: Container(
                              height: 48.sp,
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 5),
                              decoration: BoxDecoration(
                                color: AppColors.neutral100,
                                border: Border.all(
                                    color: AppColors.primary500, width: 1.5),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Image.asset(
                                    'assets/new/icons/add.png',
                                    color: AppColors.primary500,
                                    width: 24,
                                    height: 24,
                                  ),
                                  SizedBox(width: 10),
                                  CustomText(
                                    'Add new address',
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.primary500,
                                  ),
                                ],
                              ),
                            )),
                      );
                    },
                    addressForm: (value) {
                      return InkWell(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          getIt<AddressBloc>().add(AddressEvent.saveAddress());
                        },
                        child: Container(
                            color: AppColors.neutral100,
                            padding: const EdgeInsets.fromLTRB(16, 20, 16, 20),
                            child: Container(
                              height: 48.sp,
                              width: double.infinity,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 5),
                              decoration: BoxDecoration(
                                color: AppColors.primary500,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: CustomText(
                                  'Save address',
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.neutral100,
                                ),
                              ),
                            )),
                      );
                    },
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyAddressState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 30),
        child: const CustomText(
          'No saved addresses found.',
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 50, color: Colors.red),
          const SizedBox(height: 16),
          CustomText(
            'Error: $message',
            fontSize: 18,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              BlocProvider.of<AddressBloc>(context)
                  .add(const AddressEvent.fetchAddresses());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
