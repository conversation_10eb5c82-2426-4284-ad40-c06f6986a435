export 'app_update_event.dart';
export 'app_update_state.dart';

import 'dart:async';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'app_update_event.dart';
import 'app_update_state.dart';
import '../../../../core/services/remote_config_service.dart';

class AppUpdateBloc extends Bloc<AppUpdateEvent, AppUpdateState> {
  AppUpdateBloc() : super(const AppUpdateState.initial()) {
    on<AppUpdateEvent>((event, emit) async {
      await event.map(
        checkForUpdate: (_) => _onCheckForUpdate(emit),
      );
    });
  }

  Future<void> _onCheckForUpdate(Emitter<AppUpdateState> emit) async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = int.parse(packageInfo.buildNumber);
      final updateInfo = await _fetchLatestVersionInfo();
      final versionCode = updateInfo['versionCode'] as int;
      final forceUpdate = updateInfo['forceUpdate'] as bool;
      final versionNumber = updateInfo['versionNumber'] as String?;
      final updateAvailable = _isVersionDifferent(currentVersion, versionCode);
      if (updateAvailable) {
        emit(AppUpdateState.updateAvailable(
            currentVersion: currentVersion,
            versionCode: versionCode,
            versionNumber: versionNumber,
            forceUpdate: forceUpdate));
      } else {
        emit(AppUpdateState.noUpdate(currentVersion: currentVersion));
      }
    } catch (e) {
      final packageInfo = await PackageInfo.fromPlatform();
      emit(AppUpdateState.noUpdate(
          currentVersion: int.parse(packageInfo.buildNumber)));
    }
  }

  Future<Map<String, dynamic>> _fetchLatestVersionInfo() async {
    final versionData = RemoteConfigService()
        .getAppVersionUpdate[Platform.isIOS ? "ios" : "android"];
    return {
      'versionCode': versionData['versionCode'],
      'forceUpdate': versionData['forceUpdate'],
      'versionNumber': versionData['versionNumber'],
    };
  }

  bool _isVersionDifferent(int currentVersion, int versionCode) {
    return currentVersion < versionCode;
  }
}
