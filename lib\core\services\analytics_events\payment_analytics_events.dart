import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/analytics_events/analytics_data_service.dart';
import 'package:rozana/core/services/analytics_events/analytics_service.dart';


class PaymentAnalyticsEvents {
  static final AnalyticsService _analyticsService = getIt<AnalyticsService>();
  static final AnalyticsDataService _analyticsDataService = getIt<AnalyticsDataService>();

  Future<Map<String, String?>> _getAnalyticsData() async {
    return await _analyticsDataService.getAnalyticsDataWithFallbacks();
  }

  /// Track Payment Method Selected
  Future<void> trackPaymentMethodSelected({
    required String methodId,
    required Map<String, dynamic> cartData,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logGoToPaymentMethodSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: cartData['items']?.length ?? 0,
          itemsTotal: cartData['totalAmount']?.toString() ?? '0',
          deliveryCharge: '0',
          tax: '0',
          discount: '0',
          grandTotal: cartData['totalAmount']?.toString() ?? '0',
          freeProductsAdded: 0,
          deliverySlots: 0,
          paymentMethodSelected: _getPaymentMethodDisplayName(methodId),
          walletBalance: '0',
        );
      }
    } catch (e) {
      debugPrint('Error tracking payment method selected: $e');
    }
  }

  /// Track Go To Payment Change Address Selected
  Future<void> trackGoToPaymentChangeAddressSelected({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required String currentAddress,
    String? ipAddress,
    double? latitude,
    double? longitude,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logGoToPaymentChangeAddressSelected(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          freeProductsAdded: freeProductsAdded,
          currentAddress: currentAddress,
          ipAddress: ipAddress,
          latitude: latitude,
          longitude: longitude,
        );
      }
    } catch (e) {
      debugPrint('Error tracking go to payment change address selected: $e');
    }
  }

  /// Track Go To Payment Button Clicked
  Future<void> trackGoToPaymentButtonClicked({
    required int numberOfProducts,
    required String itemsTotal,
    required String deliveryCharge,
    required String tax,
    required String discount,
    required String grandTotal,
    required int freeProductsAdded,
    required int deliverySlots,
    required String paymentMethodSelected,
    required String walletBalance,
  }) async {
    try {
      if (_analyticsService.isInitialized) {
        final analyticsData = await _getAnalyticsData();
        await _analyticsService.logGoToPaymentButtonClicked(
          mobileNumber: analyticsData['mobileNumber'],
          geolocation: analyticsData['geolocation'] ?? 'unknown',
          deliverySLA: analyticsData['durationText'] ?? 'unknown',
          numberOfProducts: numberOfProducts,
          itemsTotal: itemsTotal,
          deliveryCharge: deliveryCharge,
          tax: tax,
          discount: discount,
          grandTotal: grandTotal,
          freeProductsAdded: freeProductsAdded,
          deliverySlots: deliverySlots,
          paymentMethodSelected: paymentMethodSelected,
          walletBalance: walletBalance,
        );
      }
    } catch (e) {
      debugPrint('Error tracking go to payment button clicked: $e');
    }
  }


  String _getPaymentMethodDisplayName(String paymentMethod) {
    switch (paymentMethod) {
      case 'cod':
        return 'Cash on Delivery';
      case 'razorpay':
        return 'Online Payment';
      case 'wallet':
        return 'Wallet';
      default:
        return paymentMethod;
    }
  }

}
