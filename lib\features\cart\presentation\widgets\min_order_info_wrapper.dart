import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:rozana/widgets/custom_text.dart';

/// A wrapper widget that conditionally shows the MinOrderInfoBar based on cart state
/// and adds appropriate padding to its child widget
class MinOrderWrapper extends StatelessWidget {
  final Widget child;
  final bool excludeMinOrderInfo;

  const MinOrderWrapper(
      {super.key, required this.child, this.excludeMinOrderInfo = false});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        return BlocBuilder<AddressBloc, AddressState>(
          builder: (context, addressState) {
            final total = cartState.cart.total ?? 0;
            final minPurchase =
                getIt<TypesenseService>().minPurchaseAmount ?? 0;
            final isServicable = addressState.isServicable;

            bool isMinOrderReached = total >= minPurchase ||
                minPurchase == 0 ||
                !isServicable ||
                excludeMinOrderInfo;

            // Otherwise stack child + bottom info bar with padding
            return SafeArea(
              top: false,
              child: Stack(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(bottom: isMinOrderReached ? 0 : 40.0),
                    child: child,
                  ),
                  Visibility(
                    visible: !isMinOrderReached,
                    child: Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Material(
                        color: Colors.transparent,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    AppColors.neutral800.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: const Offset(0, -2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 12,
                                backgroundColor: AppColors.yellow200,
                                child: Icon(
                                  Icons.lock,
                                  size: 16,
                                  color: AppColors.primary500,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: CustomText(
                                  cartState.cart.totalItems == 0
                                      ? "Shop for ₹$minPurchase to Place Order"
                                      : "Shop for ₹${(minPurchase - ((cartState.cart.total) ?? 0)).toStringAsFixed(2)} more to Place Order",
                                  fontSize: 14,
                                  color: AppColors.primary500,
                                  fontWeight: FontWeight.w500,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
